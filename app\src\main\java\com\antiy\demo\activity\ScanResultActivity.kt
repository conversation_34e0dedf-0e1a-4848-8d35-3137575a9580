package com.antiy.demo.activity

import android.os.Bundle
import android.view.LayoutInflater
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.core.view.WindowCompat
import androidx.recyclerview.widget.LinearLayoutManager
import com.antiy.avlsdk.entity.RiskLevel
import com.antiy.demo.R
import com.antiy.demo.adapter.ThreatDetailAdapter
import com.antiy.demo.base.BaseActivity
import com.antiy.demo.databinding.ActivityScanResultBinding
import com.antiy.demo.model.ScanThreatData
import com.antiy.demo.model.ThreatDetail
import com.google.android.material.appbar.AppBarLayout

class ScanResultActivity : BaseActivity<ActivityScanResultBinding>() {

    companion object {
        const val EXTRA_HIGH_RISK_COUNT = "high_risk_count"
        const val EXTRA_MEDIUM_RISK_COUNT = "medium_risk_count"
        const val EXTRA_LOW_RISK_COUNT = "low_risk_count"
        const val EXTRA_SCAN_THREATS = "scan_threats"
    }

    private lateinit var threatAdapter: ThreatDetailAdapter
    private val threatList = mutableListOf<ThreatDetail>()

    override fun getViewBinding(inflater: LayoutInflater): ActivityScanResultBinding {
        return ActivityScanResultBinding.inflate(inflater)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        setupStatusBar()
        setupToolbar()
        setupAppBarBehavior()
        
        // 获取扫描结果数据
        val highRiskCount = intent.getIntExtra(EXTRA_HIGH_RISK_COUNT, 0)
        val mediumRiskCount = intent.getIntExtra(EXTRA_MEDIUM_RISK_COUNT, 0)
        val lowRiskCount = intent.getIntExtra(EXTRA_LOW_RISK_COUNT, 0)
        
        // 更新UI
        updateUI(highRiskCount, mediumRiskCount, lowRiskCount)
        
        // 获取实际的威胁数据
        val scanThreats = intent.getParcelableArrayListExtra<ScanThreatData>(EXTRA_SCAN_THREATS)
        if (!scanThreats.isNullOrEmpty()) {
            // 将扫描威胁数据转换为ThreatDetail对象
            convertAndAddThreats(scanThreats)
        }
        
        // 设置RecyclerView
        setupRecyclerView()
        
        // 设置一键处理按钮
        binding.btnFixAll.setOnClickListener {
            handleAllThreats()
        }
    }

    private fun setupStatusBar() {
        // 设置状态栏为透明
        window.statusBarColor = ContextCompat.getColor(this, android.R.color.transparent)
        
        // 设置内容延伸到状态栏
        WindowCompat.setDecorFitsSystemWindows(window, false)
        
        // 设置状态栏图标为深色
        WindowCompat.getInsetsController(window, window.decorView)?.apply {
            isAppearanceLightStatusBars = true
        }
    }

    private fun setupToolbar() {
        binding.btnBack.setOnClickListener { finish() }
    }

    private fun setupAppBarBehavior() {
        var lastLightStatus = true
        binding.appBarLayout.addOnOffsetChangedListener(
            AppBarLayout.OnOffsetChangedListener { appBarLayout, verticalOffset ->
                val scrollProgress = -verticalOffset / appBarLayout.totalScrollRange.toFloat()
                val shouldBeLightStatus = scrollProgress < 0.5
                
                // 只在状态发生变化时更新
                if (shouldBeLightStatus != lastLightStatus) {
                    lastLightStatus = shouldBeLightStatus
                    WindowCompat.getInsetsController(window, window.decorView)?.apply {
                        isAppearanceLightStatusBars = shouldBeLightStatus
                    }
                }
            }
        )
    }
    
    private fun updateUI(highRiskCount: Int, mediumRiskCount: Int, lowRiskCount: Int) {
        // 更新风险计数
        binding.tvHighRiskCount.text = highRiskCount.toString()
        binding.tvMediumRiskCount.text = mediumRiskCount.toString()
        binding.tvLowRiskCount.text = lowRiskCount.toString()
        
        // 更新扫描摘要
        val totalThreats = highRiskCount + mediumRiskCount + lowRiskCount
        binding.tvScanSummary.text = "扫描完成，发现 $totalThreats 个需要处理的问题"
    }
    
    private fun setupRecyclerView() {
        threatAdapter = ThreatDetailAdapter(
            threatList,
            onDeleteClick = { threat -> handleThreat(threat, "删除") },
            onIgnoreClick = { threat -> handleThreat(threat, "忽略") }
        )
        
        binding.rvThreatDetails.apply {
            layoutManager = LinearLayoutManager(this@ScanResultActivity)
            adapter = threatAdapter
        }
    }
    
    private fun convertAndAddThreats(scanThreats: List<ScanThreatData>) {
        scanThreats.forEach { threatData ->
            threatList.add(
                ThreatDetail(
                    name = threatData.threatType,
                    location = threatData.filePath,
                    description = threatData.description,
                    riskLevel = when (threatData.riskLevel) {
                        RiskLevel.HIGH -> ThreatDetail.RiskLevel.HIGH
                        RiskLevel.MEDIUM -> ThreatDetail.RiskLevel.MEDIUM
                        else -> ThreatDetail.RiskLevel.LOW
                    },
                    type = when {
                        threatData.threatType.contains("malware", true) -> ThreatDetail.ThreatType.MALWARE
                        threatData.threatType.contains("privacy", true) -> ThreatDetail.ThreatType.PRIVACY_RISK
                        else -> ThreatDetail.ThreatType.SUSPICIOUS_BEHAVIOR
                    }
                )
            )
        }
    }
    
    private fun handleThreat(threat: ThreatDetail, action: String) {
        // 在实际应用中，这里应该处理威胁
        Toast.makeText(this, "${action}了威胁: ${threat.name}", Toast.LENGTH_SHORT).show()
        
        // 从列表中移除威胁
        threatList.remove(threat)
        threatAdapter.notifyDataSetChanged()
        
        // 更新计数
        updateCounts()
    }
    
    private fun handleAllThreats() {
        // 在实际应用中，这里应该处理所有威胁
        Toast.makeText(this, "已处理所有威胁", Toast.LENGTH_SHORT).show()
        
        // 清空列表
        threatList.clear()
        threatAdapter.notifyDataSetChanged()
        
        // 更新计数
        updateCounts()
    }
    
    private fun updateCounts() {
        var highRiskCount = 0
        var mediumRiskCount = 0
        var lowRiskCount = 0
        
        // 计算各风险等级的威胁数量
        threatList.forEach { threat ->
            when (threat.riskLevel) {
                ThreatDetail.RiskLevel.HIGH -> highRiskCount++
                ThreatDetail.RiskLevel.MEDIUM -> mediumRiskCount++
                ThreatDetail.RiskLevel.LOW -> lowRiskCount++
            }
        }
        
        // 更新UI
        updateUI(highRiskCount, mediumRiskCount, lowRiskCount)
        
        // 如果没有威胁了，显示一个提示并返回
        if (threatList.isEmpty()) {
            Toast.makeText(this, "所有威胁已处理完毕", Toast.LENGTH_SHORT).show()
            // 延迟一会儿再返回，让用户看到提示
            binding.root.postDelayed({ finish() }, 1500)
        }
    }
} 