package com.antiy.demo

import android.content.Context
import android.util.Log
import com.antiy.avlsdk.callback.Logger
import java.io.BufferedWriter
import java.io.File
import java.io.FileWriter
import java.text.SimpleDateFormat
import java.util.Date

class ALog : Logger{

    companion object {
        private const val LOG_TAG = "AVL_SDK"
        private const val LOG_TO_CONSOLE = true // 是否打印到控制台
        private const val LOG_TO_FILE = true // 是否写入文件
        private const val LOG_DIR = "app_logs" // 日志文件夹名称
        private const val LOG_FILE_NAME = "app_log.txt" // 日志文件名
    }

    private var logFile: File? = null

    constructor(context : Context) {
        initLogFile(context)
    }

    /**
     * 初始化日志文件
     * @param context 应用上下文
     */
    private fun initLogFile(context: Context){
        if (LOG_TO_FILE) {
            val dir = File(context.filesDir, LOG_DIR)
            if (!dir.exists()) {
                dir.mkdirs()
            }
            logFile = File(dir, LOG_FILE_NAME)
        }
    }

    /**
     * 打印日志并写入文件
     * @param tag 标签
     * @param message 消息
     * @param level 日志级别
     */
    private fun log(tag: String, message: String, level: Int) {
        if (LOG_TO_CONSOLE) {
            when (level) {
                Log.VERBOSE -> Log.v(tag, message)
                Log.DEBUG -> Log.d(tag, message)
                Log.INFO -> Log.i(tag, message)
                Log.WARN -> Log.w(tag, message)
                Log.ERROR -> Log.e(tag, message)
                Log.ASSERT -> Log.wtf(tag, message)
            }
        }

        if (LOG_TO_FILE && logFile != null) {
            writeToFile(tag, message, level)
        }
    }

    /**
     * 将日志写入文件
     * @param tag 标签
     * @param message 消息
     * @param level 日志级别
     */
    private fun writeToFile(tag: String, message: String, level: Int) {
        BufferedWriter(FileWriter(logFile, true)).use { writer ->
            val logMessage = getLogMessage(tag, message, level)
            writer.write(logMessage)
            writer.newLine()
        }
    }

    /**
     * 获取格式化的日志消息
     * @param tag 标签
     * @param message 消息
     * @param level 日志级别
     * @return 格式化的日志消息
     */
    private fun getLogMessage(tag: String, message: String, level: Int): String {
        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS")
        val date = dateFormat.format(Date())
        val levelString = getLevelString(level)
        return "$date [$levelString] $tag: $message"
    }

    /**
     * 获取日志级别的字符串表示
     * @param level 日志级别
     * @return 日志级别的字符串
     */
    private fun getLevelString(level: Int): String {
        return when (level) {
            Log.VERBOSE -> "V"
            Log.DEBUG -> "D"
            Log.INFO -> "I"
            Log.WARN -> "W"
            Log.ERROR -> "E"
            Log.ASSERT -> "A"
            else -> "U"
        }
    }

    // 公共方法
    override fun verbose(msg: String?) {
       log(LOG_TAG,msg!!,Log.VERBOSE)
    }

    override fun info(msg: String?) {
        log(LOG_TAG,msg!!,Log.INFO)
    }

    override fun debug(msg: String?) {
        log(LOG_TAG,msg!!,Log.DEBUG)
    }

    override fun warn(msg: String?) {
        log(LOG_TAG,msg!!,Log.WARN)
    }

    override fun error(msg: String?) {
        log(LOG_TAG,msg!!,Log.ERROR)
    }

    override fun fatal(msg: String?) {
        log(LOG_TAG,msg!!,Log.ERROR)
    }
}