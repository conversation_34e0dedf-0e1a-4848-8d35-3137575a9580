package com.antiy.avlsdk.entity;

/**
 * 2 * Copyright (C), 2020-2024
 * 3 * FileName: ResultUpdate
 * 4 * Author: wang<PERSON>o
 * 5 * Date: 2024/9/2 10:23
 * 6 * Description: 更新结果实体
 * 10
 */
public class ResultUpdate {
    // 是否有更新
    public boolean hasUpdate;
    // 更新是否成功
    boolean updateSucceeded;

    public ResultUpdate() {
    }

    public ResultUpdate(boolean hasUpdate, boolean updateSucceeded) {
        this.hasUpdate = hasUpdate;
        this.updateSucceeded = updateSucceeded;
    }

    public boolean isHasUpdate() {
        return hasUpdate;
    }

    public void setHasUpdate(boolean hasUpdate) {
        this.hasUpdate = hasUpdate;
    }

    public boolean isUpdateSucceeded() {
        return updateSucceeded;
    }

    public void setUpdateSucceeded(boolean updateSucceeded) {
        this.updateSucceeded = updateSucceeded;
    }

    @Override
    public String toString() {
        return "ResultUpdate{" +
                "hasUpdate=" + hasUpdate +
                ", updateSucceeded=" + updateSucceeded +
                '}';
    }
}
