@startuml
title AVL SDK 初始化激活流程

participant "应用层" as App
participant "AVLEngine" as Engine
participant "AVLCoreEngine" as CoreEngine
participant "AuthInfoManager" as AuthMgr
participant "LicenseManager" as LicenseMgr
participant "AVLEnginePC" as PCEngine
participant "Native层" as Native

App -> Engine: init(context, id, logger, tunnel)
activate Engine

Engine -> CoreEngine: prepareData(context)
activate CoreEngine
CoreEngine --> Engine
deactivate CoreEngine

Engine -> AuthMgr: getInstance(context)
activate AuthMgr
AuthMgr -> AuthMgr: loadDefaultConfig()
note right: 从加密配置文件加载或使用默认配置
AuthMgr --> Engine: authInfo
deactivate AuthMgr

Engine -> CoreEngine: setupAuthParams(uuid, clientId)
activate CoreEngine
CoreEngine -> Native: native_set_auth_params
Native --> CoreEngine
CoreEngine --> Engine
deactivate CoreEngine

Engine -> CoreEngine: init(libPath, dbPath, token)
activate CoreEngine
CoreEngine -> Native: native_init
activate Native
Native -> Native: 加载so库并初始化函数指针
Native -> Native: 设置授权参数
Native --> CoreEngine: 初始化结果
deactivate Native
CoreEngine --> Engine: 初始化结果
deactivate CoreEngine

Engine -> Engine: new Thread(requestAuthToken)
activate Engine
Engine -> LicenseMgr: requestAuthToken(uuid)
activate LicenseMgr
LicenseMgr -> LicenseMgr: generateAuthRequest
LicenseMgr -> CoreEngine: generateAuthRequest
CoreEngine --> LicenseMgr: 加密的请求内容
LicenseMgr -> LicenseMgr: 发送网络请求
LicenseMgr -> CoreEngine: decryptAuthToken
CoreEngine --> LicenseMgr: 解密的token
LicenseMgr -> AuthMgr: saveAuthInfo
LicenseMgr --> Engine
deactivate LicenseMgr
deactivate Engine

alt 长城版本
    Engine -> PCEngine: prepareData(context)
    activate PCEngine
    PCEngine --> Engine
    deactivate PCEngine

    Engine -> PCEngine: init()
    activate PCEngine
    PCEngine -> Native: PC引擎初始化
    Native --> PCEngine
    PCEngine --> Engine: 初始化结果
    deactivate PCEngine
end

Engine -> Engine: prepareCpuMonitor()
Engine -> Engine: prepareUpdateCheckerExe()

Engine --> App: ResultInit
deactivate Engine

@enduml