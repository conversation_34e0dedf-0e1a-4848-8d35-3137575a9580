<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light"
    tools:context=".activity.CacheManagementActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
        android:fitsSystemWindows="true">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            style="@style/CustomToolbar"
            android:layout_width="match_parent"
            android:background="?attr/colorPrimary"
            app:navigationIcon="@drawable/ic_arrow_back"
            app:title="缓存管理"
            app:titleTextColor="@android:color/white"
            app:titleMarginStart="16dp" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- 统计信息卡片 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="缓存统计"
                        android:textColor="@color/black"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tvStatistics"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="总计: 0 个，威胁: 0 个，安全: 0 个"
                        android:textColor="@color/gray"
                        android:textSize="14sp" />

                    <!-- 操作按钮 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:orientation="horizontal">

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btnRefresh"
                            style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginEnd="8dp"
                            android:text="刷新"
                            app:icon="@drawable/ic_refresh" />

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btnClearAll"
                            style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="8dp"
                            android:text="清空全部"
                            android:textColor="@color/danger_red"
                            app:icon="@drawable/ic_delete"
                            app:iconTint="@color/danger_red"
                            app:strokeColor="@color/danger_red" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- 列表容器 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp">

                <FrameLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <!-- RecyclerView -->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recyclerView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:clipToPadding="false"
                        android:padding="8dp"
                        tools:listitem="@layout/item_cache_management" />

                    <!-- 空状态视图 -->
                    <LinearLayout
                        android:id="@+id/emptyView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="32dp"
                        android:visibility="gone">

                        <ImageView
                            android:layout_width="64dp"
                            android:layout_height="64dp"
                            android:alpha="0.5"
                            android:src="@drawable/ic_list_empty"
                            app:tint="@color/gray" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="16dp"
                            android:text="暂无缓存数据"
                            android:textColor="@color/gray"
                            android:textSize="16sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:text="进行文件扫描后会生成缓存数据\n点击列表项可查看详情并复制Hash值"
                            android:textColor="@color/gray"
                            android:textSize="14sp"
                            android:gravity="center"
                            android:lineSpacingExtra="4dp" />

                    </LinearLayout>

                    <!-- 加载进度条 -->
                    <ProgressBar
                        android:id="@+id/progressBar"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_margin="32dp" />

                </FrameLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
