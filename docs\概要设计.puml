@startwbs 模块设计

'left to right direction


* 引擎SDK组件
** API模块
*** 对接各模块
*** 提供统一出口API
** 激活模块
*** 记录自身id、授权信息
*** 向服务器注册，获取激活状态
** 配置管理
*** 黑白名单增删改查
*** 扫描类型增删改查
*** 历史缓存配置增删改查
*** 云查阈值、静默性能阈值的增删改查
*** 对接API模块
** 存储模块
*** 配置数据储存、加解密
*** 缓存结果储存、加解密
** 扫描模块
*** 单文件扫描功能
*** 目录扫描
**** 目录扫描静默（低资源消耗）模式
**** 扫描过程的启停控制及暂停恢复。
**** 静默扫描需配合性能监控模块。
*** 对接API模块
** 性能监控模块
*** 性能阈值配置
*** 调控批量扫描进程
** 病毒库管理
*** 更新查询，上传本地版本获取更新包
*** 更新包拆分，拆分移动及PC引擎的更新包
*** 移动端引擎病毒库本地存储及更新
*** PC引擎病毒库的本地存储及更新
*** 查询当前版本
*** 对接API模块
** 日志模块
*** 支持日志导出或输出到可配置路径
*** 配置变动记录
*** 扫描操作触发及扫描结果记录
*** 日志大小可配置
*** 循环记录，超出后旧日志被覆盖。
*** 对接API模块
** 通信模块
*** 为云查、病毒库更新提供网络通信API
*** 对接为辰的通信SDK

@endwbs

@startuml 图例

title 图例

start

:过程|
:输入<
:输出>

#Aqua :日志记录|

#Lime :网络请求|

end

@enduml

@startuml 激活

title 激活

start

#Aqua :引擎加载|

' 激活过程

if(SDK已激活) then (否)
        repeat
        #Lime :发送激活请求|
        repeat while(判断激活响应有效性) is (无效) not (有效)
else(是)
endif

end
@enduml

@startuml 配置加载

title 配置加载

start

:读取并解密数据库SQLite文件|

#Aqua :加载性能参数|

#Aqua :加载缓存大小配置|

#Aqua :加载文件类型配置|

#Aqua :加载黑白名单到引擎|

end

@enduml

@startuml 病毒库

title 病毒库

start

:生成本地版本信息>

#Aqua :检查更新|

#Lime :发送检查更新请求|

if (解析服务端响应) then(有更新)
        #Lime :下载更新包|
        :分别更新pc引擎&android引擎|
        :重新加载引擎|
else(无更新)
endif

end

@enduml

@startuml 单文件扫描

title 单文件扫描

start

#Aqua :传入扫描路径&回调对象<

if(判断文件类型是否为支持的类型) then(是)
        if(是否在黑白名单及缓存中)then(在黑名单/白名单或缓存中)
        else(不在)
                if(判断文件类型)then(移动app)
                        :加载移动引擎|
                else(媒体文件)
                        :加载PC引擎|
                endif
                :扫描传入文件|
        endif
else(否)
        '不在支持的类型中
endif

#Aqua :扫描结果回调|

end
@enduml

@startuml 目录扫描

title 目录扫描

start

fork

#Aqua :传入扫描路径和回调对象<

if(判断文件数量) then(超过云查阈值)

        repeat :云查|

        if(收到暂停/停止调用)then(是的)
                if(收到的请求)then(暂停)
                        :记录当前扫描目录、未扫描清单|
                        :记录当前扫描方式(云/本地)|
                else(停止)
                endif

                end
        else(没有)
        endif

        :生成文件哈希|

        if(<back:lime>调用云查接口</back>) then(正常返回)
        else(异常无响应)
                :调用单文件扫描接口|
        endif
        #Aqua :扫描结果回调|
        
        repeat while(文件扫描完成) is(没有) not(是的)

else(没有超过云查阈值)
        repeat :遍历文件夹|
                :调用单文件扫描接口|
                #Aqua :扫描结果回调|
        repeat while(还有文件) is(是) not(否)
endif

fork again

#Aqua :继续扫描|

:加载上次记录的目录、未扫描文件清单|
:加载上次扫描模式(云/本地)|

:回到目录扫描逻辑|

endfork

end

@enduml

@startuml 静默扫描

title 静默扫描
start
' 批量静默扫描
#Aqua :传入扫描路径和回调对象<

fork
:启动性能监控进程|
fork again
:启动文件夹扫描|
endfork

:停止监控进程|

end
@enduml

@startuml 性能监控

title 性能监控

start

:解析命令行参数(pid/percent/interval)<

repeat
        :获取当前CPU统计信息>
        :获取当前pid统计信息>

        if(存在历史数据)then(是)
                if(当前数据-历史数据)then(小于percent)
                        :向pid发送sigcont信号|
                        :保存当前数据为历史数据|
                else(大于percent)
                        :向pid发送sigstop信号|
                endif
        else(否)
                :保存当前数据为历史数据|
        endif
repeat while(sleep配置的interval)

:被kill or 收到sigquit信号|
end

@enduml


@startuml 用例图

title 用例图
' 用例，表示参与者为完成某个目标的一系列活动。用例名称，以动宾短语出现，表示参与者做的事情。

left to right direction

' skinparam linetype polyline
' skinparam linetype ortho

actor :VIDS: <<application>> as vids

rectangle SDK {
        usecase SDK初始化 as init
        usecase 更新设置 as settings_crud
        usecase 更新病毒库 as sdk_vdb
        usecase 恶意文件扫描 as scan
        usecase 日志记录 as log
}

rectangle AVL {
        usecase 更新配置项 as config_crud
        usecase 激活 as  act
        usecase 病毒库更新 as vdb
        actor :AntiyCloud: <<service>> as antiyCloud

        antiyCloud <-- config_crud
        antiyCloud <-- act
        antiyCloud <-- vdb
}

rectangle VSOCK {
        actor :VSOCK: <<service>> as vsock

        vsock --> config_crud
        vsock --> act
        vsock --> vdb
}

vids <..> vsock : 转发

vids --> init
vids --> settings_crud
vids --> scan
vids --> sdk_vdb
vids --> log

init -- log : <<include>>
settings_crud -- log : <<include>>
scan -- log : <<include>>
sdk_vdb -- log : <<include>>

@enduml

@startuml 数据库设计
title 数据库设计

hide circle
skinparam linetype ortho

entity 配置 {
        config_id : integer<<autoinc>>
        --
        缓存大小 : integer
        缓存时长 : integer
        云查数量阈值 : integer
        云查大小阈值 : integer
        支持文件类型 : string
}

entity 黑白名单 {
        blackwhite_id : integer<<autoinc>>
        --
        哈希值 : string
        黑或白 : integer
}

entity 激活信息 {
        activation_id : integer<<autoinc>>
        --
        车辆ID : string
        激活代码 : string
        激活时间 : timestamp
}

@enduml