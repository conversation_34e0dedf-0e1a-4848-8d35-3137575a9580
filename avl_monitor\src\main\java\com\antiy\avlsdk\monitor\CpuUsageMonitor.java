package com.antiy.avlsdk.monitor;

import android.content.Context;
import android.content.res.AssetManager;
import android.os.Build;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

public class CpuUsageMonitor {
    private static String mCpuUsagePath;

    private static CpuUsageMonitor mInstance;


    public static String getCpuUsagePath() {
        return mCpuUsagePath;
    }

    public static void copyCpuUsageBinary(Context context,String destinationPath) {
        // prepare the binary that we will use.
        File cpu_usage = new File(destinationPath);
        mCpuUsagePath = cpu_usage.getAbsolutePath();
        // 如果程序已经存在则不拷贝，防止每次进入程序都拷贝
        if (cpu_usage.exists()) return;

        // NOTE: if we check the file existence, app update will cause in-compatible `cpu_usage` file
        //     remains on disk, so here we forcibly copy the binary everytime we got inited.
        copyAssetFileToFilesDir(context, String.format("%s/cpu_usage",
                        Build.CPU_ABI),
                destinationPath);

        if (!cpu_usage.canExecute()) {
            cpu_usage.setExecutable(true);
        }


    }

    private static void copyAssetFileToFilesDir(Context context, String assetFileName, String destinationFileName) {
        AssetManager assetManager = context.getAssets();
        File file = new File(destinationFileName);
        try (InputStream is = assetManager.open(assetFileName);
             OutputStream os = new FileOutputStream(file)) {
            byte[] buffer = new byte[1024];
            int read;
            while ((read = is.read(buffer)) != -1) {
                os.write(buffer, 0, read);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
