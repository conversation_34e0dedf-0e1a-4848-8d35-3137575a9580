package com.antiy.avlsdk.pc;

import android.content.Context;
import android.content.res.AssetManager;
import android.util.Log;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

public class AssetsUtil {

    private static final String TAG = AssetsUtil.class.getSimpleName();

    public static void copyAssetFileToFilesDir(Context context, String assetFileName, String destinationFileName) {
        AssetManager assetManager = context.getAssets();
        File file = new File(destinationFileName);

        try (InputStream is = assetManager.open(assetFileName); OutputStream os = new FileOutputStream(file)) {
            byte[] buffer = new byte[1024];
            int read;
            while ((read = is.read(buffer)) != -1) {
                os.write(buffer, 0, read);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static void copyFilesFromAssets(Context context, String assetsDirectoryPath, String destinationDirectoryPath) {
        AssetManager assetManager = context.getAssets();
        try {
            String[] files = assetManager.list(assetsDirectoryPath);
            if (files.length > 0) {
                File file = new File(destinationDirectoryPath);
                file.mkdirs();
                for (String fileName : files) {
                    copyFilesFromAssets(context, assetsDirectoryPath + "/" + fileName, destinationDirectoryPath + "/" + fileName);
                }
            } else {
                InputStream is = context.getAssets().open(assetsDirectoryPath);
                FileOutputStream fos = new FileOutputStream(new File(destinationDirectoryPath));
                byte[] buffer = new byte[1024];
                int byteCount = 0;

                while ((byteCount = is.read(buffer)) != -1) {
                    fos.write(buffer ,0, byteCount);
                }

                fos.flush();
                is.close();
                fos.close();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     *  解压 assets 中的 zip 文件到指定的私有目录
     * @param context 上下文
     * @param zipName assets里面zip的名字
     * @param targetDirectory 解压到哪个目录,比如长城的需求是解压到:/data/vids
     * @throws IOException
     */
    public static void unzipFromAssets(Context context, String targetDirectory,String zipName) throws IOException {
        AssetManager assetManager = context.getAssets();
        InputStream inputStream = assetManager.open(zipName);
        ZipInputStream zipInputStream = new ZipInputStream(inputStream);
        Log.i(TAG, "start copy engine from unzipFromAssets ");
        ZipEntry zipEntry;
        while ((zipEntry = zipInputStream.getNextEntry()) != null) {
            File file = new File(targetDirectory, zipEntry.getName());

            // 如果是目录，创建它
            if (zipEntry.isDirectory()) {
                file.mkdirs();
            } else {
                // 如果是文件，解压到指定目录
                FileOutputStream fileOutputStream = new FileOutputStream(file);
                byte[] buffer = new byte[1024];
                int length;
                while ((length = zipInputStream.read(buffer)) > 0) {
                    fileOutputStream.write(buffer, 0, length);
                }
                fileOutputStream.close();
            }
            Log.i(TAG, "copy + " + file.getName() +" success,path: " + file.getAbsolutePath());
        }
        zipInputStream.closeEntry();
        zipInputStream.close();
        inputStream.close();
    }

    /**
     * 检查文件夹是否包含文件
     * @param folderPath 文件夹路径
     * @return 文件夹是否包含文件(如果文件夹不存在则返回false)
     */
    public static boolean isFolderHasFiles(String folderPath) {
        File folder = new File(folderPath);
        if (!folder.exists() || !folder.isDirectory()) {
            return false;
        }
        File[] files = folder.listFiles();
        return files != null && files.length > 0;
    }
}
