package com.antiy.demo.fragment

import android.content.Intent
import android.content.pm.ApplicationInfo
import android.content.pm.PackageManager
import android.net.Uri
import android.util.Log
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.ArrayAdapter
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import com.antiy.demo.R
import com.antiy.demo.base.BaseFragment
import com.antiy.demo.databinding.FragmentScanBinding
import com.antiy.demo.activity.ScanningActivity
import com.antiy.avlsdk.scan.FileScanner
import java.io.File
import java.util.ArrayList
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class ScanFragment : BaseFragment<FragmentScanBinding>() {
    
    // 当前选中的扫描类型
    private var currentScanType = ScanType.CUSTOM
    
    // 定义应用信息类
    data class AppInfo(
        val appName: String,  // 应用名称
        val packageName: String,  // 包名
        val appPath: String   // 应用路径
    )
    
    // 文件选择器
    private val filePickerLauncher = registerForActivityResult(ActivityResultContracts.GetContent()) { uri: Uri? ->
        uri?.let {
            val filePath = FileUtils.getPathFromUri(requireContext(), it)
            if (filePath.isNullOrEmpty()) {
                binding.textSelectedFile.text = "文件路径解析失败"
                binding.textSelectedFile.setTextColor(ContextCompat.getColor(requireContext(), R.color.warning_yellow))
                binding.textSelectedFile.visibility = android.view.View.VISIBLE
                binding.textFileScanResult.visibility = android.view.View.GONE
                return@let
            }
            binding.textSelectedFile.text = filePath
            binding.textSelectedFile.setTextColor(ContextCompat.getColor(requireContext(), R.color.black))
            binding.textSelectedFile.visibility = android.view.View.VISIBLE

            // 显示正在扫描
            binding.textFileScanResult.text = "正在扫描..."
            binding.textFileScanResult.setTextColor(ContextCompat.getColor(requireContext(), R.color.processing_blue))
            binding.textFileScanResult.visibility = android.view.View.VISIBLE

            // 启动协程进行扫描
            CoroutineScope(Dispatchers.IO).launch {
                val result = try {
                    FileScanner.scan(filePath)
                } catch (e: Exception) {
                    null
                }
                withContext(Dispatchers.Main) {
                    binding.textFileScanResult.text = when {
                        result == null -> "扫描失败"
                        result.isMalicious -> "发现威胁: ${result.virusName}"
                        else -> "安全"
                    }
                    binding.textFileScanResult.setTextColor(
                        ContextCompat.getColor(requireContext(),
                            when {
                                result == null -> R.color.gray
                                result.isMalicious -> R.color.warning_yellow
                                else -> R.color.safe_green
                            }
                        )
                    )
                    binding.textFileScanResult.visibility = android.view.View.VISIBLE
                }
            }
        }
    }
    
    override fun getViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): FragmentScanBinding {
        return FragmentScanBinding.inflate(inflater, container, false)
    }

    override fun initView() {
        // 设置CPU限制选择器
        setupCpuLimitSpinner()
    }

    override fun initListener() {
        // 设置点击事件
        setupClickListeners()
        // 默认选中自定义扫描
        selectScanType(ScanType.CUSTOM)
        // 文件扫描按钮点击事件
        binding.btnSelectFileScan.setOnClickListener {
            filePickerLauncher.launch("*/*") // 只允许选择文件
        }
    }


    private fun setupCpuLimitSpinner() {
        val cpuLimits = arrayOf("低 (25%)", "中 (50%)", "高 (75%)", "不限制")
        val adapter = ArrayAdapter(requireContext(), android.R.layout.simple_spinner_item, cpuLimits)
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        binding.spinnerCpuLimit.adapter = adapter
        binding.spinnerCpuLimit.setSelection(1) // 默认选择"中 (50%)"
    }
    
    private fun setupClickListeners() {
        // 快速扫描点击事件
         binding.cardQuickScan.setOnClickListener {
            // 选中快速扫描
            selectScanType(ScanType.QUICK)
        }
        
        // 全面扫描点击事件
        binding.cardFullScan.setOnClickListener {
            // 选中全面扫描
            selectScanType(ScanType.FULL)
        }
        
        // 自定义扫描点击事件
        binding.cardCustomScan.setOnClickListener {
            // 选中自定义扫描
            selectScanType(ScanType.CUSTOM)
        }
        
        // 定时扫描点击事件
        binding.cardScheduledScan.setOnClickListener {
            // 打开定时扫描设置
            openScheduledScanSettings()
        }
        
        // 云查杀选项变更
        binding.checkBoxCloudScan.setOnCheckedChangeListener { _, isChecked ->
            // 更新云查杀设置
            updateCloudScanSetting(isChecked)
        }
        
        // 启发式扫描选项变更
        binding.checkBoxBootScan.setOnCheckedChangeListener { _, isChecked ->
            // 更新启发式扫描设置
            updateBootScanSetting(isChecked)
        }
        
        // 开始扫描按钮点击事件
        binding.buttonStartScan.setOnClickListener {
            // 开始扫描
            startScan()
        }
    }
    
    private fun selectScanType(scanType: ScanType) {
        // 保存当前选中的扫描类型
        currentScanType = scanType
        
        // 重置所有卡片的选中状态
        binding.cardQuickScan.setCardBackgroundColor(resources.getColor(R.color.white, null))
        binding.cardFullScan.setCardBackgroundColor(resources.getColor(R.color.white, null))
        binding.cardCustomScan.setCardBackgroundColor(resources.getColor(R.color.white, null))
        
        // 设置选中卡片的背景色
        when (scanType) {
            ScanType.QUICK -> {
                binding.cardQuickScan.setCardBackgroundColor(resources.getColor(R.color.light_blue, null))
            }
            ScanType.FULL -> {
                binding.cardFullScan.setCardBackgroundColor(resources.getColor(R.color.light_blue, null))
            }
            ScanType.CUSTOM -> {
                binding.cardCustomScan.setCardBackgroundColor(resources.getColor(R.color.light_blue, null))
            }
        }
    }
    
    private fun startScan() {
        when (currentScanType) {
            ScanType.QUICK -> {
                // 快速扫描 - 扫描已安装的应用
                startQuickScan()
            }
            ScanType.FULL, ScanType.CUSTOM -> {
                // 全面扫描或自定义扫描 - 使用原有逻辑
                val intent = Intent(requireContext(), ScanningActivity::class.java)
                
                // 设置扫描类型
                when (currentScanType) {
                    ScanType.FULL -> intent.putExtra(ScanningActivity.EXTRA_SCAN_TYPE, ScanningActivity.SCAN_TYPE_FULL)
                    ScanType.CUSTOM -> intent.putExtra(ScanningActivity.EXTRA_SCAN_TYPE, ScanningActivity.SCAN_TYPE_CUSTOM)
                    else -> {} // 不会执行到这里
                }
                
                startActivity(intent)
            }
        }
    }
    
    /**
     * 开始快速扫描 - 扫描已安装的应用
     */
    private fun startQuickScan() {
        // 获取已安装的应用列表和应用名称
        val installedApps = getInstalledApps()
        
        if (installedApps.isEmpty()) {
            Toast.makeText(requireContext(), "未找到已安装的应用", Toast.LENGTH_SHORT).show()
            return
        }
        
        // 创建Intent并传递应用信息
        val intent = Intent(requireContext(), ScanningActivity::class.java).apply {
            putExtra(ScanningActivity.EXTRA_SCAN_TYPE, ScanningActivity.SCAN_TYPE_QUICK)
            
            // 分别提取路径和应用名称列表
            val appPaths = ArrayList(installedApps.map { it.appPath })
            val appNames = ArrayList(installedApps.map { it.appName })
            val packageNames = ArrayList(installedApps.map { it.packageName })
            
            putStringArrayListExtra(ScanningActivity.EXTRA_APP_PATHS, appPaths)
            putStringArrayListExtra(ScanningActivity.EXTRA_APP_NAMES, appNames)
            putStringArrayListExtra(ScanningActivity.EXTRA_PACKAGE_NAMES, packageNames)
        }
        
        startActivity(intent)
    }
    
    /**
     * 获取已安装的应用列表
     * @return 应用信息列表
     */
    private fun getInstalledApps(): List<AppInfo> {
        val appInfoList = mutableListOf<AppInfo>()
        val packageManager = requireContext().packageManager
        
        try {
            // 获取所有已安装的应用列表（包括系统应用）
            val installedApps = packageManager.getInstalledApplications(PackageManager.GET_META_DATA)
            
            // 获取系统预装包列表 - 用于更准确地识别第三方应用
            val systemPackages = mutableSetOf<String>()
            packageManager.getInstalledPackages(PackageManager.GET_UNINSTALLED_PACKAGES).forEach { packageInfo ->
                if (packageInfo.applicationInfo.flags and ApplicationInfo.FLAG_SYSTEM != 0) {
                    systemPackages.add(packageInfo.packageName)
                }
            }
            
            for (appInfo in installedApps) {
                val packageName = appInfo.packageName
                
                // 更全面的判断条件来识别第三方应用:
                // 1. 不是系统应用包名
                // 2. 或者是可更新的系统应用（说明可能是被替换过的系统应用）
                val isThirdPartyApp = !systemPackages.contains(packageName) || 
                        (appInfo.flags and ApplicationInfo.FLAG_UPDATED_SYSTEM_APP != 0)
                
                // 忽略一些明显的系统包
                val isSystemPackage = packageName.startsWith("android") || 
                        packageName.startsWith("com.android") || 
                        packageName.startsWith("com.google.android")
                
                if (isThirdPartyApp && !isSystemPackage) {
                    val sourceDir = appInfo.sourceDir
                    if (sourceDir != null && File(sourceDir).exists()) {
                        // 获取应用名称
                        val appName = appInfo.loadLabel(packageManager).toString()
                        
                        // 创建AppInfo对象并添加到列表
                        appInfoList.add(
                            AppInfo(
                                appName = appName,
                                packageName = appInfo.packageName,
                                appPath = sourceDir
                            )
                        )
                    }
                }
            }
            
            // 如果检测到的应用数量太少，降低筛选条件，获取更多应用
            if (appInfoList.size < 10) {
                Log.d("ScanFragment", "检测到的第三方应用数量太少，降低筛选条件")
                
                // 清空之前的列表
                appInfoList.clear()
                
                for (appInfo in installedApps) {
                    // 排除明显的系统包
                    val packageName = appInfo.packageName
                    if (!packageName.startsWith("android") && 
                        !packageName.startsWith("com.android") && 
                        !packageName.startsWith("com.google.android")) {
                        
                        val sourceDir = appInfo.sourceDir
                        if (sourceDir != null && File(sourceDir).exists()) {
                            // 获取应用名称
                            val appName = appInfo.loadLabel(packageManager).toString()
                            
                            // 创建AppInfo对象并添加到列表
                            appInfoList.add(
                                AppInfo(
                                    appName = appName,
                                    packageName = appInfo.packageName,
                                    appPath = sourceDir
                                )
                            )
                        }
                    }
                }
            }
            
            Log.d("ScanFragment", "检测到 ${appInfoList.size} 个第三方应用")
        } catch (e: Exception) {
            Log.e("ScanFragment", "获取已安装应用列表时出错", e)
            e.printStackTrace()
        }
        
        return appInfoList
    }
    
    private fun openScheduledScanSettings() {
        // 打开定时扫描设置页面
        // 例如：显示时间选择器
    }
    
    private fun updateCloudScanSetting(enabled: Boolean) {
        // 更新云查杀设置
        // 例如：保存到SharedPreferences
    }
    
    private fun updateBootScanSetting(enabled: Boolean) {
        // 更新启发式扫描设置
        // 例如：保存到SharedPreferences
    }

    private fun startCustomScan() {
        val intent = Intent(requireContext(), ScanningActivity::class.java).apply {
            putExtra(ScanningActivity.EXTRA_SCAN_TYPE, ScanningActivity.SCAN_TYPE_CUSTOM)
        }
        startActivity(intent)
    }

    // 扫描类型枚举
    enum class ScanType {
        QUICK, FULL, CUSTOM
    }

    // 扫描状态枚举
    enum class ScanStatus {
        SAFE, THREAT_FOUND, PROCESSING
    }

    // 扫描记录数据类
    data class ScanRecord(
        val type: String,
        val time: String,
        val status: ScanStatus,
        val details: String
    )

    // 更新最近扫描记录
    private fun updateRecentScans(records: List<ScanRecord>) {
        val recentScansContainer = view?.findViewById<LinearLayout>(R.id.nav_scan)
        recentScansContainer?.removeAllViews()

        records.forEach { record ->
            val recordView = layoutInflater.inflate(R.layout.item_scan_record, recentScansContainer, false)

            // 设置扫描类型
            recordView.findViewById<TextView>(R.id.textScanType).text = record.type
            // 设置扫描时间
            recordView.findViewById<TextView>(R.id.textScanTime).text = record.time
            
            // 设置扫描状态
            val statusText = recordView.findViewById<TextView>(R.id.textScanStatus)
            val statusColor = when (record.status) {
                ScanStatus.SAFE -> R.color.safe_green
                ScanStatus.THREAT_FOUND -> R.color.warning_yellow
                ScanStatus.PROCESSING -> R.color.processing_blue
            }
            statusText.setTextColor(resources.getColor(statusColor, null))
            statusText.text = when (record.status) {
                ScanStatus.SAFE -> "安全"
                ScanStatus.THREAT_FOUND -> "已处理"
                ScanStatus.PROCESSING -> "扫描中"
            }

            // 设置扫描详情
            recordView.findViewById<TextView>(R.id.textScanDetails).text = record.details

            recentScansContainer?.addView(recordView)
        }
    }
}