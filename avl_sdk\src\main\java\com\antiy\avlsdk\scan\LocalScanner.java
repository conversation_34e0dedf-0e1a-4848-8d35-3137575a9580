package com.antiy.avlsdk.scan;

import com.antiy.avlsdk.AVLEngine;
import com.antiy.avlsdk.callback.ScanListener;
import com.antiy.avlsdk.entity.ResultScan;

import java.io.File;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

public class LocalScanner extends BaseScanner{
    private final AtomicInteger processedFiles = new AtomicInteger(0);
    private final AtomicBoolean hasFinished = new AtomicBoolean(false);

    public LocalScanner(List<File> files, ScanListener callback) {
        super(files, callback);
    }

    @Override
    public void startScan() {
        if (!AVLEngine.getInstance().getInitResult().isSuccess) {
            AVLEngine.Logger.error("Engine not initialized");
            return;
        }
        for (int i = 0; i < files.size(); i++) {
            taskQueue.add(new ScanTask(i, files.get(i)));
        }
        processScan();
    }

    @Override
    public void stopScan() {
        super.stopScan();
    }

    private void processScan() {
        int totalFiles = files.size();
        AVLEngine.Logger.info("processScan totalFiles : " + totalFiles);

        // 修复循环条件：使用AND逻辑，确保停止信号能正确终止循环
        while (!isStopped.get() && !taskQueue.isEmpty()) {
            handlePause();
            if (isStopped.get()) {
                AVLEngine.Logger.info("Scan stopped by user request");
                callback.scanStop();
                taskQueue.clear();
                break;
            }
            if (isPaused.get()) continue;

            try {
                ScanTask task = taskQueue.poll();
                AVLEngine.Logger.info("scan task : " + task + ",currentIndex = " + currentIndex.get());

                if (task != null) {
                    processLocalScan(task);
                    if (processedFiles.get() >= totalFiles && hasFinished.compareAndSet(false, true)) {
                        AVLEngine.Logger.info("All files processed, finishing scan");
                        callback.scanFinish();
                        AVLEngine.Logger.info("scanFinish: processed " + processedFiles.get() + "/" + totalFiles);
                        break;
                    }
                } else {
                    // 如果task为null但队列应该不为空，说明出现了并发问题
                    AVLEngine.Logger.warn("Got null task but queue should not be empty, checking completion");
                    if (processedFiles.get() >= totalFiles && hasFinished.compareAndSet(false, true)) {
                        AVLEngine.Logger.info("All files processed, finishing scan");
                        callback.scanFinish();
                        AVLEngine.Logger.info("scanFinish: all files processed");
                        break;
                    }
                }
            } catch (Exception e) {
                AVLEngine.Logger.error("Local scan failed: " + e.getMessage());
                // 异常时也要检查是否需要结束扫描
                if (processedFiles.get() >= totalFiles && hasFinished.compareAndSet(false, true)) {
                    AVLEngine.Logger.info("Scan completed with errors");
                    callback.scanFinish();
                    break;
                }
            }
        }

        // 如果在扫描过程中停止扫描，则通知扫描停止清空任务队列
        if (isStopped.get()) {
            callback.scanStop();
            taskQueue.clear();
        }
    }

    private void processLocalScan(ScanTask task) {
        long startTime = System.currentTimeMillis();
        AVLEngine.Logger.info("Start scanning files: " + task.file.getAbsolutePath() +
            ", Task Index: " + task.index +
            ", Currently completed: " + processedFiles.get());

        try {
            callback.scanFileStart(task.index, task.file.getAbsolutePath());
            ResultScan localResult = FileScanner.scan(task.file.getAbsolutePath());
            callback.scanFileFinish(task.index, task.file.getAbsolutePath(), localResult);

            int processed = processedFiles.incrementAndGet();

            long endTime = System.currentTimeMillis();
            AVLEngine.Logger.info("Complete scanning of files: " + task.file.getAbsolutePath() +
                ", Task Index: " + task.index +
                ", Overall progress: " + processed + "/" + files.size() +
                ", time consuming: " + (endTime - startTime) + "ms");

        } catch (Exception e) {
            processedFiles.incrementAndGet();
            AVLEngine.Logger.error("Processing file failed: " + task.file + ", Error: " + Arrays.toString(e.getStackTrace()));
        }
    }
}
