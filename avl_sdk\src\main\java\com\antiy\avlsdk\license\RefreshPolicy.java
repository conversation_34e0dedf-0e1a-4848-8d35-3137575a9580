package com.antiy.avlsdk.license;

/**
 * License刷新策略接口
 * 定义了判断是否需要刷新license的策略
 * 使用策略模式，支持不同的刷新策略实现
 * 
 * <AUTHOR>
 * @version 1.0
 */
public interface RefreshPolicy {
    
    /**
     * 判断是否应该刷新license
     * 
     * @return true表示需要刷新，false表示不需要刷新
     */
    boolean shouldRefresh();
    
    /**
     * 刷新成功后的回调
     * 用于更新策略内部状态，如记录刷新时间等
     */
    void onRefreshSuccess();
    
    /**
     * 刷新失败后的回调
     * 用于处理失败情况，如记录失败次数等
     * 
     * @param reason 失败原因
     */
    void onRefreshFailed(String reason);
    
    /**
     * 获取策略描述
     * 
     * @return 策略的描述信息
     */
    String getDescription();
}
