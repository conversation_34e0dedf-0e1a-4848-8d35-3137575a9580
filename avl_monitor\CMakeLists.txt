cmake_minimum_required(VERSION 3.6)
project("cpu_usage")

# 设置二进制的输出目录为源码目录的 assets 文件夹
#set(CMAKE_RUNTIME_OUTPUT_DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}/src/main/assets/${CMAKE_ANDROID_ARCH_ABI}")

message("OUTPUT TO: ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}")

# set(LIBRARY_OUTPUT_PATH "${CMAKE_CURRENT_SOURCE_DIR}/src/main/assets/${CMAKE_ANDROID_ARCH_ABI}")

add_subdirectory(src/main/cpp/argparse)

add_executable(${PROJECT_NAME}
        src/main/cpp/main.c)

target_include_directories(${PROJECT_NAME} PRIVATE src/main/cpp/argparse)

target_link_libraries(${PROJECT_NAME}
        log argparse_static)