<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F5F5F5">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 标题 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="实时防护"
            android:textColor="#000000"
            android:textSize="24sp"
            android:textStyle="bold" />

        <!-- 主防护卡片 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="0dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="24dp">

                <ImageView
                    android:layout_width="80dp"
                    android:layout_height="80dp"
                    android:src="@drawable/ic_realtime_protection"
                    app:tint="#4285F4" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="实时防护已开启"
                    android:textColor="#000000"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvProtectionStatus"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="您的设备受到全面保护"
                    android:textColor="#757575"
                    android:textSize="14sp" />

                <Switch
                    android:id="@+id/switchMainProtection"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp" />
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- 防护模块标题 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:text="防护模块"
            android:textColor="#000000"
            android:textSize="18sp"
            android:textStyle="bold" />

        <!-- 防护模块卡片 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="0dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- 文件防护 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:padding="16dp">

                    <ImageView
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:background="@drawable/circle_background_light_blue"
                        android:padding="8dp"
                        android:src="@drawable/ic_file"
                        app:tint="#4285F4" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="文件防护"
                            android:textColor="#000000"
                            android:textSize="16sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="监控文件操作，拦截恶意文件"
                            android:textColor="#757575"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <Switch
                        android:id="@+id/switchFileProtection"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content" />
                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="#F0F0F0" />

                <!-- 网络防护 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:padding="16dp">

                    <ImageView
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:background="@drawable/circle_background_light_blue"
                        android:padding="8dp"
                        android:src="@drawable/ic_network"
                        app:tint="#4285F4" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="网络防护"
                            android:textColor="#000000"
                            android:textSize="16sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="监控网络连接，阻止恶意网站"
                            android:textColor="#757575"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <Switch
                        android:id="@+id/switchNetworkProtection"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content" />
                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="#F0F0F0" />

                <!-- 应用防护 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:padding="16dp">

                    <ImageView
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:background="@drawable/circle_background_light_blue"
                        android:padding="8dp"
                        android:src="@drawable/ic_app"
                        app:tint="#4285F4" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="应用防护"
                            android:textColor="#000000"
                            android:textSize="16sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="监控应用行为，阻止恶意活动"
                            android:textColor="#757575"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <Switch
                        android:id="@+id/switchAppProtection"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content" />
                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="#F0F0F0" />

                <!-- 隐私防护 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:padding="16dp">

                    <ImageView
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:background="@drawable/circle_background_light_blue"
                        android:padding="8dp"
                        android:src="@drawable/ic_privacy"
                        app:tint="#4285F4" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="隐私防护"
                            android:textColor="#000000"
                            android:textSize="16sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="保护个人信息，防止隐私泄露"
                            android:textColor="#757575"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <Switch
                        android:id="@+id/switchPrivacyProtection"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content" />
                </LinearLayout>
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- 高级设置标题 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:text="高级设置"
            android:textColor="#000000"
            android:textSize="18sp"
            android:textStyle="bold" />

        <!-- 高级设置卡片 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="0dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- 白名单管理 -->
                <LinearLayout
                    android:id="@+id/layoutWhitelist"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:padding="16dp"
                    android:clickable="true"
                    android:foreground="?attr/selectableItemBackground">

                    <ImageView
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:background="@drawable/circle_background_light_blue"
                        android:padding="8dp"
                        android:src="@drawable/ic_whitelist"
                        app:tint="#4285F4" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="白名单管理"
                            android:textColor="#000000"
                            android:textSize="16sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="管理受信任的应用和文件"
                            android:textColor="#757575"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_arrow_right"
                        app:tint="#CCCCCC" />
                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="#F0F0F0" />

                <!-- 通知设置 -->
                <LinearLayout
                    android:id="@+id/layoutNotification"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:padding="16dp"
                    android:clickable="true"
                    android:foreground="?attr/selectableItemBackground">

                    <ImageView
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:background="@drawable/circle_background_light_blue"
                        android:padding="8dp"
                        android:src="@drawable/ic_notification"
                        app:tint="#4285F4" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="通知设置"
                            android:textColor="#000000"
                            android:textSize="16sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="自定义安全通知和警报"
                            android:textColor="#757575"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_arrow_right"
                        app:tint="#CCCCCC" />
                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="#F0F0F0" />

                <!-- 扫描策略 -->
                <LinearLayout
                    android:id="@+id/layoutScanStrategy"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:padding="16dp"
                    android:clickable="true"
                    android:foreground="?attr/selectableItemBackground">

                    <ImageView
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:background="@drawable/circle_background_light_blue"
                        android:padding="8dp"
                        android:src="@drawable/ic_scan_strategy"
                        app:tint="#4285F4" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="扫描策略"
                            android:textColor="#000000"
                            android:textSize="16sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="自定义扫描行为和规则"
                            android:textColor="#757575"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_arrow_right"
                        app:tint="#CCCCCC" />
                </LinearLayout>

            </LinearLayout>
        </androidx.cardview.widget.CardView>
    </LinearLayout>
</ScrollView> 