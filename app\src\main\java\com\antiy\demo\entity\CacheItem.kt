package com.antiy.demo.entity

import java.text.SimpleDateFormat
import java.util.*

/**
 * 缓存项数据实体
 * @param hash 文件Hash值
 * @param virusName 病毒名称（空表示安全）
 * @param timestamp 时间戳
 */
data class CacheItem(
    val hash: String,
    val virusName: String,
    val timestamp: Long
) {
    /**
     * 获取状态显示文本
     */
    fun getStatusText(): String {
        return if (virusName.isEmpty()) "安全" else "威胁"
    }
    
    /**
     * 获取状态颜色资源ID
     */
    fun getStatusColorRes(): Int {
        return if (virusName.isEmpty()) android.R.color.holo_green_dark else android.R.color.holo_red_dark
    }
    
    /**
     * 获取简短的Hash显示（前8位...后8位）
     */
    fun getShortHash(): String {
        return if (hash.length > 16) {
            "${hash.substring(0, 8)}...${hash.substring(hash.length - 8)}"
        } else {
            hash
        }
    }
    
    /**
     * 获取格式化的时间显示
     */
    fun getFormattedTime(): String {
        val sdf = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        return sdf.format(Date(timestamp))
    }
    
    /**
     * 获取相对时间显示
     */
    fun getRelativeTime(): String {
        val now = System.currentTimeMillis()
        val diff = now - timestamp
        
        return when {
            diff < 60 * 1000 -> "刚刚"
            diff < 60 * 60 * 1000 -> "${diff / (60 * 1000)}分钟前"
            diff < 24 * 60 * 60 * 1000 -> "${diff / (60 * 60 * 1000)}小时前"
            diff < 7 * 24 * 60 * 60 * 1000 -> "${diff / (24 * 60 * 60 * 1000)}天前"
            else -> getFormattedTime()
        }
    }
    
    /**
     * 获取病毒名称显示（如果为空则显示"无威胁"）
     */
    fun getVirusDisplayName(): String {
        return if (virusName.isEmpty()) "无威胁" else virusName
    }
    
    /**
     * 判断是否为威胁
     */
    fun isThreat(): Boolean {
        return virusName.isNotEmpty()
    }
}
