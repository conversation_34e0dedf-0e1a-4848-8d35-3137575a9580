package com.antiy.avlsdk.cloud.util;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.util.Log;

import com.antiy.avlsdk.cloud.AVLEngineCloud;

import java.io.File;

public class FileMd5Utils {
    private static final String TAG = "AVL " + FileMd5Utils.class.getName();
    public static String getFileMd5(Context context, String filePath) {
        File file = new File(filePath);
        if (!file.exists()) {
            return "";
        }
        MyDatabaseHelper dbHelper = new MyDatabaseHelper(context);
        SQLiteDatabase db = dbHelper.getWritableDatabase();
        String sql = "SELECT * from md5 where filepath=\'" + filePath + "\' and filesize=" + file.length() + " and modifytime=" + file.lastModified() + ";";
        Cursor cursor = db.rawQuery(sql, null);
        try {
            // 检查查询结果是否存在
            if (cursor.moveToFirst()) {
                do {
                    // 获取每一列的数据
                    int columnIndex = cursor.getColumnIndex("filemd5");
                    return cursor.getString(columnIndex);
                } while (cursor.moveToNext());
            }
        } finally {
            // 关闭Cursor释放资源
            if (cursor != null && !cursor.isClosed()) {
                cursor.close();
            }
            db.close();
        }

        //未查找到缓存
        String fileMd5 = AVLEngineCloud.getMD5Checksum(filePath);
        Log.d(TAG, "cal done");
        try {
            //写入缓存
            SQLiteDatabase dbW = dbHelper.getWritableDatabase();
            ContentValues values = new ContentValues();
            values.put("filepath", filePath);
            values.put("filesize", file.length());
            values.put("modifytime", file.lastModified());
            values.put("filemd5", fileMd5);
            dbW.insert("md5", null, values);
            dbW.close();
        } catch (Exception e) {
            e.printStackTrace();
        }

        return fileMd5;
    }
}
