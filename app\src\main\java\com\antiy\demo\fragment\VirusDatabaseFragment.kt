package com.antiy.demo.fragment

import android.app.ProgressDialog
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import com.antiy.avlsdk.AVLEngine
import com.antiy.demo.base.BaseFragment
import com.antiy.demo.databinding.FragmentVirusDatabaseBinding
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class VirusDatabaseFragment : BaseFragment<FragmentVirusDatabaseBinding>() {

    override fun getViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): FragmentVirusDatabaseBinding {
        return FragmentVirusDatabaseBinding.inflate(inflater, container, false)
    }

    override fun initView() {
        // 初始化视图
        updateDatabaseInfo()
    }

    override fun initListener() {
        // 立即更新按钮点击事件
        binding.btnUpdate.setOnClickListener {
            updateDatabase()
        }

        // 自动更新开关事件
        binding.cbAutoUpdate.setOnCheckedChangeListener { _, isChecked ->
            updateAutoUpdateSettings(isChecked)
        }

        // 仅WiFi更新开关事件
        binding.cbWifiOnly.setOnCheckedChangeListener { _, isChecked ->
            updateWifiOnlySettings(isChecked)
        }

        // 更新频率选择事件
        binding.spinnerUpdateFrequency.onItemSelectedListener = object : android.widget.AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: android.widget.AdapterView<*>?, view: View?, position: Int, id: Long) {
                updateFrequencySettings(position)
            }

            override fun onNothingSelected(parent: android.widget.AdapterView<*>?) {}
        }
    }

    private fun updateDatabaseInfo() {
        // 更新病毒库信息
        binding.tvVersion.text = AVLEngine.getVdbVersion() ?: "V2023.06.15"
        binding.tvVersionInfo.text = AVLEngine.getVdbVersion() ?: "V2023.06.15"
        binding.tvSignatureCount.text = "8,452,196"
        binding.tvUpdateFrequency.text = "每日自动更新"
        binding.tvStorageSpace.text = "124.5 MB"
    }

    private fun updateDatabase() {
        // 显示加载对话框
        val loadingDialog = ProgressDialog(requireContext()).apply {
            setMessage("正在更新病毒库...")
            setCancelable(false)
            show()
        }

        // 禁用更新按钮
        binding.btnUpdate.isEnabled = false
        
        // 使用协程在后台线程执行更新操作
        CoroutineScope(Dispatchers.IO).launch {
            try {
                // 调用AVLEngine的checkUpdate方法进行更新
                val result = AVLEngine.getInstance().checkUpdate()
                
                // 切换到主线程更新UI
                withContext(Dispatchers.Main) {
                    // 关闭加载对话框
                    loadingDialog.dismiss()
                    
                    // 根据更新结果显示不同的提示
                    when {
                        result.isHasUpdate && result.isUpdateSucceeded -> {
                            // 更新成功
                            showUpdateResult("病毒库更新成功")
                            // 更新病毒库信息
                            updateDatabaseInfo()
                        }
                        result.isHasUpdate && !result.isUpdateSucceeded -> {
                            // 有更新但更新失败
                            showUpdateResult("病毒库更新失败，请稍后重试")
                        }
                        !result.isHasUpdate -> {
                            // 没有更新
                            showUpdateResult("病毒库已是最新版本")
                        }
                    }
                    
                    // 重新启用更新按钮
                    binding.btnUpdate.isEnabled = true
                }
            } catch (e: Exception) {
                // 发生异常时在主线程更新UI
                withContext(Dispatchers.Main) {
                    // 关闭加载对话框
                    loadingDialog.dismiss()
                    
                    // 显示错误提示
                    showUpdateResult("更新过程中发生错误: ${e.message}")
                    Log.e("VirusDatabaseFragment", "更新过程中发生错误", e)
                    
                    // 重新启用更新按钮
                    binding.btnUpdate.isEnabled = true
                }
            }
        }
    }
    
    /**
     * 显示更新结果，使用多种方式确保消息能够显示
     */
    private fun showUpdateResult(message: String) {
        try {
            // 首先尝试使用Toast
            Toast.makeText(requireContext(), message, Toast.LENGTH_SHORT).show()
        } catch (e: Exception) {
            Log.e("VirusDatabaseFragment", "无法显示Toast: ${e.message}")
            
            try {
                // 如果Toast失败，尝试更新UI上的文本
                // 直接使用binding，如果binding或tvVersionInfo为null会抛出异常，但会被catch捕获
                binding.tvVersionInfo.text = message
            } catch (e: Exception) {
                Log.e("VirusDatabaseFragment", "无法更新UI: ${e.message}")
            }
        }
    }

    private fun updateAutoUpdateSettings(enabled: Boolean) {
        // 更新自动更新设置
        binding.spinnerUpdateFrequency.isEnabled = enabled
        // TODO: 保存设置到SharedPreferences
    }

    private fun updateWifiOnlySettings(wifiOnly: Boolean) {
        // 更新WiFi限制设置
        // TODO: 保存设置到SharedPreferences
    }

    private fun updateFrequencySettings(position: Int) {
        // 更新频率设置
        val frequency = when(position) {
            0 -> "每天"
            1 -> "每周"
            2 -> "每月"
            else -> "每天"
        }
        // TODO: 保存设置到SharedPreferences
    }
}