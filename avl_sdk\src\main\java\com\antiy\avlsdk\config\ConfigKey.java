package com.antiy.avlsdk.config;

/**
 * 2 * Copyright (C), 2020-2024
 * 3 * FileName: ConfigKey
 * 4 * Author: wang<PERSON>o
 * 5 * Date: 2024/9/2 10:30
 * 6 * Description: 配置枚举
 * 10
 */
public enum ConfigKey {
    /** 黑白名单，对应value为HashMap*/
    BLACK_WHITE_LIST,
    /** 扫描类型，对应value为StringList*/
    SCAN_TYPE,
    /** 历史缓存大小，对应value为int*/
    HISTORY_SIZE,
    /** 历史缓存时长，对应value为int*/
    HISTORY_TIMEOUT,
    /** 云查阈值，对应value为int*/
    CLOUDSCAN_COUNT,
    /** 云查文件大小阈值*/
    CLOUDSCAN_SIZE,
    /** 静默查杀阈值，对应value为int,指CPU的百分比*/
    SILENTSCAN_RES
}
