@startuml AVL引擎扫描缓存处理时序图
!theme plain
title AVL引擎扫描缓存处理时序图

participant "客户端" as Client
participant "AVLEngine" as AVLEngine
participant "ScannerFactory" as ScannerFactory
participant "Scanner\n(Cloud/Local)" as Scanner
participant "FileScanner" as FileScanner
participant "CacheManager" as CacheManager
participant "DataManager" as DataManager
participant "DatabaseHelper" as DatabaseHelper
participant "EncryptorHelper" as EncryptorHelper

== 扫描开始阶段 ==
Client -> AVLEngine: scanDir(path, listener)
'AVLEngine -> AVLEngine: validateScanRequest()
'AVLEngine -> AVLEngine: initializeScan()
'AVLEngine -> AVLEngine: prepareFileList()

== 决定扫描模式 ==
AVLEngine -> AVLEngine: shouldUseCloudScanner()
AVLEngine -> ScannerFactory: createScanner(mode, files, listener)
ScannerFactory --> AVLEngine: Scanner实例
AVLEngine -> Scanner: startScan()

== 文件扫描与缓存处理 ==
loop 对每个文件
    Scanner -> FileScanner: scan(filePath)
    
    note over FileScanner, EncryptorHelper: 缓存Key生成
    FileScanner -> EncryptorHelper: calcPathSHA256(filePath)
    EncryptorHelper --> FileScanner: SHA256哈希值
    note right of FileScanner: 缓存Key = SHA256哈希值
    
    note over FileScanner, DataManager: 缓存检查
    FileScanner -> CacheManager: hasCache(sha256)
    CacheManager -> DataManager: getCacheData(sha256)
    DataManager -> DatabaseHelper: getCacheData(sha256)
    DatabaseHelper --> DataManager: CacheEntity或null
    DataManager --> CacheManager: CacheEntity或null
    CacheManager --> FileScanner: boolean
    
    alt 有缓存
        note right of FileScanner: 直接返回缓存结果
        FileScanner -> CacheManager: getCacheResult(sha256)
        CacheManager -> DataManager: getCacheData(sha256)
        DataManager --> CacheManager: CacheEntity
        CacheManager --> FileScanner: CacheEntity
        FileScanner --> Scanner: ResultScan(来自缓存)
    else 无缓存
        note over FileScanner: 执行实际扫描
        FileScanner -> FileScanner: 检查黑白名单
        FileScanner -> FileScanner: 判断文件大小是否云查
        
        alt 需要云查
            FileScanner -> FileScanner: cloudScan(filePath, sha256)
        else 本地扫描
            alt APK/JAR/DEX文件
                FileScanner -> FileScanner: MobileEngine.scan()
            else 其他文件
                FileScanner -> FileScanner: PcEngine.scan()
            end
        end
        
        note over FileScanner, DataManager: 存储扫描结果到缓存
        FileScanner -> CacheManager: storeScanResult(path, virusName, sha256)
        CacheManager -> DataManager: saveCacheData(sha256, virusName, timestamp)
        DataManager -> DatabaseHelper: insertCacheData(sha256, virusName, timestamp)
        note right of DatabaseHelper
            缓存Key = SHA256
            缓存Value = {virusName, timestamp}
        end note
        
        FileScanner --> Scanner: ResultScan(扫描结果)
    end
    
    Scanner -> Client: scanFileFinish(index, path, result)
end

== 扫描完成与缓存清理 ==
Scanner -> Client: scanFinish()

note over AVLEngine, DatabaseHelper: 缓存维护阶段
AVLEngine -> DataManager: maintainDatabase()
DataManager -> DatabaseHelper: maintainDatabase()

note over DatabaseHelper: 缓存清理策略
DatabaseHelper -> DatabaseHelper: 1. 按时间清理：删除超过7天的缓存
note right of DatabaseHelper
    DELETE FROM cache WHERE
    (currentTime - timestamp) > 7天
end note

DatabaseHelper -> DatabaseHelper: 2. 按数量清理：超过1000条删除最旧记录
note right of DatabaseHelper
    如果count > 1000，
    删除最旧的 (count-1000) 条记录
end note

DatabaseHelper --> DataManager: 清理完成
DataManager --> AVLEngine: 维护完成

@enduml
