package com.antiy.demo.adapter

import ScanResultItem
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.antiy.demo.R
import com.antiy.demo.databinding.ItemScanResultBinding

class ScanResultAdapter : RecyclerView.Adapter<ScanResultAdapter.ViewHolder>() {

    private val items = mutableListOf<ScanResultItem>()

    fun addItem(item: ScanResultItem) {
        items.add(0, item) // 添加到列表开头
        notifyItemInserted(0)
    }

    fun clear() {
        items.clear()
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding = ItemScanResultBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: <PERSON>Holder, position: Int) {
        holder.bind(items[position])
    }

    override fun getItemCount(): Int = items.size

    class ViewHolder(private val binding: ItemScanResultBinding) : 
        RecyclerView.ViewHolder(binding.root) {
        
        private fun formatFileSize(size: Long): String {
            return when {
                size < 1024 -> "$size B"
                size < 1024 * 1024 -> String.format("%.1f KB", size / 1024f)
                size < 1024 * 1024 * 1024 -> String.format("%.1f MB", size / (1024f * 1024f))
                else -> String.format("%.1f GB", size / (1024f * 1024f * 1024f))
            }
        }
        
        fun bind(item: ScanResultItem) {
            binding.apply {
                tvFileName.text = item.fileName
                tvFileSize.text = formatFileSize(item.fileSize)
                tvStatus.apply {
                    text = if (!item.result.isMalicious) "安全" else "发现威胁"
                    setTextColor(resources.getColor(
                        if (!item.result.isMalicious) R.color.safe_green else R.color.danger_red,
                        null
                    ))
                }
            }
        }
    }
} 