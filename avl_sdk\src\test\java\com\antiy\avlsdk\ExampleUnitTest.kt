package com.antiy.avlsdk

import com.antiy.avlsdk.utils.VirusInfoUtil
import org.junit.Test

/**
 * Example local unit test, which will execute on the development machine (host).
 *
 * See [testing documentation](http://d.android.com/tools/testing).
 */
class ExampleUnitTest {
    @Test
    fun testParseVirusName() {
        val info = VirusInfoUtil.parseVirusName("PornWare/Android.GSexplayer.hm[exp,rog]")
        println(info.category)
        println(info.riskLevel)
        println(info.behaviors)
        println(info.suggestion)
    }
}