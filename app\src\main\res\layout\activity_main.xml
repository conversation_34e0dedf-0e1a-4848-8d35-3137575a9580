<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".activity.MainActivity">

    <EditText
        android:id="@+id/et_scan_path"
        android:layout_width="0dp"
        android:layout_height="60dp"
        android:text="/sdcard/samples/apk/100"
        android:autofillHints="请输入扫描地址"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/btn_scan"
        />

    <Button
        android:id="@+id/btn_scan"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="扫描"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/et_scan_path"
        />

    <Button
        android:id="@+id/btn_scan_pause"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="暂停扫描"
        android:layout_marginTop="10dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/et_scan_path"
        app:layout_constraintEnd_toStartOf="@+id/btn_scan_resume"
        />

    <Button
        android:id="@+id/btn_scan_resume"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="恢复扫描"
        app:layout_constraintStart_toEndOf="@+id/btn_scan_pause"
        app:layout_constraintTop_toTopOf="@+id/btn_scan_pause"
        app:layout_constraintBottom_toBottomOf="@+id/btn_scan_pause"
        app:layout_constraintEnd_toStartOf="@+id/btn_scan_stop"
        />

    <Button
        android:id="@+id/btn_scan_stop"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="停止扫描"
        app:layout_constraintStart_toEndOf="@+id/btn_scan_resume"
        app:layout_constraintTop_toTopOf="@+id/btn_scan_pause"
        app:layout_constraintBottom_toBottomOf="@+id/btn_scan_pause"
        app:layout_constraintEnd_toEndOf="parent"
        />

    <Button
        android:id="@+id/btn_update_virus"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="更新病毒库"
        android:layout_marginTop="10dp"
        app:layout_constraintStart_toStartOf="@+id/btn_scan_pause"
        app:layout_constraintTop_toBottomOf="@+id/btn_scan_pause"
        />

    <Button
        android:id="@+id/btn_scan_24_hours"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="扫描24小时"
        android:layout_marginStart="10dp"
        app:layout_constraintStart_toEndOf="@+id/btn_update_virus"
        app:layout_constraintTop_toTopOf="@+id/btn_update_virus"
        />

    <Button
        android:id="@+id/btn_hand_update_virus"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="手动更新病毒库"
        app:layout_constraintStart_toStartOf="@+id/btn_scan_stop"
        app:layout_constraintTop_toTopOf="@+id/btn_scan_24_hours"
        app:layout_constraintEnd_toEndOf="@+id/btn_scan_stop"

        />

    <Button
        android:id="@+id/btn_uuid"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="UUID"
        app:layout_constraintStart_toStartOf="@+id/btn_update_virus"
        app:layout_constraintTop_toBottomOf="@+id/btn_update_virus"

        />
    <Button
        android:id="@+id/btn_concurrent_scan"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="并发扫描文件"
        android:layout_marginStart="10dp"
        app:layout_constraintTop_toTopOf="@+id/btn_uuid"
        app:layout_constraintStart_toEndOf="@+id/btn_uuid"
        />
    <Button
        android:id="@+id/btn_silent_scan"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="静默扫描"
        app:layout_constraintStart_toEndOf="@+id/btn_concurrent_scan"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/btn_hand_update_virus"
        />
    <Button
        android:id="@+id/btn_cpu_pause"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="性能暂停"
        app:layout_constraintTop_toBottomOf="@+id/btn_uuid"
        app:layout_constraintStart_toStartOf="@+id/btn_uuid"
        />

    <Button
        android:id="@+id/btn_cpu_continue"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="性能继续"
        android:layout_marginStart="10dp"
        app:layout_constraintTop_toBottomOf="@+id/btn_uuid"
        app:layout_constraintStart_toEndOf="@+id/btn_cpu_pause"
        />
    <Button
        android:id="@+id/btn_clear_cache"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="清空缓存"
        android:layout_marginStart="10dp"
        app:layout_constraintTop_toBottomOf="@+id/btn_uuid"
        app:layout_constraintStart_toEndOf="@+id/btn_cpu_continue"
        />
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="20dp"
        app:layout_constraintTop_toBottomOf="@+id/btn_cpu_pause"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        />
</androidx.constraintlayout.widget.ConstraintLayout>