package com.antiy.demo.activity

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Build
import android.view.LayoutInflater
import android.view.MenuItem
import android.view.WindowManager
import androidx.core.content.ContextCompat
import androidx.core.view.WindowCompat
import com.antiy.avlsdk.AVLEngine
import com.antiy.demo.BuildConfig
import com.antiy.demo.R
import com.antiy.demo.base.BaseActivity
import com.antiy.demo.databinding.ActivityAboutBinding

class AboutActivity : BaseActivity<ActivityAboutBinding>() {

    override fun getViewBinding(inflater: LayoutInflater): ActivityAboutBinding {
        return ActivityAboutBinding.inflate(inflater)
    }

    companion object {
        fun start(context: Context) {
            context.startActivity(Intent(context, AboutActivity::class.java))
        }
    }

    override fun initView() {
        // 设置沉浸式状态栏
        setupStatusBar()
        
        // 隐藏ActionBar
        supportActionBar?.hide()
        
        setupVersionInfo()
        setupDeveloperInfo()
        setupClickListeners()
        setupBackButton()
    }
    
    private fun setupStatusBar() {
        // 确保状态栏正确显示
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            // 设置状态栏为亮色模式，图标为深色
            WindowCompat.getInsetsController(window, window.decorView)?.apply {
                isAppearanceLightStatusBars = false // 设置为false，因为我们有蓝色背景，需要白色状态栏图标
            }
            
            // 设置状态栏为透明
            window.statusBarColor = ContextCompat.getColor(this, android.R.color.transparent)
            
            // 设置内容延伸到状态栏下方，但状态栏仍然可见
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
            
            // 使用CoordinatorLayout时，设置为false让内容延伸到系统栏下方
            WindowCompat.setDecorFitsSystemWindows(window, false)
        }
    }
    
    private fun setupBackButton() {
        // 根据截图，我们需要添加一个返回按钮
        // 假设顶部蓝色区域已经包含了一个返回按钮，例如id为btn_back
        try {
            binding.btnBack?.setOnClickListener {
                finish()
            }
        } catch (e: Exception) {
            // 如果布局中没有相应的按钮，这里会发生异常，忽略即可
        }
    }

    @SuppressLint("SetTextI18n")
    private fun setupVersionInfo() {
        // App version
        binding.tvAppVersion.text = "版本 ${BuildConfig.VERSION_NAME}"
        
        // Engine version
        binding.tvEngineVersion.text = "引擎版本：1.5.3"
        
        // Virus database version and update time
        val updateTime = "2024.03.15" // 应从实际数据源获取
        binding.tvVirusDbVersion.text = "病毒库：$updateTime"
        binding.tvLastUpdate.text = "最后更新：$updateTime"
    }

    private fun setupDeveloperInfo() {
        binding.tvDeveloper.text = "开发者：XX科技有限公司"
        binding.tvWebsite.text = "官方网站：www.example.com"
        binding.tvEmail.text = "联系邮箱：<EMAIL>"
    }

    private fun setupClickListeners() {
        binding.tvServiceTerms.setOnClickListener {
            // TODO: 跳转到服务条款页面
            // 可以使用WebView加载服务条款
        }

        binding.tvPrivacyPolicy.setOnClickListener {
            // TODO: 跳转到隐私政策页面
            // 可以使用WebView加载隐私政策
        }

        binding.tvOpenSource.setOnClickListener {
            // TODO: 跳转到开源许可页面
            // 可以创建一个显示开源许可的Activity
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (item.itemId == android.R.id.home) {
            finish()
            return true
        }
        return super.onOptionsItemSelected(item)
    }
} 