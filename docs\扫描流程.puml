@startuml
title 文件夹病毒扫描流程图

:选择文件夹;
if(文件个数 > 阈值) then (是)
    :云扫描;
    stop
else(否)
    :单个文件扫描;
    repeat:取出文件;
        if(文件类型) then (是)
            if(是否有缓存) then(是)
                :从缓存取出扫描结果;
            else (否)
                if (是否在黑白名单) is (是) then
                  :返回结果;
                else (否)
                    if (文件大小 > 阈值) is (是) then
                      :云扫描;
                    else (no)
                      if (文件是APK/JAR/DEX类型) is (是) then
                         :移动引擎扫描;
                      else (否)
                        :PC引擎扫描;
                      endif
                    endif
                endif
            end if
        else (否)
            stop
        end if
    repeat while (还有文件?)
        :扫描完成;
        stop
endif
@enduml
