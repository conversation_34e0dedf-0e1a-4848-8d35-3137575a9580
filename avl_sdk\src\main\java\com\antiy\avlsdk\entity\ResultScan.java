package com.antiy.avlsdk.entity;

/**
 * 2 * Copyright (C), 2020-2024
 * 3 * FileName: ResultScan
 * 4 * Author: wangbiao
 * 5 * Date: 2024/9/2 10:38
 * 6 * Description: 扫描结果
 * 10
 */
public class ResultScan {
    // 是否是恶意文件
    public boolean isMalicious;
    // 病毒名
    public String virusName;
    // 文件哈希
    public String sha256sum;
    // 是否云查
    public boolean isCloudScan;

    public String errMsg;

    public ResultScan() {
    }

    public ResultScan(boolean isMalicious) {
        this.isMalicious = isMalicious;
    }

    public ResultScan(String errMsg) {
        this.errMsg = errMsg;
    }

    public ResultScan(boolean isMalicious, String virusName, String sha256sum, boolean isCloudScan) {
        this.isMalicious = isMalicious;
        this.virusName = virusName;
        this.sha256sum = sha256sum;
        this.isCloudScan = isCloudScan;
    }

    @Override
    public String toString() {
        return "ResultScan{" +
                "isMalicious=" + isMalicious +
                ", virusName='" + virusName + '\'' +
                ", sha256sum='" + sha256sum + '\'' +
                ", isCloudScan=" + isCloudScan +
                ", errMsg='" + errMsg + '\'' +
                '}';
    }
}
