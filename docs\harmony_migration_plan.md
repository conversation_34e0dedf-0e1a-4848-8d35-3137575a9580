# AVL SDK迁移至HarmonyOS Next平台评估报告

## 一、项目概述

将现有的Android版AVL SDK迁移到HarmonyOS Next平台，需要对整体架构和具体模块进行调整和重构。

## 二、技术调研期（1天）

1. **HarmonyOS开发环境搭建（1天）**
   - DevEco Studio环境配置
   - HarmonyOS SDK安装与测试
   - 模拟器和真机调试环境搭建


## 三、核心模块迁移（天）

### 3.1 基础框架迁移（5天）
- **API模块改造（3天）**
  - Stage模型适配
  - 权限管理适配

- **初始化模块重构（2天）**

### 3.2 核心功能模块（15天）

1. **激活模块（2天）**
   - 设备标识获取方式改造
   - 授权机制适配
   - 激活流程重构

2. **扫描模块（5天）**
   - 文件系统访问适配
   - 扫描引擎移植

3. **病毒库管理模块（5天）**
   - 存储方式改造
   - 更新机制适配
   - 版本管理重构

4. **性能监控模块（3天）**
   - 系统监控API适配

### 3.3 基础支持模块（6天）

1. **存储模块（2天）**
   - 数据持久化方案改造
   - 加密存储适配

2. **日志模块（2天）**
   - 日志系统适配
   - 日志导出功能改造

3. **通信模块（2天）**
   - 网络请求框架迁移

## 四、Native引擎适配（可以同时待定）

1. **编译系统改造**
2. **JNI层改造**
3. **引擎核心适配**



**总计工期：至少27个工作日 **


