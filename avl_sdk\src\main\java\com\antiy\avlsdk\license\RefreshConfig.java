package com.antiy.avlsdk.license;

/**
 * License刷新配置类
 * 集中管理license刷新相关的配置参数
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class RefreshConfig {
    
    // 默认配置常量
    public static final long DEFAULT_REFRESH_INTERVAL = 7 * 24 * 60 * 60 * 1000L; // 7天
    public static final long DEFAULT_REFRESH_DELAY = 30 * 1000L; // 30秒
    public static final long DEFAULT_RETRY_INTERVAL = 60 * 1000L; // 60秒
    public static final int DEFAULT_MAX_RETRIES = 3; // 最大重试3次
    public static final long DEFAULT_REFRESH_TIMEOUT = 60L; // 60秒超时
    public static final long DEFAULT_NETWORK_WAIT_TIMEOUT = 30 * 1000L; // 30秒网络等待超时
    public static final long DEFAULT_NETWORK_CHECK_INTERVAL = 5 * 1000L; // 5秒网络检查间隔
    
    // 配置参数
    private final long refreshInterval;
    private final long refreshDelay;
    private final long retryInterval;
    private final int maxRetries;
    private final long refreshTimeout;
    private final long networkWaitTimeout;
    private final long networkCheckInterval;
    private final boolean enableNetworkStabilityCheck;
    private final boolean enableForceRefreshOnNetworkChange;
    
    /**
     * 私有构造函数，使用Builder模式创建
     */
    private RefreshConfig(Builder builder) {
        this.refreshInterval = builder.refreshInterval;
        this.refreshDelay = builder.refreshDelay;
        this.retryInterval = builder.retryInterval;
        this.maxRetries = builder.maxRetries;
        this.refreshTimeout = builder.refreshTimeout;
        this.networkWaitTimeout = builder.networkWaitTimeout;
        this.networkCheckInterval = builder.networkCheckInterval;
        this.enableNetworkStabilityCheck = builder.enableNetworkStabilityCheck;
        this.enableForceRefreshOnNetworkChange = builder.enableForceRefreshOnNetworkChange;
    }
    
    // Getter方法
    public long getRefreshInterval() { return refreshInterval; }
    public long getRefreshDelay() { return refreshDelay; }
    public long getRetryInterval() { return retryInterval; }
    public int getMaxRetries() { return maxRetries; }
    public long getRefreshTimeout() { return refreshTimeout; }
    public long getNetworkWaitTimeout() { return networkWaitTimeout; }
    public long getNetworkCheckInterval() { return networkCheckInterval; }
    public boolean isNetworkStabilityCheckEnabled() { return enableNetworkStabilityCheck; }
    public boolean isForceRefreshOnNetworkChangeEnabled() { return enableForceRefreshOnNetworkChange; }
    
    /**
     * 获取默认配置
     * 
     * @return 默认的RefreshConfig实例
     */
    public static RefreshConfig getDefault() {
        return new Builder().build();
    }
    
    /**
     * 获取车机优化配置
     * 针对车机环境优化的配置
     * 
     * @return 车机优化的RefreshConfig实例
     */
    public static RefreshConfig getCarOptimized() {
        return new Builder()
                .setRefreshInterval(3 * 24 * 60 * 60 * 1000L) // 3天刷新间隔
                .setRefreshDelay(60 * 1000L) // 60秒延迟，等待车机网络稳定
                .setRetryInterval(2 * 60 * 1000L) // 2分钟重试间隔
                .setMaxRetries(5) // 最大重试5次
                .setNetworkWaitTimeout(60 * 1000L) // 60秒网络等待
                .setEnableNetworkStabilityCheck(true) // 启用网络稳定性检查
                .setEnableForceRefreshOnNetworkChange(false) // 禁用网络变化强制刷新
                .build();
    }
    
    /**
     * 获取快速配置
     * 适用于需要快速响应的场景
     * 
     * @return 快速响应的RefreshConfig实例
     */
    public static RefreshConfig getFastResponse() {
        return new Builder()
                .setRefreshDelay(5 * 1000L) // 5秒延迟
                .setRetryInterval(30 * 1000L) // 30秒重试间隔
                .setRefreshTimeout(30L) // 30秒超时
                .setNetworkWaitTimeout(15 * 1000L) // 15秒网络等待
                .build();
    }
    
    @Override
    public String toString() {
        return "RefreshConfig{" +
                "refreshInterval=" + (refreshInterval / 1000 / 60 / 60) + "h, " +
                "refreshDelay=" + (refreshDelay / 1000) + "s, " +
                "retryInterval=" + (retryInterval / 1000) + "s, " +
                "maxRetries=" + maxRetries + ", " +
                "refreshTimeout=" + refreshTimeout + "s, " +
                "networkWaitTimeout=" + (networkWaitTimeout / 1000) + "s, " +
                "networkStabilityCheck=" + enableNetworkStabilityCheck + ", " +
                "forceRefreshOnNetworkChange=" + enableForceRefreshOnNetworkChange +
                '}';
    }
    
    /**
     * Builder模式构建器
     */
    public static class Builder {
        private long refreshInterval = DEFAULT_REFRESH_INTERVAL;
        private long refreshDelay = DEFAULT_REFRESH_DELAY;
        private long retryInterval = DEFAULT_RETRY_INTERVAL;
        private int maxRetries = DEFAULT_MAX_RETRIES;
        private long refreshTimeout = DEFAULT_REFRESH_TIMEOUT;
        private long networkWaitTimeout = DEFAULT_NETWORK_WAIT_TIMEOUT;
        private long networkCheckInterval = DEFAULT_NETWORK_CHECK_INTERVAL;
        private boolean enableNetworkStabilityCheck = false;
        private boolean enableForceRefreshOnNetworkChange = false;
        
        public Builder setRefreshInterval(long refreshInterval) {
            this.refreshInterval = refreshInterval;
            return this;
        }
        
        public Builder setRefreshDelay(long refreshDelay) {
            this.refreshDelay = refreshDelay;
            return this;
        }
        
        public Builder setRetryInterval(long retryInterval) {
            this.retryInterval = retryInterval;
            return this;
        }
        
        public Builder setMaxRetries(int maxRetries) {
            this.maxRetries = maxRetries;
            return this;
        }
        
        public Builder setRefreshTimeout(long refreshTimeout) {
            this.refreshTimeout = refreshTimeout;
            return this;
        }
        
        public Builder setNetworkWaitTimeout(long networkWaitTimeout) {
            this.networkWaitTimeout = networkWaitTimeout;
            return this;
        }
        
        public Builder setNetworkCheckInterval(long networkCheckInterval) {
            this.networkCheckInterval = networkCheckInterval;
            return this;
        }
        
        public Builder setEnableNetworkStabilityCheck(boolean enable) {
            this.enableNetworkStabilityCheck = enable;
            return this;
        }
        
        public Builder setEnableForceRefreshOnNetworkChange(boolean enable) {
            this.enableForceRefreshOnNetworkChange = enable;
            return this;
        }
        
        public RefreshConfig build() {
            return new RefreshConfig(this);
        }
    }
}
