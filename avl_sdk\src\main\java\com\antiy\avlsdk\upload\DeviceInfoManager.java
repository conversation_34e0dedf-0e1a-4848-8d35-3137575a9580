package com.antiy.avlsdk.upload;

import android.content.Context;
import android.provider.Settings;
import android.text.TextUtils;

/**
 * 设备信息管理类
 * 负责获取设备相关信息，包括设备ID和车架号ID
 * 
 */
public class DeviceInfoManager {

    /**
     * 获取设备唯一标识（使用ANDROID_ID）
     * @param context 上下文
     * @return 设备唯一标识，可能为null或空字符串
     */
    public static String getDeviceId(Context context) {
        return getAndroidId(context);
    }
    
    /**
     * 获取车架号ID（来源于AVLEngine初始化参数）
     * @param vehicleId AVLEngine初始化时传入的车架号ID
     * @return 车架号ID
     */
    public static String getVehicleId(String vehicleId) {
        if (!TextUtils.isEmpty(vehicleId)) {
            return vehicleId;
        }
        throw new IllegalArgumentException("Vehicle ID cannot be null or empty");
    }
    
    /**
     * 获取ANDROID_ID
     * @param context 上下文
     * @return ANDROID_ID，可能为null或空字符串
     */
    private static String getAndroidId(Context context) {
        try {
            return Settings.Secure.getString(context.getContentResolver(), Settings.Secure.ANDROID_ID);
        } catch (Exception e) {
            return null;
        }
    }
}
