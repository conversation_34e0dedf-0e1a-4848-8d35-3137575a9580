package com.antiy.avlsdk.utils;

import android.util.Log;

import com.antiy.avlsdk.AVLEngine;

import org.apache.commons.compress.archivers.tar.TarArchiveEntry;
import org.apache.commons.compress.archivers.tar.TarArchiveInputStream;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;

/**
 * Author: wangbiao
 * Date: 2024/9/26 16:59
 * Description: 解压工具类
 */
public class ZipExtractor {

    private final String TAG = ZipExtractor.class.getSimpleName();

    public List<String> extractZipPackage(String zipPath, String destDir) {
        // 判断文件是否存在
        File updatePackageFile = new File(zipPath);
        if (!updatePackageFile.exists()) {
            Log.e(TAG, "Update package not found: " + zipPath);
            return new ArrayList<>();
        }
        // 判断解压目录是否存在
        File destFile = new File(destDir);
        if (!destFile.exists()) {
            destFile.mkdir();
        }
        List<String> paths = new ArrayList<>();
        try (ZipFile zipFile = new ZipFile(zipPath)) {
            Enumeration<? extends ZipEntry> entries = zipFile.entries();

            while (entries.hasMoreElements()) {
                ZipEntry entry = entries.nextElement();
                File entryDestination = new File(destDir, entry.getName());

                if (entry.isDirectory()) {
                    // 如果是目录，创建它
                    if (!entryDestination.exists()) {
                        entryDestination.mkdirs();
                    }
                } else {
                    // 如果是文件，写入它
                    File parentFile = entryDestination.getParentFile();
                    if (!parentFile.exists()) {
                        parentFile.mkdirs();
                    }
                    try (FileOutputStream fos = new FileOutputStream(entryDestination)) {
                        byte[] buffer = new byte[4096];
                        int len;
                        try (InputStream zis = zipFile.getInputStream(entry)) {
                            while ((len = zis.read(buffer)) > 0) {
                                fos.write(buffer, 0, len);
                            }
                        }
                    }
                    paths.add(entryDestination.getAbsolutePath());
                }
            }
            AVLEngine.Logger.info("解压路径:" + paths);
        } catch (IOException e) {
            AVLEngine.Logger.error("unzip " + zipPath + " fail");
            e.printStackTrace();
        }

        return paths;

    }

    /**
     * 生成Android和Pc的解压路径
     *
     * @return
     */
    public String[] generateAndroidPcPath(String updateZipPath, List<String> paths) {
        String androidPackageName = updateZipPath.substring(updateZipPath.lastIndexOf("/") + 1, updateZipPath.lastIndexOf(".")).split("_")[0];
        if (paths.get(0).contains(androidPackageName)) {
            return new String[]{paths.get(0), paths.get(1)};
        } else {
            return new String[]{paths.get(1), paths.get(0)};
        }
    }



    /**
     * 提取tar要搜包的内容，即解压
     *
     * @param tarFilePath
     * @param outputDir
     * @throws IOException
     */
    public void extractTarContent(String tarFilePath, String outputDir) throws IOException {
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            try (TarArchiveInputStream tarInput = new TarArchiveInputStream(Files.newInputStream(Paths.get(tarFilePath)))) {
                TarArchiveEntry entry;
                while ((entry = tarInput.getNextTarEntry()) != null) {
                    File outputFile = new File(outputDir, entry.getName());
                    if (entry.isDirectory()) {
                        outputFile.mkdirs();
                    } else {
                        outputFile.getParentFile().mkdirs();
                        try (FileOutputStream fos = new FileOutputStream(outputFile)) {
                            byte[] buffer = new byte[1024];
                            int len;
                            while ((len = tarInput.read(buffer)) != -1) {
                                fos.write(buffer, 0, len);
                            }
                        }
                    }
                }
            }
        }
    }







}
