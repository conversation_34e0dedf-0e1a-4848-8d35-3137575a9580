<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CCCJ SDK 开发工作报告 - 项目概览</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- PptxGenJS库 -->
    <script src="https://cdn.jsdelivr.net/npm/pptxgenjs@3.12.0/dist/pptxgen.bundle.js"></script>
    <!-- PPT导出模块 -->
    <script src="ppt-exporter.js"></script>
    <style>
        /* 自定义动画 */
        @keyframes slideIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .slide-in {
            animation: slideIn 0.8s ease-out forwards;
        }

        .fade-in {
            animation: fadeIn 1s ease-out forwards;
        }

        .card-hover {
            transition: all 0.3s ease;
        }

        .card-hover:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .chart-bar {
            transition: width 1s ease-out;
        }

        .metric-counter {
            font-family: 'Courier New', monospace;
        }
    </style>
</head>
<body class="gradient-bg min-h-screen p-6">
    <div class="max-w-7xl mx-auto">
        <!-- 页面标题 -->
        <div class="text-center mb-8 fade-in">
            <h1 class="text-4xl font-bold text-white mb-2">
                <i class="fas fa-shield-alt mr-3"></i>
                CCCJ SDK 开发工作报告
            </h1>
            <p class="text-xl text-white/80">项目概览与核心成果展示</p>
            <div class="mt-4 text-white/70">
                <i class="fas fa-calendar-alt mr-2"></i>
                报告期间：2025年5月8日 - 5月26日
            </div>
        </div>

        <!-- 主要指标卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <!-- 提交数量 -->
            <div class="bg-white rounded-2xl p-6 shadow-lg card-hover slide-in" style="animation-delay: 0.2s;">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm">总提交数</p>
                        <p class="text-3xl font-bold text-blue-600 metric-counter" id="commits-counter">0</p>
                    </div>
                    <div class="bg-blue-100 p-3 rounded-full">
                        <i class="fas fa-code-branch text-blue-600 text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- 功能添加 -->
            <div class="bg-white rounded-2xl p-6 shadow-lg card-hover slide-in" style="animation-delay: 0.4s;">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm">新增功能</p>
                        <p class="text-3xl font-bold text-green-600 metric-counter" id="features-counter">0</p>
                    </div>
                    <div class="bg-green-100 p-3 rounded-full">
                        <i class="fas fa-plus-circle text-green-600 text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- 问题修复 -->
            <div class="bg-white rounded-2xl p-6 shadow-lg card-hover slide-in" style="animation-delay: 0.6s;">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm">问题修复</p>
                        <p class="text-3xl font-bold text-red-600 metric-counter" id="fixes-counter">0</p>
                    </div>
                    <div class="bg-red-100 p-3 rounded-full">
                        <i class="fas fa-bug text-red-600 text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- 重构优化 -->
            <div class="bg-white rounded-2xl p-6 shadow-lg card-hover slide-in" style="animation-delay: 0.8s;">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm">重构优化</p>
                        <p class="text-3xl font-bold text-purple-600 metric-counter" id="refactor-counter">0</p>
                    </div>
                    <div class="bg-purple-100 p-3 rounded-full">
                        <i class="fas fa-sync-alt text-purple-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主要成果展示 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- 核心功能模块 -->
            <div class="bg-white rounded-2xl p-8 shadow-lg slide-in" style="animation-delay: 1s;">
                <h2 class="text-2xl font-bold text-gray-800 mb-6">
                    <i class="fas fa-rocket text-blue-600 mr-3"></i>
                    核心功能增强
                </h2>

                <div class="space-y-4">
                    <div class="flex items-start p-4 bg-blue-50 rounded-xl">
                        <div class="bg-blue-600 p-2 rounded-lg mr-4">
                            <i class="fas fa-search text-white"></i>
                        </div>
                        <div>
                            <h3 class="font-semibold text-gray-800">文件扫描功能</h3>
                            <p class="text-gray-600 text-sm">ScanFragment新增UI界面和异步扫描逻辑</p>
                        </div>
                    </div>

                    <div class="flex items-start p-4 bg-green-50 rounded-xl">
                        <div class="bg-green-600 p-2 rounded-lg mr-4">
                            <i class="fas fa-cloud text-white"></i>
                        </div>
                        <div>
                            <h3 class="font-semibold text-gray-800">云查杀阈值设置</h3>
                            <p class="text-gray-600 text-sm">可配置云查杀文件大小阈值，提升扫描效率</p>
                        </div>
                    </div>

                    <div class="flex items-start p-4 bg-purple-50 rounded-xl">
                        <div class="bg-purple-600 p-2 rounded-lg mr-4">
                            <i class="fas fa-link text-white"></i>
                        </div>
                        <div>
                            <h3 class="font-semibold text-gray-800">内容URI处理</h3>
                            <p class="text-gray-600 text-sm">支持外部应用传入的内容URI处理</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 技术改进亮点 -->
            <div class="bg-white rounded-2xl p-8 shadow-lg slide-in" style="animation-delay: 1.2s;">
                <h2 class="text-2xl font-bold text-gray-800 mb-6">
                    <i class="fas fa-cogs text-orange-600 mr-3"></i>
                    技术改进亮点
                </h2>

                <div class="space-y-6">
                    <!-- 性能优化进度条 -->
                    <div>
                        <div class="flex justify-between mb-2">
                            <span class="text-sm font-medium text-gray-700">性能优化</span>
                            <span class="text-sm text-gray-500">85%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2.5">
                            <div class="bg-blue-600 h-2.5 rounded-full chart-bar" style="width: 0%" data-width="85%"></div>
                        </div>
                    </div>

                    <!-- 代码质量进度条 -->
                    <div>
                        <div class="flex justify-between mb-2">
                            <span class="text-sm font-medium text-gray-700">代码质量</span>
                            <span class="text-sm text-gray-500">92%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2.5">
                            <div class="bg-green-600 h-2.5 rounded-full chart-bar" style="width: 0%" data-width="92%"></div>
                        </div>
                    </div>

                    <!-- 用户体验进度条 -->
                    <div>
                        <div class="flex justify-between mb-2">
                            <span class="text-sm font-medium text-gray-700">用户体验</span>
                            <span class="text-sm text-gray-500">78%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2.5">
                            <div class="bg-purple-600 h-2.5 rounded-full chart-bar" style="width: 0%" data-width="78%"></div>
                        </div>
                    </div>

                    <!-- 关键改进点 -->
                    <div class="mt-6 p-4 bg-orange-50 rounded-xl">
                        <h4 class="font-semibold text-gray-800 mb-2">关键改进</h4>
                        <ul class="text-sm text-gray-600 space-y-1">
                            <li>• 文件类型识别逻辑重构</li>
                            <li>• 黑白名单匹配算法优化</li>
                            <li>• 日志系统规范化</li>
                            <li>• 内存泄露问题修复</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- 导出功能区 -->
        <div class="bg-white/10 rounded-2xl p-6 mb-8 fade-in" style="animation-delay: 1.2s;">
            <h3 class="text-xl font-bold text-white mb-4">
                <i class="fas fa-download mr-3"></i>
                导出功能
            </h3>
            <div class="flex flex-wrap gap-4">
                <button onclick="exportToPPT.exportCurrent(1)"
                        class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-xl transition-all duration-300 flex items-center shadow-lg">
                    <i class="fas fa-file-powerpoint mr-2"></i>
                    导出当前页
                </button>
                <button onclick="exportToPPT.exportFull()"
                        class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-xl transition-all duration-300 flex items-center shadow-lg">
                    <i class="fas fa-file-export mr-2"></i>
                    导出完整演示文稿
                </button>
                <button onclick="window.location.href='export.html'"
                        class="bg-orange-600 hover:bg-orange-700 text-white px-6 py-3 rounded-xl transition-all duration-300 flex items-center shadow-lg">
                    <i class="fas fa-cog mr-2"></i>
                    导出中心
                </button>
            </div>
            <p class="text-white/70 text-sm mt-3">
                <i class="fas fa-info-circle mr-2"></i>
                点击按钮将网页PPT导出为PowerPoint文件(.pptx)
            </p>
        </div>

        <!-- 页面导航 -->
        <div class="flex justify-between items-center mt-8 fade-in" style="animation-delay: 1.4s;">
            <div class="text-white/70">
                <i class="fas fa-file-powerpoint mr-2"></i>
                第 1 页 / 共 2 页
            </div>
            <button onclick="window.location.href='slide2.html'"
                    class="bg-white/20 hover:bg-white/30 text-white px-6 py-3 rounded-xl transition-all duration-300 flex items-center">
                下一页
                <i class="fas fa-arrow-right ml-2"></i>
            </button>
        </div>
    </div>

    <script>
        // 数字动画计数器
        function animateCounter(elementId, targetValue, duration = 2000) {
            const element = document.getElementById(elementId);
            const startValue = 0;
            const increment = targetValue / (duration / 16);
            let currentValue = startValue;

            const timer = setInterval(() => {
                currentValue += increment;
                if (currentValue >= targetValue) {
                    element.textContent = targetValue;
                    clearInterval(timer);
                } else {
                    element.textContent = Math.floor(currentValue);
                }
            }, 16);
        }

        // 进度条动画
        function animateProgressBars() {
            const bars = document.querySelectorAll('.chart-bar');
            bars.forEach((bar, index) => {
                setTimeout(() => {
                    bar.style.width = bar.getAttribute('data-width');
                }, 500 + index * 200);
            });
        }

        // 页面加载完成后执行动画
        document.addEventListener('DOMContentLoaded', function() {
            // 延迟执行数字动画
            setTimeout(() => {
                animateCounter('commits-counter', 20, 1500);
                animateCounter('features-counter', 6, 1500);
                animateCounter('fixes-counter', 4, 1500);
                animateCounter('refactor-counter', 8, 1500);
            }, 800);

            // 延迟执行进度条动画
            setTimeout(() => {
                animateProgressBars();
            }, 1500);
        });

        // 鼠标悬停效果
        document.querySelectorAll('.card-hover').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-4px) scale(1.02)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
    </script>
</body>
</html>
