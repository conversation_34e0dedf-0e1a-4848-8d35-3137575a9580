## 构建说明

本项目支持两个版本的 SDK：

1. 长城版本
    - 不包含 PC 和 Mobile 资源
    - 构建命令：`./gradlew buildGreatwallAar`

2. 长安版本
    - 包含 Mobile 资源，不包含 PC 资源
    - 构建命令：`./gradlew buildChanganAar`

### 构建命令

- 构建默认版本（长城版本）：`./gradlew build`
- 构建长城版本：`./gradlew buildGreatwallAar`
- 构建长安版本：`./gradlew buildChanganAar`
- 构建所有版本：`./gradlew buildAllVersions`

构建输出目录：`build/outputs/aar/`


## keystore生成

keytool -genkey -alias avlsdk -keyalg RSA -keysize 8192 -validity 36500 -keystore avlsdk.keystore

输入密钥库口令:  
再次输入新口令:
它们不匹配。请重试
输入密钥库口令:  
再次输入新口令:
您的名字与姓氏是什么?
[Unknown]:  <PERSON>
您的组织单位名称是什么?
[Unknown]:  EngineDevelopment
您的组织名称是什么?
[Unknown]:  Antiy
您所在的城市或区域名称是什么?
[Unknown]:  <PERSON><PERSON>
您所在的省/市/自治区名称是什么?
[Unknown]:  Hubei
该单位的双字母国家/地区代码是什么?
[Unknown]:  CN
CN=Zhang Yueqian, OU=EngineDevelopment, O=Antiy, L=Wuhan, ST=Hubei, C=CN是否正确?
[否]:  Y

正在为以下对象生成 8,192 位RSA密钥对和自签名证书 (SHA512withRSA) (有效期为 36,500 天):
CN=Zhang Yueqian, OU=EngineDevelopment, O=Antiy, L=Wuhan, ST=Hubei, C=CN
