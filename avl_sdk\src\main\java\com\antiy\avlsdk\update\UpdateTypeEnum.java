package com.antiy.avlsdk.update;

/**
 * Author: wang<PERSON>o
 * Date: 2024/10/11 14:30
 * Description:
 */
public enum UpdateTypeEnum {
    NONE(0, "不更新"),
    INCREMENTAL(1, "增量更新"),
    FULL(2, "全量更新");

    private final int code;
    private final String description;

    UpdateTypeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public static UpdateTypeEnum fromCode(int code) {
        for (UpdateTypeEnum type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的更新代码: " + code);
    }
}
