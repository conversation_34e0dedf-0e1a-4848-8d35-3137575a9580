package com.antiy.demo.entity

/**
 * 黑白名单项数据实体
 * @param hash 文件Hash值
 * @param isWhite true表示白名单，false表示黑名单
 */
data class BlackWhiteListItem(
    val hash: String,
    val isWhite: Boolean
) {
    /**
     * 获取类型显示文本
     */
    fun getTypeText(): String {
        return if (!isWhite) "白名单" else "黑名单"
    }
    
    /**
     * 获取类型颜色资源ID
     */
    fun getTypeColorRes(): Int {
        return if (!isWhite) android.R.color.holo_green_dark else android.R.color.holo_red_dark
    }
    
    /**
     * 获取简短的Hash显示（前8位...后8位）
     */
    fun getShortHash(): String {
        return if (hash.length > 16) {
            "${hash.substring(0, 8)}...${hash.substring(hash.length - 8)}"
        } else {
            hash
        }
    }
}
