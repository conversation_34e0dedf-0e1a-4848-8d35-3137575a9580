package com.antiy.avlsdk.auth;

import android.content.Context;

import com.antiy.avlsdk.AVLCoreEngine;
import com.antiy.avlsdk.AVLEngine;
import com.antiy.avlsdk.callback.RequestCallback;
import com.antiy.avlsdk.entity.AuthResponseEntity;
import com.antiy.avlsdk.entity.AuthTokenEntity;
import com.antiy.avlsdk.entity.RequestMethod;
import com.antiy.avlsdk.storage.EncryptorHelper;
import com.antiy.avlsdk.utils.DateUtils;
import com.antiy.avlsdk.utils.JsonUtils;
import com.antiy.avlsdk.utils.SdkConst;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Random;

/**
 * 许可证管理类
 * 负责获取和更新授权信息，包括设备认证、令牌获取和授权信息存储等功能
 *
 * <AUTHOR>
 * @version 1.0
 */
public class LicenseManager {
    private final AuthInfoManager authInfoManager;

    public LicenseManager(Context context) {
        this.authInfoManager = AuthInfoManager.getInstance(context);
    }

    /**
     * 生成0-31范围内的随机数作为认证标志
     * 用于生成认证请求的随机标识符
     *
     * @return 返回0-31之间的随机整数
     */
    private int generateAuthFlag() {
        return new Random().nextInt(32);
    }

    /**
     * 生成客户端标识符
     * 从SDK常量中获取预定义的客户端ID
     *
     * @return 返回客户端标识符字符串
     */
    private String generateClientId() {
        return SdkConst.CLIENT_ID;
    }

    /**
     * 生成认证请求内容
     * 使用设备唯一标识、客户端ID、请求时间和认证标志生成加密的认证请求
     *
     * @param uuid 设备唯一标识
     * @param clientId 客户端标识符
     * @param requestTime 请求时间戳
     * @param flag 认证标志（0-31的随机数）
     * @return 返回加密后的认证请求内容
     */
    private String generateAuthRequest(String uuid, String clientId, String requestTime, int flag) {
        AVLCoreEngine.getInstance().setupAuthParams(uuid, clientId);
        String content = AVLCoreEngine.getInstance().generateAuthRequest(requestTime, flag);
        return content;
    }

    /**
     * 获取当前请求时间
     * 使用DateUtils工具类获取当前时间的格式化字符串
     *
     * @return 返回格式化的当前时间字符串
     */
    private String requestTime() {
        return DateUtils.getCurrentDate();
    }

    /**
     * 请求并获取认证令牌
     * 通过设备唯一标识向服务器请求认证令牌，包括以下步骤：
     * 1. 生成认证标志和客户端ID
     * 2. 构建加密的认证请求
     * 3. 发送HTTP请求获取响应
     * 4. 解密响应获取认证令牌
     * 5. 保存认证信息到安全存储
     *
     * @param uuid 设备唯一标识，用于标识请求设备
     * @throws JSONException 当JSON解析失败时抛出
     * @throws Exception 当请求过程中发生其他异常时抛出
     */
    public void requestAuthToken(String uuid) {
        try {
            int flag = generateAuthFlag();
            AVLEngine.Logger.error("license uuid：" + uuid);

            // 生成新的client_id
            String clientId = generateClientId();
            String requestTime = requestTime();
            String content = generateAuthRequest(uuid,clientId,requestTime, flag);

            // 构建请求参数
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("flag", String.valueOf(flag));
            jsonObject.put("uuid", EncryptorHelper.md5(uuid).toUpperCase());
            jsonObject.put("requestTime", requestTime);
            jsonObject.put("content", content);

            // 发送请求
            AVLEngine.getInstance().getNetworkManager().request(
                SdkConst.AUTH_URL,
                RequestMethod.POST,
                jsonObject,
                new RequestCallback() {
                    @Override
                    public void onError(String msg) {
                        AVLEngine.Logger.error("auth request fail: " + msg);
                    }

                    @Override
                    public void onFinish(int code, Object responseBody) {
                        try {
                            JSONObject object = new JSONObject(responseBody.toString());
                            int resultCode = object.getInt("code");
                            if (resultCode == 0) {
                                JSONObject data = object.getJSONObject("data");
                                AuthResponseEntity responseEntity = JsonUtils.toObject(
                                    data.toString(),
                                    AuthResponseEntity.class
                                );

                                // 解密token
                                String authToken = AVLCoreEngine.getInstance().decryptAuthToken(
                                    responseEntity.content,
                                    responseEntity.responseTime,
                                    responseEntity.flag
                                );
                                AVLEngine.Logger.info("decrypt auth token:" + authToken);

                                // 解析token
                                AuthTokenEntity authTokenEntity = JsonUtils.toObject(
                                    authToken,
                                    AuthTokenEntity.class
                                );

                                // 保存授权信息到安全存储
                                saveAuthInfo(uuid, clientId, authTokenEntity.license);

                            } else {
                                String msg = object.getString("msg");
                                AVLEngine.Logger.error("request token auth failed: " + msg);
                            }
                        } catch (JSONException e) {
                            AVLEngine.Logger.error("Failed to parse auth response: " + e.getMessage());
                        }
                    }
                }
            );
        } catch (Exception e) {
            AVLEngine.Logger.error("Request auth token failed: " + e.getMessage());
        }
    }

    /**
     * 保存授权信息到安全存储
     * 将设备唯一标识、客户端ID和认证令牌保存到安全存储中
     *
     * @param uuid 设备唯一标识
     * @param clientId 客户端标识符
     * @param token 认证令牌
     * @throws JSONException 当创建JSON对象或保存数据时发生异常
     */
    private void saveAuthInfo(String uuid, String clientId, String token) {
        try {
            JSONObject authInfo = new JSONObject();
            authInfo.put("uuid", uuid);
            authInfo.put("client_id", clientId);
            authInfo.put("token", token);
            authInfoManager.saveAuthInfo(authInfo);
        } catch (JSONException e) {
            AVLEngine.Logger.error("Failed to save auth info: " + e.getMessage());
        }
    }
}
