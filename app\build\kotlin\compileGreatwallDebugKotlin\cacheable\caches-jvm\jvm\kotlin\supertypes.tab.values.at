/ Header Record For PersistentHashMapValueStorage!  com.antiy.avlsdk.callback.Logger android.app.Application. -okhttp3.logging.HttpLoggingInterceptor.Logger2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder( 'com.antiy.avlsdk.callback.NetworkTunnel!  com.antiy.demo.base.BaseActivity!  com.antiy.demo.base.BaseActivity!  com.antiy.demo.base.BaseActivity!  com.antiy.demo.base.BaseActivity!  com.antiy.demo.base.BaseActivity!  com.antiy.demo.base.BaseActivity!  com.antiy.demo.base.BaseActivity1 0androidx.viewpager2.adapter.FragmentStateAdapter) (androidx.appcompat.app.AppCompatActivity!  com.antiy.demo.base.BaseActivity!  com.antiy.demo.base.BaseActivity!  com.antiy.demo.base.BaseActivity) (androidx.appcompat.app.AppCompatActivity2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder) (androidx.appcompat.app.AppCompatActivity androidx.fragment.app.Fragment!  com.antiy.demo.base.BaseFragment!  com.antiy.demo.base.BaseFragment!  com.antiy.demo.base.BaseFragment kotlin.Enum kotlin.Enum!  com.antiy.demo.base.BaseFragment!  com.antiy.demo.base.BaseFragment android.os.Parcelable kotlin.Enum kotlin.Enum!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding