<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground"
    app:cardCornerRadius="8dp"
    app:cardElevation="1dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:minHeight="80dp"
        android:gravity="center_vertical">

        <!-- 左侧内容 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- Hash值和状态标签 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvHash"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:ellipsize="middle"
                    android:singleLine="true"
                    android:textColor="@color/black"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    tools:text="12345678...abcdefgh" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chipStatus"
                    style="@style/Widget.MaterialComponents.Chip.Action"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:clickable="false"
                    android:textSize="12sp"
                    app:chipMinHeight="24dp"
                    tools:text="安全" />

            </LinearLayout>

            <!-- 病毒名称 -->
            <TextView
                android:id="@+id/tvVirusName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="@color/gray"
                android:textSize="14sp"
                tools:text="无威胁" />

            <!-- 完整Hash值和时间 -->
            <TextView
                android:id="@+id/tvFullHash"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:ellipsize="end"
                android:maxLines="2"
                android:textColor="@color/gray"
                android:textSize="12sp"
                tools:text="1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef\n点击查看详情并复制" />

            <!-- 时间信息 -->
            <TextView
                android:id="@+id/tvTime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:textColor="@color/gray"
                android:textSize="12sp"
                android:textStyle="italic"
                tools:text="2小时前" />

            <!-- 状态文本（备用显示） -->
            <TextView
                android:id="@+id/tvStatus"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:textSize="14sp"
                android:textStyle="bold"
                android:visibility="gone"
                tools:text="安全"
                tools:textColor="@android:color/holo_green_dark" />

        </LinearLayout>

        <!-- 右侧删除按钮 -->
        <ImageButton
            android:id="@+id/btnDelete"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="8dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="删除"
            android:src="@drawable/ic_delete"
            app:tint="@color/gray" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
