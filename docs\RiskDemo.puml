@startwbs 组件图
* APP
** 界面
*** 开始扫描
*** 设置扫描路径
*** 设置扫描参数
*** 显示扫描进度
** 封装层
*** SDK的初始化
*** 扫描单文件
*** 扫描目录
** 移动端SDK
*** 初始化
*** 更新
*** 扫描文件
** PC端SDK
*** 初始化
*** 更新
*** 扫描文件
** 其他依赖
*** 权限申请
@endwbs

@startuml 用例图

skinparam Linetype polyline
left to right direction

actor User
actor App

rectangle 测试过程 {
        usecase 设置扫描参数 as setArgs
        usecase 选择文件or目录 as selectFile
        usecase 开始扫描 as scanFile
}

rectangle 统计过程 {
        usecase 统计扫描信息 as statistics
        usecase 导出统计数据 as dump
}

User -> setArgs
User -> selectFile
User -> scanFile

App -> statistics
App -> dump

@enduml

@startuml 活动图_流程图

skinparam DefaultFontName "Microsoft YaHei"
skinparam DefaultFontSize 12
skinparam ConditionEndStyle hline

title 测试app内部逻辑

start

:准备测试文件|

:点击app启动|

if (存在检测配置) then(是)

repeat :开始检测;
:删除当前检测配置;
:重启;
repeat while(存在检测配置) is (是) not(否)

else(否)

:选择要扫描的目录或文件;

:添加扫描性能配置;
note right
允许添加多个配置
默认会有个配置(percent:100, interval:50)
end note

:保存配置到持久化存储;

repeat :开始扫描;


        fork
                :遍历目录,获取待扫描的文件列表;
                :回调进度显示;
                note left
                可选
                end note

        fork again
                fork
                        :记录文件扫描完成时间;
                fork again
                        :记录CPU/内存使用情况;
                end fork

                :合并两个时间为统计数据文件;
        end fork

        :扫描结束;

        :删除AVL引擎数据库文件(缓存);

        :删除当前配置;

        :重启app;

repeat while (存在剩余配置) is (是) not(否)

endif
end
@enduml