package com.antiy.avlsdk.license;

/**
 * License刷新结果封装类
 * 封装了license刷新操作的结果信息
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class RefreshResult {
    
    private final RefreshState state;
    private final String message;
    private final long timestamp;
    private final Throwable exception;
    
    /**
     * 私有构造函数，使用Builder模式创建实例
     */
    private RefreshResult(RefreshState state, String message, long timestamp, Throwable exception) {
        this.state = state;
        this.message = message;
        this.timestamp = timestamp;
        this.exception = exception;
    }
    
    /**
     * 获取刷新状态
     * 
     * @return 刷新状态
     */
    public RefreshState getState() {
        return state;
    }
    
    /**
     * 获取结果消息
     * 
     * @return 结果消息
     */
    public String getMessage() {
        return message;
    }
    
    /**
     * 获取时间戳
     * 
     * @return 操作时间戳
     */
    public long getTimestamp() {
        return timestamp;
    }
    
    /**
     * 获取异常信息
     * 
     * @return 异常对象，如果没有异常则返回null
     */
    public Throwable getException() {
        return exception;
    }
    
    /**
     * 判断是否成功
     * 
     * @return true表示成功，false表示失败
     */
    public boolean isSuccess() {
        return state == RefreshState.SUCCESS;
    }
    
    /**
     * 判断是否失败
     * 
     * @return true表示失败，false表示成功或其他状态
     */
    public boolean isFailed() {
        return state == RefreshState.FAILED;
    }
    
    /**
     * 判断是否被跳过
     * 
     * @return true表示被跳过，false表示其他状态
     */
    public boolean isSkipped() {
        return state == RefreshState.SKIPPED;
    }
    
    @Override
    public String toString() {
        return "RefreshResult{" +
                "state=" + state +
                ", message='" + message + '\'' +
                ", timestamp=" + timestamp +
                ", hasException=" + (exception != null) +
                '}';
    }
    
    /**
     * 创建成功结果
     * 
     * @param message 成功消息
     * @return RefreshResult实例
     */
    public static RefreshResult success(String message) {
        return new RefreshResult(RefreshState.SUCCESS, message, System.currentTimeMillis(), null);
    }
    
    /**
     * 创建失败结果
     * 
     * @param message 失败消息
     * @return RefreshResult实例
     */
    public static RefreshResult failed(String message) {
        return new RefreshResult(RefreshState.FAILED, message, System.currentTimeMillis(), null);
    }
    
    /**
     * 创建失败结果（带异常）
     * 
     * @param message 失败消息
     * @param exception 异常对象
     * @return RefreshResult实例
     */
    public static RefreshResult failed(String message, Throwable exception) {
        return new RefreshResult(RefreshState.FAILED, message, System.currentTimeMillis(), exception);
    }
    
    /**
     * 创建跳过结果
     * 
     * @param message 跳过原因
     * @return RefreshResult实例
     */
    public static RefreshResult skipped(String message) {
        return new RefreshResult(RefreshState.SKIPPED, message, System.currentTimeMillis(), null);
    }
    
    /**
     * 创建正在刷新状态的结果
     * 
     * @param message 状态消息
     * @return RefreshResult实例
     */
    public static RefreshResult refreshing(String message) {
        return new RefreshResult(RefreshState.REFRESHING, message, System.currentTimeMillis(), null);
    }
}
