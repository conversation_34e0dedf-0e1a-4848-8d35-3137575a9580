package com.antiy.avlsdk.utils;

import android.os.Build;

import androidx.annotation.RequiresApi;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Author: wangbiao
 * Date: 2024/10/9 14:34
 * Description:日期格式化
 */
public class DateUtils {
    private static final String DATE_FORMAT_1 = "yyyyMMddHHmmss";
    @RequiresApi(api = Build.VERSION_CODES.O)
    public static String getCurrentDate() {
        // 获取当前日期和时间
        LocalDateTime now = LocalDateTime.now();

        // 定义格式化模式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATE_FORMAT_1);

        // 格式化当前日期和时间
        String formattedDateTime = now.format(formatter);

        return formattedDateTime;
    }

}
