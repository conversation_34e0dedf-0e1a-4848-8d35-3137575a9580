package com.antiy.avlsdk.license;

/**
 * License刷新状态枚举
 * 定义了license刷新过程中的各种状态
 * 
 * <AUTHOR>
 * @version 1.0
 */
public enum RefreshState {
    /**
     * 空闲状态 - 没有进行任何刷新操作
     */
    IDLE("空闲状态"),
    
    /**
     * 正在刷新 - 当前正在执行license刷新操作
     */
    REFRESHING("正在刷新"),
    
    /**
     * 刷新成功 - license刷新操作成功完成
     */
    SUCCESS("刷新成功"),
    
    /**
     * 刷新失败 - license刷新操作失败
     */
    FAILED("刷新失败"),
    
    /**
     * 已跳过 - 由于某种原因跳过了刷新操作
     */
    SKIPPED("已跳过");
    
    private final String description;
    
    RefreshState(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
    
    @Override
    public String toString() {
        return name() + "(" + description + ")";
    }
}
