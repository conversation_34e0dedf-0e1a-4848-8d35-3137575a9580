#include <android/log.h>
#include <ctype.h>
#include <dirent.h>
#include <dlfcn.h>
#include <jni.h>
#include <stdbool.h>
#include <stdlib.h>
#include <string.h>
#include <sys/mman.h>
#include <sys/stat.h>
#include <sys/utsname.h>
#include <unistd.h>

#include "aes.h"
#include "avl_sdk.h"
#include "avlm_interface.h"
#include "base64.h"
#include "md5.h"
#include "parson.h"

#define AVLM_LIB_FORMAT_STR "%s/%s/libavlm.so"
static char* avlmLibPath = NULL;
static char* avlmDbPath  = NULL;

static void* gEngineHandle = NULL;

struct sigaction* gOldSigIntHandler  = NULL;
struct sigaction* gOldSigSegvHandler = NULL;
struct sigaction* gOldSigTermHandler = NULL;

#ifdef AVLM_USE_AUTH
static struct auth_args au_Args;
AVLM_AuthInit_FUNC      fAVLM_AuthInit      = NULL;
AVLM_AuthRelease_FUNC   fAVLM_AuthRelease   = NULL;
AVLM_AuthGetProof_FUNC  fAVLM_AuthGetProof  = NULL;
AVLM_AuthFreeProof_FUNC fAVLM_AuthFreeProof = NULL;
#else
AVLM_Init_FUNC    fAVLM_Init    = NULL;
AVLM_Release_FUNC fAVLM_Release = NULL;
AVLM_InitEx_FUNC  fAVLM_InitEx  = NULL;
#endif

AVLM_SetScanOpt_FUNC         fAVLM_SetScanOpt         = NULL;
AVLM_SetScanCategoryOpt_FUNC fAVLM_SetScanCategoryOpt = NULL;
AVLM_Scan_FUNC               fAVLM_Scan               = NULL;
AVLM_ScanWithScanOpt_FUNC    fAVLM_ScanWithScanOpt    = NULL;
AVLM_ScanWithScanOptEx_FUNC  fAVLM_ScanWithScanOptEx  = NULL;
AVLM_ScanWithArgs_FUNC       fAVLM_ScanWithArgs       = NULL;
AVLM_ScanFdWithArgs_FUNC     fAVLM_ScanFdWithArgs     = NULL;
AVLM_InstallPackage_FUNC     fAVLM_InstallPackage     = NULL;
AVLM_Result_Free_FUNC        fAVLM_Result_Free        = NULL;
AVLM_GetVirnameEx_FUNC       fAVLM_GetVirnameEx       = NULL;
AVLM_GetWhitenameEx_FUNC     fAVLM_GetWhitenameEx     = NULL;
AVLM_GetInfo_FUNC            fAVLM_GetEngineVersion   = NULL;
AVLM_GetInfo_FUNC            fAVLM_GetSigLibVersion   = NULL;

/**
 * 信号处理函数，处理 SIGINT 异常
 *
 * @param sig 接收到的信号，需要判断是否是 SIGINT
 */
void sigint_handler(int sig)
{
        if (sig == SIGINT) {
                fprintf(stderr, "Got interrupt signal!\n");
                return;
        }
}

/**
 * 信号处理函数，处理 SIGSEGV 异常
 *
 * @param sig
 */
void sigsegv_handler(int sig)
{
        if (sig == SIGSEGV) {
                fprintf(stderr, "Got segfault signal!\n");
                return;
        }
}

/**
 * 信号处理函数，处理 SIGTERM 信号
 *
 * @param sig
 */
void sigterm_handler(int sig)
{
        if (sig == SIGTERM) {
                fprintf(stderr, "Got SIGTERM signal!\n");
                return;
        }
}

/**
 * 获取当前运行的架构，用来组合目录
 *
 * @return 字符串形式的架构信息，如 "arm64-v8a"、"armeabi"、"x86"、"x86_64" 等等
 */
char* native_get_running_arch()
{
        struct utsname systemInfo;
        if (uname(&systemInfo) == -1) return strdup("unknown");

        const char* arch = systemInfo.machine;

        if (strstr(arch, "aarch64") != NULL) { return strdup("arm64-v8a"); }
        else if (strstr(arch, "armv7") != NULL || strstr(arch, "arm") != NULL) {
                return strdup("armeabi");
        }
        else if (strstr(arch, "i686") != NULL) {
                return strdup("x86");
        }
        else if (strstr(arch, "x86_64") != NULL) {
                return strdup("x86_64");
        }
        else {
                return strdup(arch);
        }
}

#ifdef AVLM_USE_AUTH

/**
 * 将输入字节流转换成对应 hex 输出
 *
 * @param bytes 输入字节
 * @param bytesLen 输入字节长度
 * @param upperCase 输出是否大写
 * @return 输出字符串，调用方负责释放
 */
char* native_bytes_to_hex(unsigned char* bytes, int bytesLen, bool upperCase)
{
        char* ret = (char*)calloc(1, bytesLen * 2 + 1);
        char* tmp = ret;
        for (int i = 0; i < bytesLen; i++) { tmp += sprintf(tmp, upperCase ? "%02X" : "%02x", bytes[i]); }

        return ret;
}

/**
 * 生成 aes 加密 key
 *
 * @param uuid 客户端 uuid，字符串全大写
 * @param requestTime 请求发起时间，yyyyMMddHHMMSS
 * @param flag 随机数，范围 [0-31]
 * @return 基于输入的 md5 计算值，全大写 需要调用方释放
 */
char* native_generate_key(const char* requestTime, int flag)
{
        md5_state_t md5_ctx;
        md5_byte_t* md5_bytes = NULL;

        const char* uuid = au_Args.uuid;

        md5_bytes     = md5((const md5_byte_t*)uuid, (int)strlen(uuid));
        char* md5_str = native_bytes_to_hex(md5_bytes, 16, true);
        free(md5_bytes);

        char* key_str = (char*)calloc(1, strlen(requestTime) + strlen(md5_str) + 3);
        sprintf(key_str, "%s%s%d", requestTime, md5_str, flag << 2);
        free(md5_str);

        md5_bytes = md5((const md5_byte_t*)key_str, (int)strlen(key_str));
        free(key_str);

        md5_str = native_bytes_to_hex(md5_bytes, 16, true);
        free(md5_bytes);

        // printf("Encrypted key is %s\n", md5_str);

        return md5_str;
}

/**
 * 使用 key 加密输入内容，返回 base64 编码的密文
 *
 * @param key 加密密钥
 * @param clearText 要加密的明文
 * @return base64 编码的密文，调用方负责释放
 */
char* native_encrypt(const char* key, const char* clearText)
{
        // 如果 key 长度小于 16，退出
        if (strlen(key) < 16) { return NULL; }

        // padding
        char* dupText    = NULL;
        int   paddingLen = 16 - strlen(clearText) % 16;

        if (paddingLen != 16) {
                dupText   = (char*)calloc(1, strlen(clearText) + paddingLen + 1);
                char* end = stpcpy(dupText, clearText);
                for (int i = 0; i < paddingLen; i++, end++) { *end = (char)paddingLen; }
        }
        else {
                dupText = strdup(clearText);
        }

        int totalLen = strlen(dupText);

        // 按照约定，使用 key 的前 16 字节作为 iv
        uint8_t iv[16];
        memcpy(iv, key, 16);

        // 初始化 aes
        struct AES_ctx aes_ctx;
        AES_init_ctx_iv(&aes_ctx, (uint8_t*)key, iv);
        AES_CBC_encrypt_buffer(&aes_ctx, (uint8_t*)dupText, strlen(dupText));

        char* base64 = base64_encode(dupText, totalLen);
        free(dupText);

        return base64;
}

/**
 * 生成认证请求需要的 base64 编码的密文
 *
 * @param uuid 客户端 uuid
 * @param client_id 客户端 client_id
 * @param requestTime 请求时间 yyyyMMddHHMMSS
 * @param flag 随机数，[0-31]
 * @return base64 编码的密文，调用方负责释放
 */
char* native_generate_auth_request(const char* requestTime, int flag)
{
        // generate key
        char* key = native_generate_key(requestTime, flag);

        // encrypt request
        char* request;
        asprintf(&request, "{\"uuid\": \"%s\", \"client_id\":\"%s\"}", au_Args.uuid, au_Args.clientId);

        // encrypt & base64 encode
        return native_encrypt(key, request);
}

/**
 * 使用 key 解密 base64 编码的密文
 *
 * @param key 解密用的 key
 * @param encryptedText base64 编码的密文
 * @param msgLen 原始数据长度
 * @return 原始数据，调用方负责释放
 */
char* native_decrypt(const char* key, const char* encryptedText, int* msgLen)
{
        // base64 解码
        char* clearText = base64_decode(encryptedText, msgLen);

        // 构建 aes 对象
        uint8_t iv[16];
        memcpy(iv, key, 16);

        struct AES_ctx aes_ctx;
        AES_init_ctx_iv(&aes_ctx, (uint8_t*)key, iv);
        AES_CBC_decrypt_buffer(&aes_ctx, (uint8_t*)clearText, *msgLen);

        // unpadding
        uint8_t padding = clearText[(*msgLen) - 1];
        if (padding < AES_BLOCKLEN) {
                for (int i = 0; i < padding; i++) { clearText[(*msgLen) - 1 - i] = '\0'; }
        }

        *msgLen = strlen(clearText);

        return clearText;
}

/**
 * 设置认证 UUID 和 ClientID
 *
 * 如果传入参数无效，会清空已保存参数
 *
 * @param uuid
 * @param clientId
 * @return 0 设置成功
 */
int native_set_auth_params(const char* uuid, const char* clientId)
{
        if (uuid && strlen(uuid) != 0 && clientId && strlen(clientId) != 0) {
                strcpy(au_Args.uuid, uuid);
                strcpy(au_Args.clientId, clientId);

                return 0;
        }
        else {
                memset(au_Args.uuid, '\0', sizeof(au_Args.uuid));
                memset(au_Args.clientId, '\0', sizeof(au_Args.clientId));

                return -1;
        }
}

/**
 * 设置认证参数
 *
 * @param uuid 当前 uuid
 * @param license 服务端下发的 license 数据
 * @param licenseLen license 长度
 * @return 0 设置成功
 */
int native_set_auth_token(const char* license, int licenseLen)
{
        if (!license || licenseLen == 0) { return -1; }

        // init auth license
        memcpy(au_Args.license, license, licenseLen);
        au_Args.licenseLen = licenseLen;

        return 0;
}

char* native_get_auth_info()
{
        char* ret = NULL;
        if (au_Args.licenseLen != 0 && fAVLM_AuthGetProof && fAVLM_AuthFreeProof) {
                char* licenseInfo = fAVLM_AuthGetProof(au_Args.license, au_Args.licenseLen);
                ret               = strdup(licenseInfo);
                fAVLM_AuthFreeProof(licenseInfo);
        }

        return ret;
}
#endif

/**
 * 初始化引擎
 *
 * 当前主要是初始化引擎 so 所在目录和病毒库所在目录
 * 目录结构：
 * so_path
 *     |--- arm64-v8a
 *         |--- libavlm.so
 *     |--- armeabi
 *         |--- libavlm.so
 * db_path
 *     |--- avl
 *         |--- android
 *             |--- ***.avl
 *         |--- conf
 *             |--- avllib.conf
 *             |--- license.conf
 *
 * @param libPath 引擎 so 所在目录
 * @param dbPath 引擎病毒库所在目录
 * @return 0:初始化成功; -1:初始化失败
 */
int native_init(const char* libPath, const char* dbPath)
{
        avlmLibPath = (char*)calloc(1, strlen(libPath) + 1);
        avlmDbPath  = (char*)calloc(1, strlen(dbPath) + 1);
        strcpy(avlmLibPath, libPath);
        strcpy(avlmDbPath, dbPath);

        if (access(libPath, 0) == 0) {
                LOGI("Provided so path %s accessible.", libPath);
                char* arch  = native_get_running_arch();
                avlmLibPath = (char*)calloc(1, strlen(libPath) + strlen(arch) + strlen(AVLM_LIB_FORMAT_STR));   // 两个%s可以提供尾部的\0所需空间
                sprintf(avlmLibPath, AVLM_LIB_FORMAT_STR, libPath, arch);
                free(arch);
                if (access(avlmLibPath, 0) == 0) { LOGI("%s", "Found provided so successfully."); }
                else {
                        LOGE("Failed to load so at path %s", avlmLibPath);
                        free(avlmLibPath);
                        return -1;
                }
        }
        else {
                LOGE("Provided so %s not accessible", libPath);
                return -1;
        }

        if (access(avlmDbPath, 0) == 0) { LOGI("Provided db dir %s accessible.", avlmDbPath); }
        else {
                LOGE("Provided db dir %s not accessible.", avlmDbPath);
                free(avlmDbPath);
                avlmDbPath = NULL;
                return -1;
        }

        // 加载 so 库
        gEngineHandle = dlopen(avlmLibPath, RTLD_LAZY);
        if (gEngineHandle == NULL) {
                LOGE("Failed to load so at path %s", avlmLibPath);
                free(avlmLibPath);
                return -1;
        }

#ifdef AVLM_USE_AUTH
        fAVLM_AuthInit      = (AVLM_AuthInit_FUNC)dlsym(gEngineHandle, AVLM_AuthInit);
        fAVLM_AuthRelease   = (AVLM_AuthRelease_FUNC)dlsym(gEngineHandle, AVLM_AuthRelease);
        fAVLM_AuthGetProof  = (AVLM_AuthGetProof_FUNC)dlsym(gEngineHandle, AVLM_AuthGetProof);
        fAVLM_AuthFreeProof = (AVLM_AuthFreeProof_FUNC)dlsym(gEngineHandle, AVLM_AuthFreeProof);

        if (!fAVLM_AuthInit || !fAVLM_AuthRelease || !fAVLM_AuthGetProof || !fAVLM_AuthFreeProof) { LOGE("%s", "failed to load engine."); }
#else
        fAVLM_Init    = (AVLM_Init_FUNC)dlsym(gEngineHandle, AVLM_Init);
        fAVLM_Release = (AVLM_Release_FUNC)dlsym(gEngineHandle, AVLM_Release);
        if (!fAVLM_Init || !fAVLM_Release) { LOGE("%s", "failed to load engine."); }
#endif
        fAVLM_InstallPackage     = (AVLM_InstallPackage_FUNC)dlsym(gEngineHandle, AVLM_InstallPackage);
        fAVLM_SetScanOpt         = (AVLM_SetScanOpt_FUNC)dlsym(gEngineHandle, AVLM_SetScanOpt);
        fAVLM_SetScanCategoryOpt = (AVLM_SetScanCategoryOpt_FUNC)dlsym(gEngineHandle, AVLM_SetScanCategoryOpt);
        fAVLM_Scan               = (AVLM_Scan_FUNC)dlsym(gEngineHandle, AVLM_Scan);
        fAVLM_ScanWithScanOpt    = (AVLM_ScanWithScanOpt_FUNC)dlsym(gEngineHandle, AVLM_ScanWithScanOpt);
        fAVLM_ScanWithScanOptEx  = (AVLM_ScanWithScanOptEx_FUNC)dlsym(gEngineHandle, AVLM_ScanWithScanOptEx);
        fAVLM_ScanWithArgs       = (AVLM_ScanWithArgs_FUNC)dlsym(gEngineHandle, AVLM_ScanWithArgs);
        fAVLM_ScanFdWithArgs     = (AVLM_ScanFdWithArgs_FUNC)dlsym(gEngineHandle, AVLM_ScanFdWithArgs);
        fAVLM_GetVirnameEx       = (AVLM_GetVirnameEx_FUNC)dlsym(gEngineHandle, AVLM_GetVirnameEx);
        fAVLM_GetWhitenameEx     = (AVLM_GetWhitenameEx_FUNC)dlsym(gEngineHandle, AVLM_GetWhitenameEx);
        fAVLM_Result_Free        = (AVLM_Result_Free_FUNC)dlsym(gEngineHandle, AVLM_Result_Free);
        fAVLM_GetEngineVersion   = (AVLM_GetInfo_FUNC)dlsym(gEngineHandle, AVLM_GetEngineVersion);
        fAVLM_GetSigLibVersion   = (AVLM_GetInfo_FUNC)dlsym(gEngineHandle, AVLM_GetSigLibVersion);

        if (!fAVLM_InstallPackage || !fAVLM_SetScanOpt || !fAVLM_SetScanCategoryOpt || !fAVLM_Scan || !fAVLM_ScanWithScanOpt || !fAVLM_ScanWithScanOptEx ||
            !fAVLM_ScanWithArgs || !fAVLM_ScanFdWithArgs || !fAVLM_GetVirnameEx || !fAVLM_GetWhitenameEx || !fAVLM_Result_Free || !fAVLM_GetEngineVersion ||
            !fAVLM_GetSigLibVersion) {
                LOGE("%s", "failed to load engine.");
                return -1;
        }

#ifdef AVLM_USE_AUTH
        int initRet = -99;

        // 反病毒鉴权初始化引擎的返回值 0 成功，-1 其它失败，-2 病毒库校验失败，-3 解密授权凭证失败，-4 鉴权失败，-5 重复初始化
        if (fAVLM_AuthInit) { initRet = fAVLM_AuthInit(avlmDbPath, au_Args.uuid, au_Args.license, au_Args.licenseLen); }

#else
        int initRet = -99;
        if (fAVLM_Init) { initRet = fAVLM_Init(g_Args.avllibsigpath); }
#endif

        if (initRet == 0) { LOGI("EngineVersion: %s, SiglibVersion: %s", fAVLM_GetEngineVersion(), fAVLM_GetSigLibVersion()); }

        return initRet;
}

/**
 * 释放 dlopen 加载的 so 库
 *
 * @return 0 释放成功
 */
int native_release()
{
        if (!gEngineHandle) return 0;

#ifdef AVLM_USE_AUTH
        if (fAVLM_AuthRelease) fAVLM_AuthRelease();
#else
        if (fAVLM_Release) fAVLM_Release();
#endif

        dlclose(gEngineHandle);
        gEngineHandle = NULL;

#ifdef AVLM_USE_AUTH
        fAVLM_AuthInit    = NULL;
        fAVLM_AuthRelease = NULL;
#else
        fAVLM_Init    = NULL;
        fAVLM_Release = NULL;
        fAVLM_InitEx  = NULL;
#endif

        fAVLM_SetScanOpt         = NULL;
        fAVLM_SetScanCategoryOpt = NULL;
        fAVLM_Scan               = NULL;
        fAVLM_ScanWithScanOptEx  = NULL;
        fAVLM_InstallPackage     = NULL;
        fAVLM_Result_Free        = NULL;
        fAVLM_GetVirnameEx       = NULL;
        fAVLM_GetWhitenameEx     = NULL;
        fAVLM_GetEngineVersion   = NULL;
        fAVLM_GetSigLibVersion   = NULL;

        return 0;
}

/**
 * 扫描文件
 *
 * @param path 要扫描的文件路径
 * @return 扫出来的病毒名，无病毒则为空
 */
char* native_scan(const char* path)
{
        char*       ret     = NULL;
        AVLM_Result iResult = NULL;
        // FIXME: 当前先使用 ScanWithScanOptEx
        //        等研究透了再看怎么使用另外两个函数 Scan / ScanWithScanOpt
        // iResult = (void*)fAVLM_ScanWithScanOptEx((char*)path, AVLM_ADR_ScanOpt_ALL, AVLM_CATEGORY_ALL);
        ScanArgs1 args;
        args.categoryFlag = AVLM_CATEGORY_ALL;
        args.scanOpt = AVLM_PLATFORM_Android | (AVLM_ADR_ScanOpt_ALL & ~(AVLM_ADR_ScanOpt_DEXSYM));
        args.logOpt  = 0x0;
        args.firstInstallTime = 0x0;

        iResult = fAVLM_ScanWithArgs((char*)path, &args);

        if (iResult) {
                char* retName = fAVLM_GetVirnameEx(iResult);
                if (retName && strlen(retName) != 0) {
                        // should ignores "White.xxx" or "White/xxx"
                        if (strncmp(retName, "White/", 6) != 0) { ret = strdup(retName); }
                }
        }

        fAVLM_Result_Free(iResult);

        return ret;
}

char* native_scan_fd(const int fd)
{
        char*       ret     = NULL;
        AVLM_Result iResult = NULL;
        // FIXME: 当前先使用 ScanWithScanOptEx
        //        等研究透了再看怎么使用另外两个函数 Scan / ScanWithScanOpt
        // iResult = (void*)fAVLM_ScanWithScanOptEx((char*)path, AVLM_ADR_ScanOpt_ALL, AVLM_CATEGORY_ALL);
        ScanArgs1 args;
        args.categoryFlag     = AVLM_CATEGORY_ALL;
        args.scanOpt          = AVLM_PLATFORM_Android | AVLM_ADR_ScanOpt_ALL;
        args.logOpt           = 0x0;
        args.firstInstallTime = 0x0;

        iResult = fAVLM_ScanFdWithArgs(fd, &args);

        if (iResult) {
                char* retName = fAVLM_GetVirnameEx(iResult);
                if (retName && strlen(retName) != 0) {
                        // should ignores "White.xxx" or "White/xxx"
                        if (strncmp(retName, "White/", 6) != 0) { ret = strdup(retName); }
                }
        }

        fAVLM_Result_Free(iResult);

        return ret;
}

/**
 * 从 apk 文件中获取 key 哈希
 *
 * @param path apk 文件路径
 * @return key 哈希字符串，需要 free
 */
char* native_get_cert_hash(const char* path)
{
        void*                 pvModule          = NULL;
        char*                 pcCerthash        = NULL;
        AVLM_GetCertHash_FUNC mAVLM_GetCertHash = NULL;

        // 检测传入参数文件是否存在
        if (access(path, 0) == 0) {
                // 加载 so 库
                // 如果打开 so 库失败，直接返回空字符串
                pvModule = dlopen(avlmLibPath, RTLD_LAZY);
                if (pvModule) {
                        // 获得函数指针
                        mAVLM_GetCertHash = (AVLM_GetCertHash_FUNC)dlsym(pvModule, "AVLM_GetCertHash");

                        // 执行获取 key 哈希操作
                        if (mAVLM_GetCertHash) { pcCerthash = mAVLM_GetCertHash((char*)path); }

                        // 释放 so
                        dlclose(pvModule);
                        pvModule = NULL;
                }
                else {
                        LOGI("%s", "Failed to load library avlmso");
                }
        }

        return pcCerthash;
}

/**
 * 提取 apk 文件的完整 key 哈希
 *
 * @param path 要提取 key 哈希的 apk 文件路径
 * @return 字符串形式的 key 哈希（16 位），需要 free
 */
char* native_get_cert_full_hash(const char* path)
{
        void*                 pvModule              = NULL;
        char*                 pcCerthash            = NULL;
        AVLM_GetCertHash_FUNC mAVLM_GetCertFullHash = NULL;

        // 检测传入参数文件是否存在
        if (access(path, 0) == 0) {
                // 加载 so 库
                // 如果打开 so 库失败，直接返回空字符串
                pvModule = dlopen(avlmLibPath, RTLD_LAZY);
                if (pvModule) {
                        // 获得函数指针
                        mAVLM_GetCertFullHash = (AVLM_GetCertHash_FUNC)dlsym(pvModule, "AVLM_GetCertFullHash");

                        // 执行获取 key 哈希操作
                        if (mAVLM_GetCertFullHash) { pcCerthash = mAVLM_GetCertFullHash((char*)path); }

                        // 释放 so
                        dlclose(pvModule);
                        pvModule = NULL;
                }
                else {
                        LOGI("%s", "Failed to load library avlmso");
                }
        }

        return pcCerthash;
}

/**
 * 用于删除目录的帮助函数
 *
 * @param path 要删除的目录/文件
 * @return 删除文件 + 文件夹的数量，负数表示错误
 */
int rmtree(const char path[])
{
        size_t         path_len;
        char*          full_path;
        DIR*           dir;
        struct stat    stat_path, stat_entry;
        struct dirent* entry;
        int            ret = -1;

        if (access(path, F_OK) != 0) { return ret; }

        // stat for the path
        stat(path, &stat_path);

        // if path does not exists or is not dir - exit with status -1
        if (S_ISDIR(stat_path.st_mode) == 0) {
                // FIXME: 这里要检查下 unlink 是否成功
                unlink(path);
                ret = 1;
                return ret;
        }

        // if not possible to read the directory for this user
        if ((dir = opendir(path)) == NULL) {
                fprintf(stderr, "%s: %s\n", "Can`t open directory", path);
                return ret;
        }

        // the length of the path
        path_len = strlen(path);
        ret      = 0;

        // iteration through entries in the directory
        while ((entry = readdir(dir)) != NULL) {
                // skip entries "." and ".."
                if (!strcmp(entry->d_name, ".") || !strcmp(entry->d_name, "..")) continue;

                // determinate a full path of an entry
                asprintf(&full_path, "%s/%s", path, entry->d_name);

                // stat for the entry
                stat(full_path, &stat_entry);

                // recursively remove a nested directory
                if (S_ISDIR(stat_entry.st_mode) != 0) {
                        // FIXME: 这里如果 rmtree 返回了 -1 会导致逻辑错误
                        ret += rmtree(full_path);
                        free(full_path);
                        continue;
                }

                // remove a file object
                if (unlink(full_path) == 0) {
                        ret++;
                        printf("Removed a file:\t%s\n", full_path);
                }
                else
                        printf("Can't remove a file:\t%s\n", full_path);
                free(full_path);
        }

        // remove the devastated directory and close the object of it
        if (rmdir(path) == 0) {
                ret++;
                printf("Removed a directory:\t%s\n", path);
        }
        else
                printf("Can't remove a directory:\t%s\n", path);

        closedir(dir);

        return ret;
}

/**
 * 应用病毒库更新包
 *
 * @param package_path 更新包路径
 * @param package_type 更新包类型
 * @param to_version 更新后的版本
 * @param sig_package_type 病毒库类型
 * @return 更新结果，0 成功，非 0 失败
 */
int native_install_package(const char* package_path, int package_type, const char* to_version, const char* sig_package_type)
{
        void*                    pvModule             = NULL;
        char*                    pcCerthash           = NULL;
        AVLM_InstallPackage_FUNC mAVLM_InstallPackage = NULL;

        int ret = -1;

        fprintf(stderr, "%s: installing package %s as version %s.\n", __func__, package_path, to_version);

        if (access(package_path, 0) == 0) {
                pvModule = dlopen(avlmLibPath, RTLD_LAZY);
                if (pvModule) {
                        mAVLM_InstallPackage = (AVLM_InstallPackage_FUNC)dlsym(pvModule, "AVLM_InstallPackage");
                        if (mAVLM_InstallPackage) {
                                if (mAVLM_InstallPackage(package_path, package_type, to_version, sig_package_type) != 0) {
                                        // AVL 提供的接口，非 0 表示成功，0 表示失败，这里要转换一下
                                        ret = 0;

                                        /* 清理安装完后多余的文件，避免下一次 install package 失败 */
                                        // 构造 avl 上级目录
                                        char* avlmSiglibRoot = strdup(avlmDbPath);   // 形如 /data/data/com.app.name/files/avlsdk_mobile/avl
                                        char* lastSep        = strrchr(avlmSiglibRoot, '/');
                                        *lastSep             = '\0';

                                        // avl.old 目录
                                        char* avlmOldPath;
                                        asprintf(&avlmOldPath, "%s/avl.old", avlmSiglibRoot);
                                        fprintf(stderr, "Removing old dir %s\n", avlmOldPath);
                                        if (access(avlmOldPath, F_OK) == 0) { rmtree(avlmOldPath); }
                                        free(avlmOldPath);

                                        // avl_conf 目录
                                        char* avlmConfPath;
                                        asprintf(&avlmConfPath, "%s/avl_conf", avlmSiglibRoot);
                                        fprintf(stderr, "Removing old conf %s\n", avlmOldPath);
                                        if (access(avlmConfPath, F_OK) == 0) { rmtree(avlmConfPath); }
                                        free(avlmConfPath);

                                        free(avlmSiglibRoot);
                                }
                        }
                }
        }

        return ret;
}

/**
 * 获取引擎版本
 *
 * @return 引擎版本字符串，无需释放
 */
const char* native_get_engine_version()
{
        const char* ret = NULL;
        if (fAVLM_GetEngineVersion) { ret = fAVLM_GetEngineVersion(); }

        return ret;
}

/**
 * 获取病毒库版本
 *
 * @return 病毒库版本字符串，无需释放
 */
const char* native_get_sig_version()
{
        const char* ret = NULL;
        if (fAVLM_GetSigLibVersion) { ret = fAVLM_GetSigLibVersion(); }

        return ret;
}

#ifdef __cplusplus
extern "C" {
#endif

JNIEXPORT void Java_com_antiy_avlsdk_AVLCoreEngine_setupAuthParams(JNIEnv* env, jobject thiz, jstring uuid, jstring client_id)
{
        jboolean    isCopy;
        const char* nativeUuid     = (*env)->GetStringUTFChars(env, uuid, &isCopy);
        const char* nativeClientId = (*env)->GetStringUTFChars(env, client_id, &isCopy);

        if (native_set_auth_params(nativeUuid, nativeClientId) != 0) {
                LOGE("failed to set uuid<%s> & client_id<%s>, please provide valid param", nativeUuid, nativeClientId);
        }

        (*env)->ReleaseStringUTFChars(env, uuid, nativeUuid);
        (*env)->ReleaseStringUTFChars(env, client_id, nativeClientId);
}

JNIEXPORT jstring Java_com_antiy_avlsdk_AVLCoreEngine_generateAuthRequest(JNIEnv* env, jobject cls, jstring requestTime, jint flag)
{
        jstring ret = (*env)->NewStringUTF(env, "");
        // 读取输入参数，转为 native 变量
        jboolean    isCopy;
        const char* nativeRequestTime = (*env)->GetStringUTFChars(env, requestTime, &isCopy);

        // 生成请求内容
        char* request = native_generate_auth_request(nativeRequestTime, flag);

        // 生成 jstring 对象
        ret = (*env)->NewStringUTF(env, request);
        free(request);

        (*env)->ReleaseStringUTFChars(env, requestTime, nativeRequestTime);

        return ret;
}

JNIEXPORT jstring Java_com_antiy_avlsdk_AVLCoreEngine_decryptAuthToken(JNIEnv* env, jobject thiz, jstring body, jstring response_time, jint flag)
{
        jstring ret = (*env)->NewStringUTF(env, "");

        jboolean    isCopy;
        const char* nativeMsgBody      = (*env)->GetStringUTFChars(env, body, &isCopy);
        const char* nativeResponseTime = (*env)->GetStringUTFChars(env, response_time, &isCopy);

        char* key = native_generate_key(nativeResponseTime, flag);

        int   msgLen;
        char* msg = native_decrypt(key, nativeMsgBody, &msgLen);
        if (msg && strlen(msg) == msgLen) { ret = (*env)->NewStringUTF(env, msg); }

        (*env)->ReleaseStringUTFChars(env, body, nativeMsgBody);
        (*env)->ReleaseStringUTFChars(env, response_time, nativeResponseTime);

        return ret;
}

/**
 * 初始化引擎，包括加载 so、验证授权等。
 *
 * @param env JNI 函数固定参数
 * @param cls JNI 函数固定参数
 * @param libPath 引擎 so 库所在路径，arm64-v8a 所在目录
 * @param dbPath 引擎病毒库目录，包含 android / conf 的目录
 * @return 0: 初始化成功; 非 0: 初始化失败
 */
JNIEXPORT jint Java_com_antiy_avlsdk_AVLCoreEngine_init(JNIEnv* env, jobject cls, jstring libPath, jstring dbPath, jstring token)
{
        // 读取入参转为 native 路径
        jboolean    isCopy        = false;
        const char* nativeLibPath = (*env)->GetStringUTFChars(env, libPath, &isCopy);
        const char* nativeDbPath  = (*env)->GetStringUTFChars(env, dbPath, &isCopy);
        const char* nativeToken   = (*env)->GetStringUTFChars(env, token, &isCopy);
        LOGI("Token: <%s>", nativeToken);
#if 0
#        define UUID "5252D5E2-1EC7-49E8-88CF-30751BC2518A"
        int   licenseLen;
        char* license = base64_decode(
            "fOz/DjxF2WBlH/Q1lgTDz/oPQr613zoDp+8y0uDV4mI1oKWbGCwG9X1KEOtVVXxATQzY3gsSvXlIwn6YymgIdg+lzHHky4w9Qi9+T/"
            "8PVY1lSLR5ZJLKMlYGMv9jDJXWsB6OcfelBDon+1BXfwKfmI56e3BtQ1ovTDamtAEIWT1yMv28KwRuTyH1QvO60agsx0aQc6s2lFphCkuIjpiM0L/q2nV/ZfmpQUpxIMb4i6mGVEqN/"
            "StSxRB30R/ByHHvBAAAAA==",
            &licenseLen);
        native_set_auth_token(license, licenseLen);
#else
        int   licenseLen;
        char* license = base64_decode(nativeToken, &licenseLen);
        LOGI("Token len after base64 decode: %d", licenseLen);
        native_set_auth_token(license, licenseLen);
#endif
        int ret = native_init(nativeLibPath, nativeDbPath);

        (*env)->ReleaseStringUTFChars(env, libPath, nativeLibPath);
        (*env)->ReleaseStringUTFChars(env, dbPath, nativeDbPath);

        return (jint)ret;
}

/**
 * 释放引擎 so，减少内存占用
 *
 * @param env JNI 函数固定参数
 * @param cls JNI 函数固定参数
 * @return 0: 成功; 非 0: 失败
 */
JNIEXPORT jint Java_com_antiy_avlsdk_AVLCoreEngine_release(JNIEnv* env, jobject cls) { return native_release(); }

/**
 * 设置扫描参数
 *
 * @param env JNI 函数固定参数
 * @param cls JNI 函数固定参数
 * @param flags 要设置的参数选项
 * @return 0: 成功; 非 0: 失败
 */
JNIEXPORT jint Java_com_antiy_avlsdk_AVLCoreEngine_setScanOpt(JNIEnv* env, jobject cls, jint flags) { return (jint)0; }

/**
 * 设置分类选项 (TODO)
 *
 * @param env JNI 函数固定参数
 * @param cls JNI 函数固定参数
 * @param flags 要设置的参数
 * @return 0: 成功; 非 0: 失败
 */
JNIEXPORT jint Java_com_antiy_avlsdk_AVLCoreEngine_setScanCategoryOpt(JNIEnv* env, jobject cls, jint flags) { return (jint)0; }

JNIEXPORT void JNICALL Java_com_antiy_avlsdk_AVLCoreEngine_installSignalHandler(JNIEnv* env, jobject thiz)
{
        if (gOldSigIntHandler && gOldSigSegvHandler && gOldSigTermHandler)
                return;
        else {
                if (!gOldSigIntHandler) {
                        gOldSigIntHandler = calloc(1, sizeof(struct sigaction));

                        struct sigaction newSigIntHandler;
                        memset(&newSigIntHandler, 0, sizeof(struct sigaction));
                        sigemptyset(&newSigIntHandler.sa_mask);
                        newSigIntHandler.sa_handler = sigint_handler;
                        sigaction(SIGINT, &newSigIntHandler, gOldSigIntHandler);
                }
                if (!gOldSigSegvHandler) {
                        gOldSigSegvHandler = calloc(1, sizeof(struct sigaction));

                        struct sigaction newSigSegvHandler;
                        memset(&newSigSegvHandler, 0, sizeof(struct sigaction));
                        sigemptyset(&newSigSegvHandler.sa_mask);
                        newSigSegvHandler.sa_handler = sigsegv_handler;
                        sigaction(SIGSEGV, &newSigSegvHandler, gOldSigSegvHandler);
                }
                if (!gOldSigTermHandler) {
                        gOldSigTermHandler = calloc(1, sizeof(struct sigaction));

                        struct sigaction newSigTermHandler;
                        memset(&newSigTermHandler, 0, sizeof(struct sigaction));
                        sigemptyset(&newSigTermHandler.sa_mask);
                        newSigTermHandler.sa_handler = sigterm_handler;
                        sigaction(SIGTERM, &newSigTermHandler, gOldSigTermHandler);
                }
        }
}

JNIEXPORT void JNICALL Java_com_antiy_avlsdk_AVLCoreEngine_restoreSignalHandler(JNIEnv* env, jobject thiz)
{
        if (!gOldSigIntHandler && !gOldSigSegvHandler && !gOldSigTermHandler) { return; }
        else {
                if (gOldSigIntHandler) {
                        sigaction(SIGINT, gOldSigIntHandler, NULL);
                        free(gOldSigIntHandler);
                        gOldSigIntHandler = NULL;
                }
                if (gOldSigSegvHandler) {
                        sigaction(SIGSEGV, gOldSigSegvHandler, NULL);
                        free(gOldSigSegvHandler);
                        gOldSigSegvHandler = NULL;
                }
                if (gOldSigTermHandler) {
                        sigaction(SIGTERM, gOldSigTermHandler, NULL);
                        free(gOldSigTermHandler);
                        gOldSigTermHandler = NULL;
                }
        }
}

/**
 * 扫描文件
 *
 * @param env JNI 函数固定参数
 * @param cls JNI 函数固定参数
 * @param filePath 要扫描的文件绝对路径
 * @return 扫描出来的病毒名称，无病毒则为空字符串
 */
JNIEXPORT jstring Java_com_antiy_avlsdk_AVLCoreEngine_scan__Ljava_lang_String_2(JNIEnv* env, jobject cls, jstring filePath)
{
        const char* nativeString = NULL;
        jboolean    isCopy       = false;
        jstring     ret          = (*env)->NewStringUTF(env, "");

        nativeString = (*env)->GetStringUTFChars(env, filePath, &isCopy);

        char* retScan = native_scan(nativeString);
        if (retScan) {
                ret = (*env)->NewStringUTF(env, retScan);
                free(retScan);
        }

        (*env)->ReleaseStringUTFChars(env, filePath, nativeString);

        return ret;
}

/**
 * 扫描给定的文件描述符
 *
 * 本函数为重载函数，添加了 _II 后缀以与标准scan函数做区分
 * @param env JNI函数固定参数
 * @param cls JNI函数固定参数
 * @param fileDescriptor 要扫描的文件描述符
 * @param fileSize 要扫描的文件大小
 * @return 扫描出来的病毒名，无病毒则为空字符串
 */
JNIEXPORT jstring Java_com_antiy_avlsdk_AVLCoreEngine_scan__I(JNIEnv* env, jobject cls, jint fileDescriptor)
{
        jstring ret = (*env)->NewStringUTF(env, "");

        LOGI("%s: scanning fd %d", __func__, fileDescriptor);

        // check fd stat
        struct stat file_info;
        if (fstat(fileDescriptor, &file_info) == -1) {   // 获取文件信息
                perror("fstat failed");
                LOGE("%s: cannot get file size info", __func__);
        }
        else {
                LOGI("%s: file size is %lld", __func__, file_info.st_size);
        }

        char* retScan = native_scan_fd(fileDescriptor);
        if (retScan) {
                LOGI("%s: scan finished, result<%s>", __func__, retScan);
                ret = (*env)->NewStringUTF(env, retScan);
                free(retScan);
        }
        else {
                LOGE("%s: scan failed!", __func__);
        }

        return ret;
}

/**
 * 获取病毒库版本
 *
 * @param env JNI 函数固定参数
 * @param cls JNI 函数固定参数
 * @return 字符串形式的病毒库版本
 */
JNIEXPORT jstring Java_com_antiy_avlsdk_AVLCoreEngine_getSigLibVersion(JNIEnv* env, jobject cls) { return (*env)->NewStringUTF(env, native_get_sig_version()); }

/**
 * 获取引擎版本
 *
 * @param env JNI 函数固定参数
 * @param cls JNI 函数固定参数
 * @return 字符串形式的引擎版本号
 */
JNIEXPORT jstring Java_com_antiy_avlsdk_AVLCoreEngine_getEngineVersion(JNIEnv* env, jobject cls)
{
        return (*env)->NewStringUTF(env, native_get_engine_version());
}

/**
 * 应用病毒库更新文件
 *
 * @param env JNI 函数固定参数
 * @param cls JNI 函数固定参数
 * @param packagePath 更新包绝对路径
 * @param packageType 更新包类型
 * @param toVersion 更新后的版本号
 * @param sigPackageType sig 更新包类型，全量包用 "total", 增量包用 "tar"
 * @return 0: 更新成功; 非 0: 更新失败
 */
JNIEXPORT jint
Java_com_antiy_avlsdk_AVLCoreEngine_installPackage(JNIEnv* env, jobject cls, jstring packagePath, jint packageType, jstring toVersion, jstring sigPackageType)
{
        jint     ret = -1;
        jboolean isCopy;

        const char* nativePackagePath    = (*env)->GetStringUTFChars(env, packagePath, &isCopy);
        const char* nativeToVersion      = (*env)->GetStringUTFChars(env, toVersion, &isCopy);
        const char* nativeSigPackageType = (*env)->GetStringUTFChars(env, sigPackageType, &isCopy);

        ret = native_install_package(nativePackagePath, packageType, nativeToVersion, nativeSigPackageType);

        return ret;
}

/**
 * 获取给定 apk 文件的 key 哈希
 *
 * @param env JNI 函数固定参数
 * @param cls JNI 函数固定参数
 * @param packagePath 要获取 key 哈希的 apk 文件路径
 * @return 字符串形式的 key 哈希
 */
JNIEXPORT jstring Java_com_antiy_avlsdk_AVLCoreEngine_getCertHash(JNIEnv* env, jobject cls, jstring packagePath)
{
        jstring ret = (*env)->NewStringUTF(env, "");

        // 读取传入参数为 native
        jboolean    isCopy       = false;
        const char* nativeString = (*env)->GetStringUTFChars(env, packagePath, &isCopy);

        // 获取 key 哈希
        char* keyHash = native_get_cert_hash(nativeString);
        if (keyHash != NULL) {
                ret = (*env)->NewStringUTF(env, keyHash);
                free(keyHash);
        }

        // 释放 java 对象关联的本地字符串
        (*env)->ReleaseStringUTFChars(env, packagePath, nativeString);

        return ret;
}

/**
 * 获取给定 apk 文件的全部 key 哈希
 *
 * @param env JNI 函数固定参数
 * @param cls JNI 函数固定参数
 * @param packagePath 要获取 key 哈希的 apk 文件路径
 * @return 字符串形式的 key 哈希
 */
JNIEXPORT jstring Java_com_antiy_avlsdk_AVLCoreEngine_getCertFullHash(JNIEnv* env, jobject cls, jstring packagePath)
{
        jstring ret = (*env)->NewStringUTF(env, "");

        // 读取传入参数为 native
        jboolean    isCopy       = false;
        const char* nativeString = (*env)->GetStringUTFChars(env, packagePath, &isCopy);

        // 获取 key 哈希
        char* keyHash = native_get_cert_full_hash(nativeString);
        if (keyHash != NULL) {
                ret = (*env)->NewStringUTF(env, keyHash);
                free(keyHash);
        }

        // 释放 java 对象关联的本地字符串
        (*env)->ReleaseStringUTFChars(env, packagePath, nativeString);

        return ret;
}
#ifdef __cplusplus
}
#endif
