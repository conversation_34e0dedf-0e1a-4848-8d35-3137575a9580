package com.antiy.avlsdk.scan;

import android.text.TextUtils;

import com.antiy.avlsdk.AVLEngine;
import com.antiy.avlsdk.callback.ScanListener;
import com.antiy.avlsdk.entity.BlackWhiteEntity;
import com.antiy.avlsdk.entity.CacheEntity;
import com.antiy.avlsdk.entity.ResultScan;
import com.antiy.avlsdk.entity.ScanErrorType;
import com.antiy.avlsdk.storage.DataManager;
import com.antiy.avlsdk.storage.EncryptorHelper;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

/**
 * Author: wangbiao
 * Date: 2024/9/9 10:04
 * Description:
 */
public class FileScanner {
    /**
     *
     * @param filePath 文件路径
     * @param isFromCloud  是否来自云查失败那边的调用，即是云查那边的调用扫描，默认为false
     * @return 扫描结果
     */
    public static ResultScan scan(String filePath,boolean isFromCloud) {
        AVLEngine.Logger.info("FileScanner scan:" + filePath);
        if (!AVLEngine.getInstance().getInitResult().isSuccess) {
            return new ResultScan("engine is init fail,please init sdk engine");
        }
        // 判断path是否存在，防止在扫描的过程中删除了某个文件
        if (!new File(filePath).exists()) {
            AVLEngine.Logger.error("File does not exist");
            ResultScan errResult = new ResultScan(false);
            errResult.errMsg = "File does not exist";
            return errResult;
        }
        //1、判断文件类型是否合法
        if (!FileChecker.getInstance().isValidType(new File(filePath))) {
            AVLEngine.Logger.info("file scan--> file type is invalid ");
            return new ResultScan(false);
        }
        String sha256 = EncryptorHelper.calcPathSHA256(filePath);
        //2、判断是否有缓存
        if (CacheManager.hasCache(sha256)) {
            AVLEngine.Logger.info("file scan--> file has cache ");
            CacheEntity entity = CacheManager.getCacheResult(sha256);
            ResultScan scan = new ResultScan();
            scan.virusName = entity.getVirusName();
            scan.sha256sum = entity.getHash();
            scan.isCloudScan = false;
            scan.isMalicious = !TextUtils.isEmpty(entity.getVirusName());
            if (!ScanResultConfig.shouldCalculateSha256()) {
                scan.sha256sum = sha256;
            }
            return scan;
        }
        //3、判断是否在黑白名单
        BlackWhiteEntity blackWhiteEntity = BlackWhiteManager.getInstance().match(sha256);
        if(blackWhiteEntity != null){
            AVLEngine.Logger.info("file scan--> file check black white list,current hash: " + sha256);
            ResultScan scan = new ResultScan();
            scan.isMalicious = blackWhiteEntity.isBlack;
            scan.virusName = blackWhiteEntity.isBlack ? "黑名单病毒" : "";
            scan.isCloudScan = false;
            if (!ScanResultConfig.shouldCalculateSha256()) {
                scan.sha256sum = sha256;
            }
            return scan;
        }

        //4、判断文件大小是否超过了云查阈值
        long length = new File(filePath).length();
        long spCloudSizeThreshold = DataManager.getInstance().getCloudSizeThreshold();
        AVLEngine.Logger.info("file length:" + length);
        AVLEngine.Logger.info("CloudSizeThreshold:" + spCloudSizeThreshold);
        // 这里需要判断是否来自云查那边的调用，不然是来自云查那边的调用，就不走云查了，否则就陷入了死循环了
        if (length > spCloudSizeThreshold && AVLEngine.getInstance().getNetworkManager().isAvailable() && !isFromCloud) {
            AVLEngine.Logger.info("file scan--> file cloud size ");
            // 使用已计算的哈希值进行云扫描，避免重复计算
            return cloudScan(filePath, sha256);
        }

        //5、判断文件是否是apk、jar、dex，如果是走手机引擎
        if (FileChecker.getInstance().isApkOrJarOrDex(new File(filePath))) {
            AVLEngine.Logger.info("file scan--> file scan android ");
            ResultScan resultScan = MobileEngine.scan(filePath,sha256);
            CacheManager.storeScanResult(filePath, resultScan.virusName, sha256);
            return resultScan;
        } else {
            AVLEngine.Logger.info("file scan--> file scan pc ");
            ResultScan resultScan = PcEngine.scan(filePath,sha256);
            CacheManager.storeScanResult(filePath, resultScan.virusName,sha256);
            return resultScan;
        }
    }
    public static ResultScan scan(String filePath) {
        return scan(filePath,false);
    }

    private static ResultScan cloudScan(String filePath) {
        return cloudScan(filePath, null);
    }

    /**
     * 云扫描方法（支持预计算哈希）
     * @param filePath 文件路径
     * @param preCalculatedHash 预计算的SHA256哈希值，如果为null则会重新计算
     * @return 扫描结果
     */
    private static ResultScan cloudScan(String filePath, String preCalculatedHash) {
        AVLEngine.Logger.info("file is too large,change cloud engine,filePath:" + filePath);

        // 云扫描超时时间（秒）
        final int CLOUD_SCAN_TIMEOUT_SECONDS = 60;

        CountDownLatch countDownLatch = new CountDownLatch(1);
        final ResultScan[] resultScan = new ResultScan[1];
        final Exception[] scanException = new Exception[1];

        List<File> files = new ArrayList<>();
        files.add(new File(filePath));

        // 如果提供了预计算的哈希值，创建哈希映射
        Map<String, String> preCalculatedHashes = null;
        if (preCalculatedHash != null) {
            preCalculatedHashes = new HashMap<>();
            preCalculatedHashes.put(filePath, preCalculatedHash);
            AVLEngine.Logger.info("Using pre-calculated hash for cloud scan: " + preCalculatedHash);
        }

        CloudScanner cloudScanner = new CloudScanner(files, new ScanListener() {
            @Override
            public void scanStart() {
                AVLEngine.Logger.info("Cloud scan started for file: " + filePath);
            }

            @Override
            public void scanStop() {
                AVLEngine.Logger.info("Cloud scan stopped for file: " + filePath);
                // 如果扫描被停止且还没有结果，创建一个失败结果
                if (resultScan[0] == null) {
                    resultScan[0] = new ResultScan("Cloud scan was stopped");
                    countDownLatch.countDown();
                }
            }

            @Override
            public void scanFinish() {
                AVLEngine.Logger.info("Cloud scan finished for file: " + filePath);
                // 如果扫描完成但没有结果，创建一个失败结果
                if (resultScan[0] == null) {
                    resultScan[0] = new ResultScan("Cloud scan finished without result");
                    countDownLatch.countDown();
                }
            }

            @Override
            public void scanCount(int count) {
                // 单文件扫描，忽略此回调
            }

            @Override
            public void scanFileStart(int index, String path) {
                AVLEngine.Logger.info("Cloud scan file start: " + path);
            }

            @Override
            public void scanFileFinish(int index, String path, ResultScan result) {
                AVLEngine.Logger.info("Cloud scan file finish: " + path + ", result: " + (result != null ? result.isMalicious : "null"));
                resultScan[0] = result;
                countDownLatch.countDown();
            }

            @Override
            public void scanError(ScanErrorType errorMsg) {
                AVLEngine.Logger.info("Cloud scan file error");
            }
        }, false, preCalculatedHashes);

        AVLEngine.Logger.info("start cloud scan with timeout: " + CLOUD_SCAN_TIMEOUT_SECONDS + " seconds");

        try {
            cloudScanner.startScan();

            // 等待扫描完成，设置超时时间
            boolean completed = countDownLatch.await(CLOUD_SCAN_TIMEOUT_SECONDS, TimeUnit.SECONDS);

            if (!completed) {
                // 超时处理
                AVLEngine.Logger.error("Cloud scan timeout for file: " + filePath);
                cloudScanner.stopScan(); // 停止扫描

                // 返回超时错误结果
                ResultScan timeoutResult = new ResultScan("Cloud scan timeout after " + CLOUD_SCAN_TIMEOUT_SECONDS + " seconds");
                return timeoutResult;
            }

            // 检查是否有异常
            if (scanException[0] != null) {
                AVLEngine.Logger.error("Cloud scan exception: " + scanException[0].getMessage());
                return new ResultScan("Cloud scan failed: " + scanException[0].getMessage());
            }

            // 检查结果是否为空
            if (resultScan[0] == null) {
                AVLEngine.Logger.error("Cloud scan returned null result");
                return new ResultScan("Cloud scan returned no result");
            }

            return resultScan[0];

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt(); // 恢复中断状态
            AVLEngine.Logger.error("Cloud scan interrupted: " + e.getMessage());
            cloudScanner.stopScan(); // 确保停止扫描
            return new ResultScan("Cloud scan was interrupted");
        } catch (Exception e) {
            AVLEngine.Logger.error("Cloud scan unexpected error: " + e.getMessage());
            cloudScanner.stopScan(); // 确保停止扫描
            return new ResultScan("Cloud scan failed: " + e.getMessage());
        }
    }
}
