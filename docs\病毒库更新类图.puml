@startuml
title 病毒库更新相关类图

class AVLEngine {
  -static volatile AVLEngine instance
  -Context mContext
  -static ResultInit resultInit
  -IScanner scanner
  +static AVLEngine getInstance()
  +ResultUpdate checkUpdate()
  +static String getVdbVersion()
}

class VirusDatabaseUpdater {
  -static volatile VirusDatabaseUpdater instance
  -String DOWNLOAD_PREFIX
  +static VirusDatabaseUpdater getInstance()
  +Single<ResultUpdate> checkUpdate()
  -Single<ResultUpdate> downloadAndUpdate(VirusUpdateEntity)
  +void update(String path)
}

class UpdateCoordinator {
  -UpdateStrategy strategy
  +ResultUpdate handleUpdate(VirusUpdateEntity, String, String)
  -boolean extractPackages(String, String)
  +String[] generatePath(VirusUpdateEntity, Pair, String)
  -Pair<UpdateTypeEnum, UpdateTypeEnum> parseUpdateTypes(String, String)
  -boolean updatePlatform(PlatformType, UpdateTypeEnum, String, Pair)
}

abstract class UpdateStrategy {
  #static final String INSTALL_FULL
  #static final String INSTALL_INCREMENT
  +boolean performUpdate(String, Pair)
  +void cleanupTempFiles()
  +void resetPcEngine()
  +boolean pcVirusUpdateCheck()
}

class PCFullUpdateStrategy {
  +boolean performUpdate(String, Pair)
  +void renameAvlPcDir()
}

class PCIncrementUpdateStrategy {
  +boolean performUpdate(String, Pair)
}

class AndroidFullUpdateStrategy {
  +boolean performUpdate(String, Pair)
}

class AndroidIncrementUpdateStrategy {
  +boolean performUpdate(String, Pair)
}

class UpdateStrategyFactory {
  +static UpdateStrategy createStrategy(UpdateTypeEnum, PlatformType)
}

enum UpdateTypeEnum {
  NONE
  INCREMENTAL
  FULL
  +static UpdateTypeEnum fromCode(int)
}

enum PlatformType {
  ANDROID
  PC
}

class VirusUpdateEntity {
  -String download_path
  -String hash
  -String status
  -String version
  -String istotal
  +String getDownloadUrl()
  +String getHash()
  +String getVersion()
  +String getIstotal()
  +boolean isUpdated()
}

class ResultUpdate {
  -boolean hasUpdate
  -boolean updateSucceeded
  +boolean isHasUpdate()
  +void setHasUpdate(boolean)
  +boolean isUpdateSucceeded()
  +void setUpdateSucceeded(boolean)
}

class AVLCoreEngine {
  +static AVLCoreEngine getInstance()
  +String getSigLibVersion()
  +int installPackage(String, int, String, String)
}

class AVLEnginePC {
  +static AVLEnginePC getInstance()
  +String getDbInfo()
  +String getAVLPCPath()
  +int init()
  +void unloadEngine()
}

AVLEngine --> VirusDatabaseUpdater: 使用
VirusDatabaseUpdater --> UpdateCoordinator: 使用
UpdateCoordinator --> UpdateStrategyFactory: 创建策略
UpdateStrategyFactory ..> UpdateStrategy: 创建
UpdateStrategy <|-- PCFullUpdateStrategy
UpdateStrategy <|-- PCIncrementUpdateStrategy
UpdateStrategy <|-- AndroidFullUpdateStrategy
UpdateStrategy <|-- AndroidIncrementUpdateStrategy
UpdateCoordinator --> UpdateStrategy: 使用
UpdateCoordinator --> VirusUpdateEntity: 处理
VirusDatabaseUpdater --> ResultUpdate: 返回
UpdateStrategy --> AVLCoreEngine: 使用
UpdateStrategy --> AVLEnginePC: 使用
PCFullUpdateStrategy --> AVLEnginePC: 使用

@enduml