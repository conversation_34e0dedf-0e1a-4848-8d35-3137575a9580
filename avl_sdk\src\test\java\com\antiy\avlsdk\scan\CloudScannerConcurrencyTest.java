package com.antiy.avlsdk.scan;

import com.antiy.avlsdk.AVLEngine;
import com.antiy.avlsdk.callback.ScanListener;
import com.antiy.avlsdk.entity.ResultScan;
import com.antiy.avlsdk.entity.ScanErrorType;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.annotation.Config;

import java.io.File;
import java.io.IOException;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * CloudScanner并发安全性测试
 * 验证多线程环境下的稳定性和资源管理
 */
@RunWith(RobolectricTestRunner.class)
@Config(sdk = 28)
public class CloudScannerConcurrencyTest {

    @Mock
    private AVLEngine mockEngine;
    
    @Mock
    private ScanListener mockListener;

    private List<File> testFiles;
    private File tempDir;

    @Before
    public void setUp() throws IOException {
        MockitoAnnotations.initMocks(this);
        
        // 创建临时测试文件
        tempDir = createTempDirectory();
        testFiles = createTestFiles(5);
        
        // Mock AVLEngine
        when(mockEngine.getInitResult()).thenReturn(createSuccessInitResult());
        when(mockEngine.getNetworkManager()).thenReturn(createMockNetworkManager());
    }

    /**
     * 测试1：并发启动多个CloudScanner实例
     * 验证：不会出现线程池自我关闭问题
     */
    @Test
    public void testConcurrentCloudScannerInstances() throws InterruptedException {
        final int CONCURRENT_SCANNERS = 5;
        final CountDownLatch startLatch = new CountDownLatch(1);
        final CountDownLatch finishLatch = new CountDownLatch(CONCURRENT_SCANNERS);
        final AtomicInteger successCount = new AtomicInteger(0);
        final AtomicInteger errorCount = new AtomicInteger(0);

        // 创建多个CloudScanner并发执行
        for (int i = 0; i < CONCURRENT_SCANNERS; i++) {
            final int scannerIndex = i;
            new Thread(() -> {
                try {
                    startLatch.await(); // 等待统一开始信号
                    
                    CloudScanner scanner = new CloudScanner(testFiles, new ScanListener() {
                        @Override
                        public void scanStart() {}

                        @Override
                        public void scanStop() {}

                        @Override
                        public void scanFinish() {
                            successCount.incrementAndGet();
                            finishLatch.countDown();
                        }

                        @Override
                        public void scanCount(int count) {}

                        @Override
                        public void scanFileStart(int index, String path) {}

                        @Override
                        public void scanFileFinish(int index, String path, ResultScan result) {}

                        @Override
                        public void scanError(ScanErrorType errorMsg) {

                        }
                    }, true);
                    
                    scanner.startScan();
                    
                } catch (Exception e) {
                    errorCount.incrementAndGet();
                    finishLatch.countDown();
                    System.err.println("Scanner " + scannerIndex + " failed: " + e.getMessage());
                }
            }).start();
        }

        // 启动所有扫描器
        startLatch.countDown();
        
        // 等待所有扫描器完成（最多30秒）
        boolean allFinished = finishLatch.await(30, TimeUnit.SECONDS);
        
        // 验证结果
        assertTrue("所有扫描器应该在30秒内完成", allFinished);
        assertTrue("至少应该有一些成功的扫描", successCount.get() > 0);
        assertEquals("不应该有错误", 0, errorCount.get());
        
        System.out.println("并发测试完成 - 成功: " + successCount.get() + ", 错误: " + errorCount.get());
    }

    /**
     * 测试2：线程池资源正确释放
     * 验证：扫描完成后线程池被正确关闭
     */
    @Test
    public void testThreadPoolResourceCleanup() throws Exception {
        final AtomicBoolean scanFinished = new AtomicBoolean(false);
        final CountDownLatch finishLatch = new CountDownLatch(1);

        CloudScanner scanner = new CloudScanner(testFiles, new ScanListener() {
            @Override
            public void scanStart() {}

            @Override
            public void scanStop() {}

            @Override
            public void scanFinish() {
                scanFinished.set(true);
                finishLatch.countDown();
            }

            @Override
            public void scanCount(int count) {}

            @Override
            public void scanFileStart(int index, String path) {}

            @Override
            public void scanFileFinish(int index, String path, ResultScan result) {}
        }, true);

        scanner.startScan();
        
        // 等待扫描完成
        assertTrue("扫描应该在15秒内完成", finishLatch.await(15, TimeUnit.SECONDS));
        assertTrue("扫描应该成功完成", scanFinished.get());

        // 等待一小段时间让线程池关闭
        Thread.sleep(1000);

        // 使用反射检查线程池状态
        ExecutorService hashExecutor = getPrivateField(scanner, "hashExecutor");
        ExecutorService scanExecutor = getPrivateField(scanner, "scanExecutor");

        assertTrue("HashExecutor应该被关闭", hashExecutor.isShutdown());
        assertTrue("ScanExecutor应该被关闭", scanExecutor.isShutdown());
        
        System.out.println("资源清理测试通过 - 线程池已正确关闭");
    }

    /**
     * 测试3：手动停止扫描的资源清理
     * 验证：调用stopScan()后线程池被强制关闭
     */
    @Test
    public void testManualStopResourceCleanup() throws Exception {
        final CountDownLatch startLatch = new CountDownLatch(1);
        final AtomicBoolean stopCalled = new AtomicBoolean(false);

        CloudScanner scanner = new CloudScanner(testFiles, new ScanListener() {
            @Override
            public void scanStart() {
                startLatch.countDown();
            }

            @Override
            public void scanStop() {
                stopCalled.set(true);
            }

            @Override
            public void scanFinish() {}

            @Override
            public void scanCount(int count) {}

            @Override
            public void scanFileStart(int index, String path) {}

            @Override
            public void scanFileFinish(int index, String path, ResultScan result) {}
        }, true);

        scanner.startScan();
        
        // 等待扫描开始
        assertTrue("扫描应该在5秒内开始", startLatch.await(5, TimeUnit.SECONDS));
        
        // 手动停止扫描
        scanner.stopScan();
        
        // 等待停止处理完成
        Thread.sleep(1000);

        // 检查线程池状态
        ExecutorService hashExecutor = getPrivateField(scanner, "hashExecutor");
        ExecutorService scanExecutor = getPrivateField(scanner, "scanExecutor");

        assertTrue("HashExecutor应该被关闭", hashExecutor.isShutdown());
        assertTrue("ScanExecutor应该被关闭", scanExecutor.isShutdown());
        
        System.out.println("手动停止测试通过 - 线程池已强制关闭");
    }

    /**
     * 测试4：预计算哈希值功能
     * 验证：使用预计算哈希值时不会重复计算
     */
    @Test
    public void testPreCalculatedHashes() throws Exception {
        // 准备预计算的哈希值
        Map<String, String> preCalculatedHashes = new HashMap<>();
        for (File file : testFiles) {
            preCalculatedHashes.put(file.getAbsolutePath(), "mock_hash_" + file.getName());
        }

        final AtomicInteger hashCalculationCount = new AtomicInteger(0);
        final CountDownLatch finishLatch = new CountDownLatch(1);

        // 创建带预计算哈希的CloudScanner
        CloudScanner scanner = new CloudScanner(testFiles, new ScanListener() {
            @Override
            public void scanStart() {}

            @Override
            public void scanStop() {}

            @Override
            public void scanFinish() {
                finishLatch.countDown();
            }

            @Override
            public void scanCount(int count) {}

            @Override
            public void scanFileStart(int index, String path) {}

            @Override
            public void scanFileFinish(int index, String path, ResultScan result) {}
        }, true, preCalculatedHashes);

        scanner.startScan();
        
        // 等待扫描完成
        assertTrue("扫描应该在10秒内完成", finishLatch.await(10, TimeUnit.SECONDS));
        
        // 验证预计算哈希值被正确存储
        Map<String, String> storedHashes = getPrivateField(scanner, "preCalculatedHashes");
        assertEquals("应该存储了所有预计算的哈希值", testFiles.size(), storedHashes.size());
        
        for (File file : testFiles) {
            String expectedHash = "mock_hash_" + file.getName();
            assertEquals("哈希值应该匹配", expectedHash, storedHashes.get(file.getAbsolutePath()));
        }
        
        System.out.println("预计算哈希测试通过 - 哈希值正确存储和使用");
    }

    // 辅助方法：使用反射获取私有字段
    @SuppressWarnings("unchecked")
    private <T> T getPrivateField(Object obj, String fieldName) throws Exception {
        Field field = obj.getClass().getDeclaredField(fieldName);
        field.setAccessible(true);
        return (T) field.get(obj);
    }

    // 辅助方法：创建临时目录
    private File createTempDirectory() throws IOException {
        File tempDir = File.createTempFile("test", "dir");
        tempDir.delete();
        tempDir.mkdirs();
        tempDir.deleteOnExit();
        return tempDir;
    }

    // 辅助方法：创建测试文件
    private List<File> createTestFiles(int count) throws IOException {
        List<File> files = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            File file = new File(tempDir, "test_file_" + i + ".txt");
            file.createNewFile();
            file.deleteOnExit();
            files.add(file);
        }
        return files;
    }

    // 辅助方法：创建成功的初始化结果
    private Object createSuccessInitResult() {
        // 这里需要根据实际的InitResult类来实现
        // 返回一个表示成功初始化的对象
        return new Object() {
            public boolean isSuccess = true;
        };
    }

    // 辅助方法：创建Mock网络管理器
    private Object createMockNetworkManager() {
        return new Object() {
            public boolean isAvailable() {
                return true;
            }
        };
    }
}
