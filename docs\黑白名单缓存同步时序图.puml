@startuml 黑白名单缓存同步时序图
title 黑白名单更新时缓存同步机制

participant "客户端" as Client
participant "Config" as Config
participant "DataManager" as DataManager
participant "DatabaseHelper" as DatabaseHelper
participant "SQLite数据库" as Database

== 用户更新黑白名单 ==

Client -> Config: updateBlackWhiteList(map)
activate Config

note right of Config
  map包含:
  - "hash1" -> true (白名单)
  - "hash2" -> false (黑名单)
  - "hash3" -> true (白名单)
end note

Config -> Config: this.blackWhiteMap = map
Config -> DataManager: saveBlackWhiteList(map)
activate DataManager

== 保存黑白名单到数据库 ==

loop 遍历map中的每个entry
    DataManager -> DatabaseHelper: insertBlackWhiteData(hash, isBlack.toString())
    activate DatabaseHelper
    DatabaseHelper -> Database: INSERT OR REPLACE INTO black_white_list
    Database --> DatabaseHelper: 操作完成
    deactivate DatabaseHelper
end

DataManager --> Config: 黑白名单保存完成
deactivate DataManager

== 同步更新缓存数据 ==

Config -> DataManager: updateCacheForBlackWhiteList(map)
activate DataManager

note right of DataManager
  新增方法：专门处理缓存同步
  避免缓存优先级高于黑白名单的冲突
end note

loop 遍历map中的每个hash
    DataManager -> DatabaseHelper: getCacheData(hash)
    activate DatabaseHelper
    DatabaseHelper -> Database: SELECT * FROM cache WHERE hash = ?
    Database --> DatabaseHelper: CacheEntity或null
    deactivate DatabaseHelper
    
    alt 缓存中存在该hash的数据
        DataManager -> DataManager: 根据黑白名单设置确定新的virusName
        
        note right of DataManager
          白名单(true): virusName = ""
          黑名单(false): virusName = "黑名单病毒"
          保持原有timestamp不变
        end note
        
        DataManager -> DatabaseHelper: insertCacheData(hash, newVirusName, originalTimestamp)
        activate DatabaseHelper
        DatabaseHelper -> Database: INSERT OR REPLACE INTO cache
        Database --> DatabaseHelper: 缓存更新完成
        deactivate DatabaseHelper
        
        DataManager -> DataManager: 记录日志：缓存已更新
    else 缓存中不存在该hash
        note right of DataManager
          跳过，不需要更新
          只有已存在的缓存才需要同步
        end note
    end
end

DataManager --> Config: 缓存同步完成
deactivate DataManager

Config --> Client: 黑白名单更新完成
deactivate Config

== 后续扫描时的优先级验证 ==

note over Client, Database
  现在当文件扫描时：
  1. 首先检查缓存（优先级最高）
  2. 缓存中的结果已与黑白名单设置同步
  3. 不会出现缓存与黑白名单冲突的情况
end note

@enduml
