package com.antiy.avlsdk.storage;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

import android.content.Context;

import com.antiy.avlsdk.AVLEngine;
import com.antiy.avlsdk.entity.CacheEntity;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.RuntimeEnvironment;

import java.util.HashMap;

/**
 * 测试DataManager中黑白名单更新缓存的功能
 * 验证当更新黑白名单时，缓存中对应的数据能够正确同步更新
 */
@RunWith(RobolectricTestRunner.class)
public class DataManagerBlackWhiteCacheTest {

    @Mock
    private DatabaseHelper mockDbHelper;
    
    @Mock
    private AVLEngine mockEngine;
    
    private DataManager dataManager;
    private Context context;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        context = RuntimeEnvironment.getApplication();
        
        // Mock AVLEngine
        try (MockedStatic<AVLEngine> mockedEngine = mockStatic(AVLEngine.class)) {
            mockedEngine.when(AVLEngine::getInstance).thenReturn(mockEngine);
            when(mockEngine.getContext()).thenReturn(context);
            
            dataManager = DataManager.getInstance();
        }
        
        // 使用反射设置mock的DatabaseHelper
        try {
            java.lang.reflect.Field dbHelperField = DataManager.class.getDeclaredField("dbHelper");
            dbHelperField.setAccessible(true);
            dbHelperField.set(dataManager, mockDbHelper);
        } catch (Exception e) {
            fail("Failed to set mock DatabaseHelper: " + e.getMessage());
        }
    }

    @Test
    public void testUpdateCacheForBlackWhiteList_WhiteList() {
        // 准备测试数据
        String testHash = "test_hash_123";
        HashMap<String, Boolean> blackWhiteMap = new HashMap<>();
        blackWhiteMap.put(testHash, true); // 设置为白名单
        
        // Mock缓存中已存在该hash的数据
        CacheEntity existingCache = new CacheEntity(testHash, "原有病毒名", System.currentTimeMillis());
        when(mockDbHelper.getCacheData(testHash)).thenReturn(existingCache);
        
        // 执行测试
        dataManager.updateCacheForBlackWhiteList(blackWhiteMap);
        
        // 验证：应该调用insertCacheData更新缓存，virusName为空字符串（白名单）
        verify(mockDbHelper).insertCacheData(testHash, "", existingCache.getTimestamp());
    }

    @Test
    public void testUpdateCacheForBlackWhiteList_BlackList() {
        // 准备测试数据
        String testHash = "test_hash_456";
        HashMap<String, Boolean> blackWhiteMap = new HashMap<>();
        blackWhiteMap.put(testHash, false); // 设置为黑名单
        
        // Mock缓存中已存在该hash的数据
        CacheEntity existingCache = new CacheEntity(testHash, "", System.currentTimeMillis());
        when(mockDbHelper.getCacheData(testHash)).thenReturn(existingCache);
        
        // 执行测试
        dataManager.updateCacheForBlackWhiteList(blackWhiteMap);
        
        // 验证：应该调用insertCacheData更新缓存，virusName为"黑名单病毒"
        verify(mockDbHelper).insertCacheData(testHash, "黑名单病毒", existingCache.getTimestamp());
    }

    @Test
    public void testUpdateCacheForBlackWhiteList_NoCacheExists() {
        // 准备测试数据
        String testHash = "test_hash_789";
        HashMap<String, Boolean> blackWhiteMap = new HashMap<>();
        blackWhiteMap.put(testHash, true);
        
        // Mock缓存中不存在该hash的数据
        when(mockDbHelper.getCacheData(testHash)).thenReturn(null);
        
        // 执行测试
        dataManager.updateCacheForBlackWhiteList(blackWhiteMap);
        
        // 验证：不应该调用insertCacheData，因为缓存中没有对应数据
        verify(mockDbHelper, never()).insertCacheData(anyString(), anyString(), anyLong());
    }

    @Test
    public void testUpdateCacheForBlackWhiteList_NullMap() {
        // 执行测试：传入null
        dataManager.updateCacheForBlackWhiteList(null);
        
        // 验证：不应该有任何数据库操作
        verify(mockDbHelper, never()).getCacheData(anyString());
        verify(mockDbHelper, never()).insertCacheData(anyString(), anyString(), anyLong());
    }

    @Test
    public void testUpdateCacheForBlackWhiteList_MultipleEntries() {
        // 准备测试数据：多个hash值
        HashMap<String, Boolean> blackWhiteMap = new HashMap<>();
        blackWhiteMap.put("hash1", true);  // 白名单
        blackWhiteMap.put("hash2", false); // 黑名单
        blackWhiteMap.put("hash3", true);  // 白名单
        
        long timestamp1 = System.currentTimeMillis();
        long timestamp2 = System.currentTimeMillis() + 1000;
        
        // Mock缓存数据
        when(mockDbHelper.getCacheData("hash1")).thenReturn(new CacheEntity("hash1", "病毒1", timestamp1));
        when(mockDbHelper.getCacheData("hash2")).thenReturn(new CacheEntity("hash2", "", timestamp2));
        when(mockDbHelper.getCacheData("hash3")).thenReturn(null); // 缓存中不存在
        
        // 执行测试
        dataManager.updateCacheForBlackWhiteList(blackWhiteMap);
        
        // 验证：只有存在缓存的hash才会被更新
        verify(mockDbHelper).insertCacheData("hash1", "", timestamp1);
        verify(mockDbHelper).insertCacheData("hash2", "黑名单病毒", timestamp2);
        verify(mockDbHelper, never()).insertCacheData(eq("hash3"), anyString(), anyLong());
    }

    @Test
    public void testUpdateCacheForBlackWhiteList_PreservesTimestamp() {
        // 准备测试数据
        String testHash = "timestamp_test_hash";
        long originalTimestamp = 1234567890L;
        HashMap<String, Boolean> blackWhiteMap = new HashMap<>();
        blackWhiteMap.put(testHash, true);
        
        // Mock缓存中已存在该hash的数据，使用特定的时间戳
        CacheEntity existingCache = new CacheEntity(testHash, "原有病毒名", originalTimestamp);
        when(mockDbHelper.getCacheData(testHash)).thenReturn(existingCache);
        
        // 执行测试
        dataManager.updateCacheForBlackWhiteList(blackWhiteMap);
        
        // 验证：时间戳应该保持不变
        verify(mockDbHelper).insertCacheData(testHash, "", originalTimestamp);
    }
}
