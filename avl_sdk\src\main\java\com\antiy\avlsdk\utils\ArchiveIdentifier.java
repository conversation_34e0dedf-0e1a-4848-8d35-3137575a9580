package com.antiy.avlsdk.utils;

import com.antiy.avlsdk.AVLEngine;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;
import java.util.zip.ZipException;
import java.util.zip.ZipFile;

public class ArchiveIdentifier {

    // ZIP文件的魔数 (PK\03\04)
    private static final byte[] ZIP_MAGIC_NUMBER = {0x50, 0x4B, 0x03, 0x04};
    // DEX文件的魔数 (dex\n)
    private static final byte[] DEX_MAGIC_NUMBER = {0x64, 0x65, 0x78, 0x0a};

    // APK文件的特征文件名
    private static final String ANDROID_MANIFEST_XML = "AndroidManifest.xml";
    // private static final String CLASSES_DEX = "classes.dex"; // 可选的更强检查

    // JAR文件的特征文件名
    private static final String META_INF_MANIFEST_MF = "META-INF/MANIFEST.MF";

    /**
     * 文件类型的枚举
     */
    public enum ArchiveType {
        APK,          // Android应用包
        JAR,          // Java归档文件
        DEX,          // Dalvik可执行文件
        ZIP,          // 普通ZIP文件
        UNKNOWN,      // 未知文件类型 (替代NOT_ZIP，更通用)
        READ_ERROR,   // 读取文件时发生错误
        CORRUPTED_ZIP // ZIP文件已损坏
    }

    /**
     * 通过文件头判断文件是否是ZIP格式。
     *
     * @param file 要检查的文件
     * @return 如果是ZIP文件返回true，否则返回false。
     */
    private static boolean isZipByHeader(File file) {
        // 基本的文件校验
        if (file == null || !file.exists() || !file.isFile() || file.length() < 4) {
            return false;
        }
        // 读取并比较魔数
        return checkMagicNumber(file, ZIP_MAGIC_NUMBER);
    }

    /**
     * 通过文件头判断文件是否是DEX格式。
     *
     * @param file 要检查的文件
     * @return 如果是DEX文件返回true，否则返回false。
     */
    private static boolean isDexByHeader(File file) {
        // DEX文件的完整头部是8字节，但前4字节的魔数是主要特征
        if (file == null || !file.exists() || !file.isFile() || file.length() < 8) {
            return false;
        }
        return checkMagicNumber(file, DEX_MAGIC_NUMBER);
    }

    /**
     * 检查文件头部是否与给定的魔数匹配。这是一个辅助方法，避免代码重复。
     *
     * @param file         要检查的文件
     * @param magicNumber  要匹配的魔数 byte 数组
     * @return 如果匹配返回 true，否则返回 false
     */
    private static boolean checkMagicNumber(File file, final byte[] magicNumber) {
        byte[] header = new byte[magicNumber.length];
        try (InputStream fis = new FileInputStream(file)) {
            int bytesRead = fis.read(header);
            if (bytesRead < magicNumber.length) {
                return false; // 读取的字节数不足
            }
            return Arrays.equals(header, magicNumber);
        } catch (IOException e) {
            AVLEngine.Logger.error("IO error occurred while reading the file: " + file.getName() + ",error msg:" + Arrays.toString(e.getStackTrace()));
            return false;
        }
    }


    /**
     * 判断给定文件的类型 (APK, JAR, DEX, ZIP, 或其他)。
     *
     * @param filePath 文件路径
     * @return ArchiveType 枚举表示的文件类型
     */
    public static ArchiveType identifyFileType(String filePath) {
        if (filePath == null || filePath.isEmpty()) {
            return ArchiveType.READ_ERROR;
        }
        File file = new File(filePath);
        return identifyFileType(file);
    }

    /**
     * 判断给定文件的类型 (APK, JAR, DEX, ZIP, 或其他)。
     *
     * @param file 要检查的文件对象
     * @return ArchiveType 枚举表示的文件类型
     */
    public static ArchiveType identifyFileType(File file) {
        // 首先进行基本的文件有效性检查
        if (file == null || !file.exists() || !file.isFile()) {
            return ArchiveType.READ_ERROR;
        }

        // 1. 判断是否为ZIP衍生格式 (ZIP, APK, JAR)
        if (isZipByHeader(file)) {
            // 文件头表明是ZIP，现在尝试用ZipFile打开并检查内部结构
            try (ZipFile zipFile = new ZipFile(file)) {
                // 检查是否为APK
                if (zipFile.getEntry(ANDROID_MANIFEST_XML) != null) {
                    return ArchiveType.APK;
                }

                // 检查是否为JAR
                if (zipFile.getEntry(META_INF_MANIFEST_MF) != null) {
                    return ArchiveType.JAR;
                }

                // 如果以上特征都不满足，但确实是ZIP文件
                return ArchiveType.ZIP;

            } catch (ZipException e) {
                // 使用日志框架会更好
                AVLEngine.Logger.error("The file may be a damaged ZIP package: " + file.getName() + " - " + Arrays.toString(e.getStackTrace()));
                return ArchiveType.CORRUPTED_ZIP;
            } catch (IOException e) {
                AVLEngine.Logger.error("IO error occurred while reading the contents of ZIP file: " + file.getName() + " - " + Arrays.toString(e.getStackTrace()));
                return ArchiveType.READ_ERROR;
            }
        }

        // 2. 如果不是ZIP，判断是否为DEX文件
        if (isDexByHeader(file)) {
            return ArchiveType.DEX;
        }

        // 3. 如果以上都不是，则为未知类型
        return ArchiveType.UNKNOWN;
    }
}