# License 刷新机制优化技术方案

> 文档作者：Cascade (gemini 2.5 pro)
> 
> 日期：2025-06-26

## 1. 背景

当前的 `cccj-sdk` 在初始化阶段（`AVLEngine.init`）会启动一个后台线程尝试刷新 `license`（即 `token`）。在车机环境中，SDK 初始化时，其依赖的网络模块可能尚未准备就绪，导致这次刷新请求必定失败。这使得 `license` 的更新机制在实际应用中变得不可靠，完全依赖于下次 SDK 重启时网络是否可用。

## 2. 设计目标

- **解耦依赖**：将 `license` 刷新逻辑与车机启动时的不稳定环境解耦，提高刷新成功率。
- **健壮可靠**：确保刷新机制能够应对网络波动、并发请求等异常情况，不会导致 `license` 状态锁死或数据不一致。
- **高效节约**：避免不必要的网络请求，只在需要时进行刷新，节省设备和服务器资源。

## 3. 方案设计

核心思想是将 `license` 刷新的主要触发时机从 **SDK 初始化时** 转移到 **发起扫描任务时**，并结合 **“时间阈值检查 + 并发锁”** 的策略来保证其健壮性和高效性。

### 3.1. 刷新触发机制

1.  **主要触发点**：在 `AVLEngine.scanDir()` 方法的入口处，增加一个 `license` 检查与更新的调用，例如 `LicenseManager.getInstance().ensureLicenseIsValid()`。
2.  **保留初始化刷新**：保留在 `AVLEngine.init()` 中的刷新逻辑。这主要用于应对首次安装或本地无任何 `license` 信息的冷启动场景。此次刷新失败不应影响 SDK 的初始化结果。

### 3.2. 刷新策略

#### 3.2.1. 时间阈值检查

- 在 `AuthInfoManager` 中增加一个持久化存储项：`lastSuccessfulRefreshTime` (long)，用于记录上一次成功刷新 `license` 的时间戳。
- 在 `ensureLicenseIsValid()` 方法中，首先检查 `System.currentTimeMillis() - lastSuccessfulRefreshTime` 是否大于一个预设的阈值 `REFRESH_THRESHOLD`（例如 `24 * 60 * 60 * 1000L`，即24小时）。
- 只有当超出时间阈值时，才需要执行刷新逻辑。否则，直接跳过，不产生任何额外开销。

#### 3.2.2. 并发控制（解决“请求进行中”问题）

为防止因网络延迟导致在前一个刷新请求未完成时，后续的扫描任务重复发起刷新，我们引入一个静态的并发锁。

- 在 `LicenseManager` 中定义一个静态的原子布尔标志位：
  ```java
  private static final AtomicBoolean isRefreshing = new AtomicBoolean(false);
  ```
- 当时间阈值检查通过后，使用 `isRefreshing.compareAndSet(false, true)` 尝试获取锁。
  - **获取成功**（返回 `true`）：表示当前没有刷新任务在进行，可以启动一个新的后台刷新任务。
  - **获取失败**（返回 `false`）：表示已有刷新任务正在进行，本次请求应直接静默跳过。
- **锁的释放**：这是最关键的一步。必须在 `requestAuthToken` 的异步网络回调 `onFinish` 和 `onError` 的 `finally` 代码块中调用 `isRefreshing.set(false)` 来释放锁。这能确保无论刷新成功、失败或在回调处理中发生异常，锁都一定会被释放，从而避免死锁。

## 4. 代码实现细节（伪代码）

**1. AVLEngine.java**

```java
// AVLEngine.java
public void scanDir(String path, ScanListener listener) {
    // 在扫描开始时，检查并按需刷新 License
    LicenseManager.getInstance().ensureLicenseIsValid();

    // ... 原有的扫描逻辑 ...
}
```

**2. LicenseManager.java**

```java
// LicenseManager.java
public class LicenseManager {
    private static final long REFRESH_THRESHOLD = 24 * 60 * 60 * 1000L; // 24小时
    private static final AtomicBoolean isRefreshing = new AtomicBoolean(false);
    private final AuthInfoManager authInfoManager;

    // ...

    public void ensureLicenseIsValid() {
        long lastRefreshTime = authInfoManager.getLastSuccessfulRefreshTime();
        if (System.currentTimeMillis() - lastRefreshTime < REFRESH_THRESHOLD) {
            return; // License 仍然在有效期内，无需刷新
        }

        if (isRefreshing.compareAndSet(false, true)) {
            Logger.info("Starting a background license refresh.");
            try {
                // 启动异步刷新，注意传递 uuid
                String uuid = authInfoManager.getUuid();
                if (uuid != null) {
                    requestAuthToken(uuid);
                } else {
                    isRefreshing.set(false); // 没有 uuid，无法刷新，直接释放锁
                }
            } catch (Exception e) {
                isRefreshing.set(false); // 启动过程异常，释放锁
            }
        } else {
            Logger.info("License refresh is already in progress. Skipping.");
        }
    }

    public void requestAuthToken(String uuid) {
        try {
            // ... 准备网络请求参数 ...

            AVLEngine.getInstance().getNetworkManager().request(
                SdkConst.AUTH_URL,
                RequestMethod.POST,
                jsonObject,
                new RequestCallback() {
                    @Override
                    public void onError(String msg) {
                        try {
                            Logger.error("Auth request failed: " + msg);
                        } finally {
                            isRefreshing.set(false); // 关键：在 finally 块中释放锁
                        }
                    }

                    @Override
                    public void onFinish(int code, Object responseBody) {
                        try {
                            // ... 处理响应的逻辑 ...
                            if (/* 响应码正确且 token 解析成功 */) {
                                // ... 保存新的 license ...

                                // 只有在完全成功后，才更新“上次成功刷新时间”
                                authInfoManager.saveLastSuccessfulRefreshTime(System.currentTimeMillis());
                            }
                        } finally {
                            isRefreshing.set(false); // 关键：在 finally 块中释放锁
                        }
                    }
                }
            );
        } catch (Exception e) {
            isRefreshing.set(false); // 确保同步异常也能释放锁
            Logger.error("Request auth token failed: " + e.getMessage());
        }
    }
}
```

**3. AuthInfoManager.java**

需要增加 `getLastSuccessfulRefreshTime()` 和 `saveLastSuccessfulRefreshTime()` 方法，用于读写持久化存储的时间戳。

## 5. 风险与缓解措施

- **风险**：长时间不进行扫描的设备，其 `license` 可能会过期。
  - **评估**：此风险可接受。该方案的设计确保了只要用户发起下一次扫描，`license` 就会被更新。对于车机这种常规使用的设备，完全不扫描的场景较少。
- **风险**：并发控制逻辑实现不当，可能导致死锁。
  - **缓解**：严格遵守在 `finally` 块中释放 `AtomicBoolean` 锁的原则，可以完全规避此风险。

## 6. 总结

该方案将 `license` 刷新与不稳定的启动环境解耦，通过“按需检查、加锁刷新”的策略，实现了一个健壮、高效且对开发者透明的 `license` 自动续期机制。它解决了现有方案的痛点，并能很好地适应车机复杂的运行环境。

## 7. 流程图与时序图

### 7.1. 刷新逻辑流程图

```mermaid
flowchart TD
    A[开始调用 ensureLicenseIsValid] --> B{上次成功刷新是否超24小时?};
    B -- 否 --> G[结束];
    B -- 是 --> C{compareAndSet(false, true) 成功?};
    C -- 否/已有任务在进行 --> G;
    C -- 是/获取到锁 --> D[后台线程异步调用 requestAuthToken];
    D --> E[主线程立即返回, 继续执行扫描];
    E --> G;
```

### 7.2. 核心交互时序图

```mermaid
sequenceDiagram
    participant App as 调用方
    participant AVLEngine
    participant LicenseManager
    participant Network as 网络/服务器

    App->>AVLEngine: scanDir(path)
    AVLEngine->>LicenseManager: ensureLicenseIsValid()
    
    alt 时间超阈值且无刷新任务进行中
        Note over LicenseManager: 检查时间戳, 成功获取刷新锁
        LicenseManager->>LicenseManager: 后台启动 requestAuthToken()
        
        par 后台刷新任务
            LicenseManager->>Network: 发送认证 HTTP 请求
            Network-->>LicenseManager: 异步回调 onFinish() / onError()
            Note over LicenseManager: 处理响应, 存储Token
            Note over LicenseManager: 在finally块中释放刷新锁
        and 主流程 (立即发生)
            LicenseManager-->>AVLEngine: 立即返回
            AVLEngine-->>App: 开始执行扫描...
        end
    else 时间未超阈值或已有刷新任务
        Note over LicenseManager: 直接跳过刷新逻辑
        LicenseManager-->>AVLEngine: 立即返回
        AVLEngine-->>App: 开始执行扫描...
    end
```
