package com.antiy.avlsdk.callback;

/**
 * 扫描器接口
 * 定义了扫描操作的基本行为，包括开始、暂停、恢复和停止扫描等功能
 * 实现此接口的类需要提供完整的扫描生命周期管理
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface IScanner {
    /**
     * 开始扫描
     * 初始化扫描环境并启动扫描进程
     */
    void startScan();

    /**
     * 暂停扫描
     * 临时暂停当前的扫描操作，可以通过resumeScan恢复
     */
    void pauseScan();

    /**
     * 恢复扫描
     * 从暂停状态恢复扫描操作
     */
    void resumeScan();

    /**
     * 停止扫描
     * 完全停止扫描操作并清理相关资源
     */
    void stopScan();

    /**
     * 检查扫描是否处于暂停状态
     *
     * @return 如果扫描已暂停返回true，否则返回false
     */
    boolean isPaused();

    /**
     * 检查扫描是否已停止
     *
     * @return 如果扫描已停止返回true，否则返回false
     */
    boolean isStopped();
}
