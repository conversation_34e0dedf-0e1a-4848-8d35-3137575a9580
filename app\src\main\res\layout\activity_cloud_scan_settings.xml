<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_gray"
    android:fitsSystemWindows="true">

    <!-- 顶部标题栏 -->
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:fitsSystemWindows="true">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/white"
            app:contentInsetStartWithNavigation="0dp"
            app:layout_scrollFlags="scroll|enterAlways">

            <ImageButton
                android:id="@+id/btnBack"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_back"
                android:contentDescription="返回" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="32dp"
                android:text="云查杀设置"
                android:textColor="@color/black"
                android:textSize="18sp" />

        </androidx.appcompat.widget.Toolbar>
    </com.google.android.material.appbar.AppBarLayout>

    <!-- 内容区域 -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- 云查杀配置 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="云查杀配置"
                        android:textColor="@color/black"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="启用云查杀"
                            android:textColor="@color/black"
                            android:textSize="14sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_below="@id/switchCloud"
                            android:text="使用云端数据库增强检测能力"
                            android:textColor="@color/gray"
                            android:textSize="12sp" />

                        <com.google.android.material.switchmaterial.SwitchMaterial
                            android:id="@+id/switchCloud"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentEnd="true" />
                    </RelativeLayout>

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="仅在WiFi下使用云查杀"
                            android:textColor="@color/black"
                            android:textSize="14sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_below="@id/switchWifiOnly"
                            android:text="节省移动数据流量"
                            android:textColor="@color/gray"
                            android:textSize="12sp" />

                        <com.google.android.material.switchmaterial.SwitchMaterial
                            android:id="@+id/switchWifiOnly"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentEnd="true" />
                    </RelativeLayout>

                    <!-- 云查阈值（文件数）设置卡片 -->
                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="16dp"
                        android:layout_marginTop="8dp"
                        app:cardCornerRadius="12dp"
                        app:cardElevation="2dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:padding="16dp">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="云查阈值（文件数）"
                                android:textColor="@color/black"
                                android:textSize="15sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="超过该文件数时将自动启用云查"
                                android:textColor="@color/gray"
                                android:textSize="12sp"
                                android:layout_marginTop="4dp"/>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:gravity="center_vertical"
                                android:layout_marginTop="12dp">

                                <EditText
                                    android:id="@+id/editCloudThreshold"
                                    android:layout_width="0dp"
                                    android:layout_height="40dp"
                                    android:layout_weight="1"
                                    android:background="@drawable/bg_edittext_rounded"
                                    android:hint="请输入文件数"
                                    android:inputType="number"
                                    android:paddingHorizontal="12dp"
                                    android:textSize="14sp" />

                                <com.google.android.material.button.MaterialButton
                                    android:id="@+id/btnSaveCloudThreshold"
                                    android:layout_width="wrap_content"
                                    android:layout_height="40dp"
                                    android:text="保存"
                                    android:layout_marginStart="12dp"/>
                            </LinearLayout>
                        </LinearLayout>
                    </androidx.cardview.widget.CardView>

                    <!-- 云查文件大小阈值（MB）设置卡片 -->
                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="16dp"
                        android:layout_marginTop="8dp"
                        app:cardCornerRadius="12dp"
                        app:cardElevation="2dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:padding="16dp">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="云查文件大小阈值（MB）"
                                android:textColor="@color/black"
                                android:textSize="15sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="超过该大小的文件将启用云查"
                                android:textColor="@color/gray"
                                android:textSize="12sp"
                                android:layout_marginTop="4dp"/>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:gravity="center_vertical"
                                android:layout_marginTop="12dp">

                                <EditText
                                    android:id="@+id/editCloudSizeThreshold"
                                    android:layout_width="0dp"
                                    android:layout_height="40dp"
                                    android:layout_weight="1"
                                    android:background="@drawable/bg_edittext_rounded"
                                    android:hint="请输入大小（MB）"
                                    android:inputType="number"
                                    android:paddingHorizontal="12dp"
                                    android:textSize="14sp" />

                                <com.google.android.material.button.MaterialButton
                                    android:id="@+id/btnSaveCloudSizeThreshold"
                                    android:layout_width="wrap_content"
                                    android:layout_height="40dp"
                                    android:text="保存"

                                    android:layout_marginStart="12dp"/>
                            </LinearLayout>
                        </LinearLayout>
                    </androidx.cardview.widget.CardView>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- 云查杀说明 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="关于云查杀"
                        android:textColor="@color/black"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="云查杀功能将扫描结果与云端数据库进行比对，提高检测准确率。开启此功能可能会消耗少量网络流量。"
                        android:textColor="@color/gray"
                        android:textSize="14sp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="注意：开启'仅在WiFi下使用云查杀'选项后，在移动网络下将不会使用云查杀功能，这可能会降低检测准确率，但可以节省移动数据流量。"
                        android:textColor="@color/gray"
                        android:textSize="14sp" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</androidx.coordinatorlayout.widget.CoordinatorLayout> 