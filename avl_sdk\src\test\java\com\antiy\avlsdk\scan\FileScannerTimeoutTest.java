package com.antiy.avlsdk.scan;

import com.antiy.avlsdk.AVLEngine;
import com.antiy.avlsdk.entity.ResultScan;
import com.antiy.avlsdk.storage.DataManager;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.annotation.Config;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.lang.reflect.Method;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * FileScanner超时机制和性能优化测试
 * 验证CountDownLatch超时处理和哈希计算优化
 */
@RunWith(RobolectricTestRunner.class)
@Config(sdk = 28)
public class FileScannerTimeoutTest {

    @Mock
    private AVLEngine mockEngine;
    
    @Mock
    private DataManager mockDataManager;

    private File testFile;
    private File tempDir;

    @Before
    public void setUp() throws IOException {
        MockitoAnnotations.initMocks(this);
        
        // 创建临时测试文件
        tempDir = createTempDirectory();
        testFile = createLargeTestFile();
        
        // Mock AVLEngine和相关组件
        mockStatic(AVLEngine.class);
        when(AVLEngine.getInstance()).thenReturn(mockEngine);
        when(mockEngine.getInitResult()).thenReturn(createSuccessInitResult());
        when(mockEngine.getNetworkManager()).thenReturn(createMockNetworkManager());
        
        // Mock DataManager
        mockStatic(DataManager.class);
        when(DataManager.getInstance()).thenReturn(mockDataManager);
        when(mockDataManager.getCloudSizeThreshold()).thenReturn(1024L); // 1KB阈值
    }

    /**
     * 测试1：云扫描超时机制
     * 验证：CountDownLatch超时后正确返回超时错误
     */
    @Test
    public void testCloudScanTimeout() throws Exception {
        // 创建一个会超时的CloudScanner Mock
        CloudScanner mockCloudScanner = createTimeoutCloudScanner();
        
        // 使用反射调用cloudScan方法
        Method cloudScanMethod = FileScanner.class.getDeclaredMethod("cloudScan", String.class, String.class);
        cloudScanMethod.setAccessible(true);
        
        long startTime = System.currentTimeMillis();
        ResultScan result = (ResultScan) cloudScanMethod.invoke(null, testFile.getAbsolutePath(), "test_hash");
        long endTime = System.currentTimeMillis();
        
        // 验证超时处理
        assertNotNull("应该返回结果", result);
        assertTrue("应该包含超时错误信息", result.errMsg != null && result.errMsg.contains("timeout"));
        
        // 验证超时时间合理（应该在60秒左右，允许一些误差）
        long duration = endTime - startTime;
        assertTrue("超时时间应该合理", duration >= 59000 && duration <= 65000); // 59-65秒
        
        System.out.println("超时测试通过 - 耗时: " + duration + "ms, 错误信息: " + result.errMsg);
    }

    /**
     * 测试2：中断处理机制
     * 验证：线程中断时正确恢复中断状态
     */
    @Test
    public void testInterruptHandling() throws Exception {
        final AtomicBoolean interruptHandled = new AtomicBoolean(false);
        final CountDownLatch testLatch = new CountDownLatch(1);
        
        Thread testThread = new Thread(() -> {
            try {
                // 使用反射调用cloudScan方法
                Method cloudScanMethod = FileScanner.class.getDeclaredMethod("cloudScan", String.class, String.class);
                cloudScanMethod.setAccessible(true);
                
                // 在另一个线程中中断当前线程
                new Thread(() -> {
                    try {
                        Thread.sleep(1000); // 等待1秒后中断
                        Thread.currentThread().interrupt();
                    } catch (InterruptedException e) {
                        // 忽略
                    }
                }).start();
                
                ResultScan result = (ResultScan) cloudScanMethod.invoke(null, testFile.getAbsolutePath(), "test_hash");
                
                // 验证中断处理
                if (Thread.currentThread().isInterrupted()) {
                    interruptHandled.set(true);
                }
                
                assertNotNull("应该返回结果", result);
                assertTrue("应该包含中断错误信息", result.errMsg != null && result.errMsg.contains("interrupted"));
                
            } catch (Exception e) {
                System.err.println("中断测试异常: " + e.getMessage());
            } finally {
                testLatch.countDown();
            }
        });
        
        testThread.start();
        assertTrue("测试应该在10秒内完成", testLatch.await(10, TimeUnit.SECONDS));
        assertTrue("中断状态应该被正确处理", interruptHandled.get());
        
        System.out.println("中断处理测试通过");
    }

    /**
     * 测试3：哈希计算性能优化
     * 验证：使用预计算哈希值时避免重复计算
     */
    @Test
    public void testHashCalculationOptimization() throws Exception {
        final AtomicLong hashCalculationTime = new AtomicLong(0);
        final AtomicBoolean hashReused = new AtomicBoolean(false);
        
        // 模拟哈希计算耗时
        String preCalculatedHash = "pre_calculated_hash_value";
        
        // 第一次调用：计算哈希值
        long startTime1 = System.currentTimeMillis();
        ResultScan result1 = FileScanner.scan(testFile.getAbsolutePath(), false);
        long endTime1 = System.currentTimeMillis();
        long firstCallTime = endTime1 - startTime1;
        
        // 第二次调用：使用预计算的哈希值
        Method cloudScanMethod = FileScanner.class.getDeclaredMethod("cloudScan", String.class, String.class);
        cloudScanMethod.setAccessible(true);
        
        long startTime2 = System.currentTimeMillis();
        ResultScan result2 = (ResultScan) cloudScanMethod.invoke(null, testFile.getAbsolutePath(), preCalculatedHash);
        long endTime2 = System.currentTimeMillis();
        long secondCallTime = endTime2 - startTime2;
        
        // 验证性能优化效果
        assertNotNull("第一次调用应该返回结果", result1);
        assertNotNull("第二次调用应该返回结果", result2);
        
        System.out.println("性能优化测试 - 第一次: " + firstCallTime + "ms, 第二次: " + secondCallTime + "ms");
        System.out.println("哈希计算优化测试通过");
    }

    /**
     * 测试4：异常场景的错误处理
     * 验证：各种异常情况下的错误处理机制
     */
    @Test
    public void testExceptionHandling() throws Exception {
        // 测试文件不存在的情况
        ResultScan result1 = FileScanner.scan("/non/existent/file.txt", false);
        assertNotNull("应该返回结果", result1);
        assertFalse("应该不是恶意文件", result1.isMalicious);
        assertTrue("应该包含文件不存在错误", result1.errMsg != null && result1.errMsg.contains("not exist"));
        
        // 测试引擎未初始化的情况
        when(mockEngine.getInitResult()).thenReturn(createFailedInitResult());
        ResultScan result2 = FileScanner.scan(testFile.getAbsolutePath(), false);
        assertNotNull("应该返回结果", result2);
        assertTrue("应该包含引擎初始化错误", result2.errMsg != null && result2.errMsg.contains("init fail"));
        
        System.out.println("异常处理测试通过");
    }

    /**
     * 测试5：网络不可用时的处理
     * 验证：网络不可用时不会触发云扫描
     */
    @Test
    public void testNetworkUnavailableHandling() throws Exception {
        // Mock网络不可用
        when(mockEngine.getNetworkManager()).thenReturn(createUnavailableNetworkManager());
        
        ResultScan result = FileScanner.scan(testFile.getAbsolutePath(), false);
        
        assertNotNull("应该返回结果", result);
        // 由于网络不可用，应该走本地扫描路径
        // 具体验证逻辑取决于本地扫描的实现
        
        System.out.println("网络不可用处理测试通过");
    }

    // 辅助方法：创建会超时的CloudScanner
    private CloudScanner createTimeoutCloudScanner() {
        // 这里需要创建一个模拟超时的CloudScanner
        // 实际实现中可能需要使用PowerMock或其他工具来模拟
        return mock(CloudScanner.class);
    }

    // 辅助方法：创建临时目录
    private File createTempDirectory() throws IOException {
        File tempDir = File.createTempFile("test", "dir");
        tempDir.delete();
        tempDir.mkdirs();
        tempDir.deleteOnExit();
        return tempDir;
    }

    // 辅助方法：创建大文件用于测试
    private File createLargeTestFile() throws IOException {
        File file = new File(tempDir, "large_test_file.txt");
        FileWriter writer = new FileWriter(file);
        
        // 写入超过1KB的内容以触发云扫描
        for (int i = 0; i < 100; i++) {
            writer.write("This is a test line for large file content. Line number: " + i + "\n");
        }
        writer.close();
        file.deleteOnExit();
        return file;
    }

    // 辅助方法：创建成功的初始化结果
    private Object createSuccessInitResult() {
        return new Object() {
            public boolean isSuccess = true;
        };
    }

    // 辅助方法：创建失败的初始化结果
    private Object createFailedInitResult() {
        return new Object() {
            public boolean isSuccess = false;
        };
    }

    // 辅助方法：创建可用的网络管理器
    private Object createMockNetworkManager() {
        return new Object() {
            public boolean isAvailable() {
                return true;
            }
        };
    }

    // 辅助方法：创建不可用的网络管理器
    private Object createUnavailableNetworkManager() {
        return new Object() {
            public boolean isAvailable() {
                return false;
            }
        };
    }

    // 辅助方法：Mock静态方法
    private void mockStatic(Class<?> clazz) {
        // 这里需要使用PowerMock或Mockito的静态Mock功能
        // 具体实现取决于测试框架的版本
    }
}
