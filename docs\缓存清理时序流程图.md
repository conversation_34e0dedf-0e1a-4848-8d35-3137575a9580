# 数据库维护时序图

本文档描述了数据库维护流程（`maintainDatabase`）的调用时序，重点突出了 `DatabaseHelper` 内部的详细处理步骤。

```mermaid
sequenceDiagram
    participant Client
    participant AVLEngine
    participant DataManager
    participant DatabaseHelper
    participant SQLiteDatabase

    Client->>AVLEngine: scanDir(path, listener)
    activate AVLEngine

    Note over AVLEngine: aysnc scan task...

    alt Scan Finishes Normally
        AVLEngine->>AVLEngine: listener.scanFinish()
        activate AVLEngine
        AVLEngine->>DataManager: getInstance().maintainDatabase()
        deactivate AVLEngine
    else Scan Encounters Exception
        AVLEngine->>DataManager: getInstance().maintainDatabase()
    end

    activate DataManager

    DataManager->>DatabaseHelper: maintainDatabase()
    activate DatabaseHelper

    DatabaseHelper->>SQLiteDatabase: getWritableDatabase()
    DatabaseHelper->>SQLiteDatabase: beginTransaction()
    activate SQLiteDatabase

    alt 清理过期缓存
        DatabaseHelper->>DataManager: getInstance().getHistoryCacheTimeout()
        DataManager-->>DatabaseHelper: timeout
        DatabaseHelper->>SQLiteDatabase: execSQL("DELETE FROM TABLE_CACHE WHERE ... > timeout")
        Note right of SQLiteDatabase: 删除时间戳超过阈值的记录
    end

    alt 清理多余缓存
        DatabaseHelper->>SQLiteDatabase: rawQuery("SELECT COUNT(*) FROM TABLE_CACHE")
        SQLiteDatabase-->>DatabaseHelper: cursor (contains count)

        DatabaseHelper->>DataManager: getInstance().getHistoryCacheSize()
        DataManager-->>DatabaseHelper: sizeLimit

        opt 如果 count > sizeLimit
            DatabaseHelper->>SQLiteDatabase: execSQL("DELETE ... ORDER BY timestamp LIMIT (count - sizeLimit)")
            Note right of SQLiteDatabase: 删除最旧的记录
        end
    end

    DatabaseHelper->>SQLiteDatabase: setTransactionSuccessful()
    DatabaseHelper->>SQLiteDatabase: endTransaction()
    deactivate SQLiteDatabase

    deactivate DatabaseHelper
    deactivate DataManager

```

# 数据库维护流程图 (包含调用上下文)

此流程图展示了从 `AVLEngine` 的 `scanDir` 方法发起，到 `DatabaseHelper` 中执行数据库维护的完整流程，并对关键步骤进行了解释。

```mermaid
graph TD
    subgraph AVLEngine [AVLEngine 调用上下文]
        A[客户端调用 scanDir] --> B[异步扫描任务开始...];
        B --> C{扫描结果如何?};
        C -- 正常结束 --> D["scanFinish() 回调触发<br/>- 清理扫描资源<br/>- 调用 maintainDatabase"];
        C -- 发生异常 --> E["catch 异常处理块<br/>- 记录错误日志<br/>- 调用 maintainDatabase"];
    end

    D --> F(maintainDatabase);
    E --> F;

    subgraph "DatabaseHelper 内部维护逻辑"
        F --> G[开始维护];
        G --> I["<b>清理步骤 1: 按时间</b><br/>删除所有超过缓存时效的记录"];
        I --> J["<b>清理步骤 2: 按数量</b><br/>获取当前缓存总数"];
        J --> K{缓存数量是否 > 数量上限?};
        K -- 是 --> L[删除最旧的记录, 直到数量达标];
        K -- 否 --> Z[维护结束];
        L --> Z;
    end

    style F fill:#f9f,stroke:#333,stroke-width:2px
```
