<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_gray">

    <!-- 顶部标题栏 -->
    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@android:color/transparent"
        android:fitsSystemWindows="true"
        app:elevation="0dp">

        <com.google.android.material.appbar.CollapsingToolbarLayout
            android:id="@+id/collapsingToolbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fitsSystemWindows="true"
            app:contentScrim="@color/white"
            app:layout_scrollFlags="scroll|exitUntilCollapsed"
            app:statusBarScrim="@android:color/transparent">

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                android:background="@color/white"
                app:layout_collapseMode="pin">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <ImageButton
                        android:id="@+id/btnBack"
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:contentDescription="返回"
                        android:src="@drawable/ic_back" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="扫描结果"
                        android:textColor="@color/black"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <View
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:visibility="invisible" />
                </LinearLayout>

            </androidx.appcompat.widget.Toolbar>

        </com.google.android.material.appbar.CollapsingToolbarLayout>
    </com.google.android.material.appbar.AppBarLayout>

    <!-- 可滚动的内容区域 -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- 扫描结果卡片 -->
            <androidx.cardview.widget.CardView
                android:id="@+id/cardScanResult"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <!-- 警告图标和标题 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <ImageView
                            android:layout_width="64dp"
                            android:layout_height="64dp"
                            android:background="@drawable/circle_background_light_red"
                            android:padding="16dp"
                            android:src="@drawable/ic_warning"
                            app:tint="@color/warning_yellow" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="16dp"
                            android:text="发现潜在威胁"
                            android:textColor="@color/black"
                            android:textSize="20sp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/tvScanSummary"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:text="扫描完成，发现 3 个需要处理的问题"
                            android:textColor="@color/gray"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <!-- 风险等级统计 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:orientation="horizontal">

                        <!-- 高风险 -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/tvHighRiskCount"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="1"
                                android:textColor="@color/danger_red"
                                android:textSize="24sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="高风险"
                                android:textColor="@color/gray"
                                android:textSize="14sp" />
                        </LinearLayout>

                        <!-- 中风险 -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/tvMediumRiskCount"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="1"
                                android:textColor="@color/warning_yellow"
                                android:textSize="24sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="中风险"
                                android:textColor="@color/gray"
                                android:textSize="14sp" />
                        </LinearLayout>

                        <!-- 低风险 -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/tvLowRiskCount"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="1"
                                android:textColor="@color/processing_blue"
                                android:textSize="24sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="低风险"
                                android:textColor="@color/gray"
                                android:textSize="14sp" />
                        </LinearLayout>
                    </LinearLayout>

                    <!-- 一键处理按钮 -->
                    <Button
                        android:id="@+id/btnFixAll"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="24dp"
                        android:background="@drawable/button_primary"
                        android:drawableStart="@drawable/ic_shield"
                        android:paddingStart="16dp"
                        android:paddingEnd="16dp"
                        android:text="一键处理全部"
                        android:textColor="@color/white" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- 详细信息标题 -->
            <TextView
                android:id="@+id/tvDetailsTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="24dp"
                android:text="详细信息"
                android:textColor="@color/black"
                android:textSize="18sp"
                android:textStyle="bold" />

            <!-- 详细信息列表 -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvThreatDetails"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:clipToPadding="false"
                android:nestedScrollingEnabled="false"
                android:paddingBottom="16dp" />

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout> 