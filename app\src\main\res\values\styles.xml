<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 快捷功能卡片样式 -->
    <style name="QuickFunctionCard">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginStart">8dp</item>
        <item name="android:layout_marginTop">16dp</item>
        <item name="android:layout_marginEnd">8dp</item>
        <item name="cardCornerRadius">12dp</item>
        <item name="cardElevation">2dp</item>
    </style>

    <!-- 快捷功能内部布局样式 -->
    <style name="QuickFunctionLayout">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:gravity">center</item>
        <item name="android:orientation">vertical</item>
        <item name="android:padding">16dp</item>
    </style>

    <!-- 快捷功能图标样式 -->
    <style name="QuickFunctionIcon">
        <item name="android:layout_width">32dp</item>
        <item name="android:layout_height">32dp</item>
        <item name="android:background">@drawable/circle_background_light_blue</item>
        <item name="android:padding">6dp</item>
        <item name="android:tint">#4285F4</item>
    </style>

    <!-- 快捷功能文本样式 -->
    <style name="QuickFunctionText">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginTop">8dp</item>
        <item name="android:textColor">#000000</item>
        <item name="android:textSize">12sp</item>
    </style>

    <!-- 自定义Toolbar样式 -->
    <style name="CustomToolbar" parent="Widget.MaterialComponents.Toolbar.Primary">
        <item name="android:layout_height">64dp</item>
        <item name="android:paddingTop">8dp</item>
        <item name="android:paddingBottom">8dp</item>
        <item name="titleTextAppearance">@style/ToolbarTitleText</item>
    </style>

    <!-- Toolbar标题文本样式 -->
    <style name="ToolbarTitleText" parent="TextAppearance.MaterialComponents.Headline6">
        <item name="android:textSize">20sp</item>
        <item name="android:textColor">@android:color/white</item>
        <item name="android:textStyle">bold</item>
    </style>
</resources>