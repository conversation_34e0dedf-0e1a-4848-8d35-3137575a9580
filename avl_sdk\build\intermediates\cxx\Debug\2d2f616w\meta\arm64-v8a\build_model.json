{"abi": "ARM64_V8A", "info": {"abi": "ARM64_V8A", "bitness": 64, "deprecated": false, "default": true}, "cxxBuildFolder": "D:\\Projects\\Android\\cccj-sdk\\avl_sdk\\.cxx\\Debug\\2d2f616w\\arm64-v8a", "soFolder": "D:\\Projects\\Android\\cccj-sdk\\avl_sdk\\build\\intermediates\\cxx\\Debug\\2d2f616w\\obj\\arm64-v8a", "soRepublishFolder": "D:\\Projects\\Android\\cccj-sdk\\avl_sdk\\build\\intermediates\\cmake\\baseDebug\\obj\\arm64-v8a", "abiPlatformVersion": 26, "cmake": {"effectiveConfiguration": {"inheritEnvironments": [], "variables": []}}, "variant": {"buildSystemArgumentList": [], "cFlagsList": ["-DAVLM_USE_AUTH"], "cppFlagsList": ["-DAVLM_USE_AUTH"], "variantName": "baseDebug", "soFolder": "D:\\Projects\\Android\\cccj-sdk\\avl_sdk\\build\\intermediates\\cxx\\Debug\\2d2f616w\\obj", "soRepublishFolder": "D:\\Projects\\Android\\cccj-sdk\\avl_sdk\\build\\intermediates\\cmake\\baseDebug\\obj", "cxxBuildFolder": "D:\\Projects\\Android\\cccj-sdk\\avl_sdk\\.cxx\\Debug\\2d2f616w", "intermediatesFolder": "D:\\Projects\\Android\\cccj-sdk\\avl_sdk\\build\\intermediates\\cxx\\Debug\\2d2f616w", "isDebuggableEnabled": true, "validAbiList": ["ARM64_V8A"], "buildTargetSet": [], "implicitBuildTargetSet": [], "cmakeSettingsConfiguration": "android-gradle-plugin-predetermined-name", "module": {"cxxFolder": "D:\\Projects\\Android\\cccj-sdk\\avl_sdk\\.cxx", "intermediatesBaseFolder": "D:\\Projects\\Android\\cccj-sdk\\avl_sdk\\build\\intermediates", "intermediatesFolder": "D:\\Projects\\Android\\cccj-sdk\\avl_sdk\\build\\intermediates\\cxx", "gradleModulePathName": ":avl_sdk", "moduleRootFolder": "D:\\Projects\\Android\\cccj-sdk\\avl_sdk", "moduleBuildFile": "D:\\Projects\\Android\\cccj-sdk\\avl_sdk\\build.gradle", "makeFile": "D:\\Projects\\Android\\cccj-sdk\\avl_sdk\\src\\main\\cpp\\CMakeLists.txt", "buildSystem": "CMAKE", "ndkFolder": "D:\\Android\\Sdk\\ndk\\21.4.7075529", "ndkVersion": "21.4.7075529", "ndkSupportedAbiList": ["ARMEABI_V7A", "ARM64_V8A", "X86", "X86_64"], "ndkDefaultAbiList": ["ARMEABI_V7A", "ARM64_V8A", "X86", "X86_64"], "ndkDefaultStl": "LIBCXX_STATIC", "ndkMetaPlatforms": {"min": 16, "max": 30, "aliases": {"20": 19, "25": 24, "J": 16, "J-MR1": 17, "J-MR2": 18, "K": 19, "L": 21, "L-MR1": 22, "M": 23, "N": 24, "N-MR1": 24, "O": 26, "O-MR1": 27, "P": 28, "Q": 29, "R": 30}}, "ndkMetaAbiList": [{"abi": "ARMEABI_V7A", "bitness": 32, "deprecated": false, "default": true}, {"abi": "ARM64_V8A", "bitness": 64, "deprecated": false, "default": true}, {"abi": "X86", "bitness": 32, "deprecated": false, "default": true}, {"abi": "X86_64", "bitness": 64, "deprecated": false, "default": true}], "cmakeToolchainFile": "D:\\Android\\Sdk\\ndk\\21.4.7075529\\build\\cmake\\android.toolchain.cmake", "cmake": {"isValidCmakeAvailable": true, "cmakeExe": "D:\\Android\\Sdk\\cmake\\3.22.1\\bin\\cmake.exe", "minimumCmakeVersion": "3.22.1", "ninjaExe": "D:\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe"}, "stlSharedObjectMap": {"LIBCXX_SHARED": {"ARMEABI_V7A": "D:\\Android\\Sdk\\ndk\\21.4.7075529\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\arm-linux-androideabi\\libc++_shared.so", "ARM64_V8A": "D:\\Android\\Sdk\\ndk\\21.4.7075529\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\aarch64-linux-android\\libc++_shared.so", "X86": "D:\\Android\\Sdk\\ndk\\21.4.7075529\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\i686-linux-android\\libc++_shared.so", "X86_64": "D:\\Android\\Sdk\\ndk\\21.4.7075529\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\x86_64-linux-android\\libc++_shared.so"}, "LIBCXX_STATIC": {}, "NONE": {}, "SYSTEM": {}}, "project": {"rootBuildGradleFolder": "D:\\Projects\\Android\\cccj-sdk", "sdkFolder": "D:\\Android\\Sdk", "isBuildOnlyTargetAbiEnabled": true, "ideBuildTargetAbi": "arm64-v8a,armeabi-v7a,armeabi", "isCmakeBuildCohabitationEnabled": false, "isPrefabEnabled": false}, "nativeBuildOutputLevel": "QUIET"}, "prefabClassPathFileCollection": [], "prefabPackageDirectoryListFileCollection": [], "stlType": "c++_static", "optimizationTag": "Debug"}, "buildSettings": {"environmentVariables": []}, "prefabFolder": "D:\\Projects\\Android\\cccj-sdk\\avl_sdk\\.cxx\\Debug\\2d2f616w\\prefab\\arm64-v8a", "isActiveAbi": true, "fullConfigurationHash": "2d2f616w1z3ge10652z122o3n53g5u71472h2t5h2w1o6b52j50366e5x326a", "configurationArguments": ["-HD:\\Projects\\Android\\cccj-sdk\\avl_sdk\\src\\main\\cpp", "-DCMAKE_SYSTEM_NAME=Android", "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON", "-DCMAKE_SYSTEM_VERSION=26", "-DANDROID_PLATFORM=android-26", "-DANDROID_ABI=arm64-v8a", "-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a", "-DANDROID_NDK=D:\\Android\\Sdk\\ndk\\21.4.7075529", "-DCMAKE_ANDROID_NDK=D:\\Android\\Sdk\\ndk\\21.4.7075529", "-DCMAKE_TOOLCHAIN_FILE=D:\\Android\\Sdk\\ndk\\21.4.7075529\\build\\cmake\\android.toolchain.cmake", "-DCMAKE_MAKE_PROGRAM=D:\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-DCMAKE_C_FLAGS=-DAVLM_USE_AUTH", "-DCMAKE_CXX_FLAGS=-DAVLM_USE_AUTH", "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\Projects\\Android\\cccj-sdk\\avl_sdk\\build\\intermediates\\cxx\\Debug\\2d2f616w\\obj\\arm64-v8a", "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\\Projects\\Android\\cccj-sdk\\avl_sdk\\build\\intermediates\\cxx\\Debug\\2d2f616w\\obj\\arm64-v8a", "-DCMAKE_BUILD_TYPE=Debug", "-BD:\\Projects\\Android\\cccj-sdk\\avl_sdk\\.cxx\\Debug\\2d2f616w\\arm64-v8a", "-<PERSON><PERSON><PERSON><PERSON>"]}