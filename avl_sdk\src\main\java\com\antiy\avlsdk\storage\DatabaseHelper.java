package com.antiy.avlsdk.storage;

import static com.antiy.avlsdk.storage.DBConstants.BLACK_WHITE_COLUMN_HASH;
import static com.antiy.avlsdk.storage.DBConstants.BLACK_WHITE_COLUMN_IS_BLACK;
import static com.antiy.avlsdk.storage.DBConstants.CACHE_HASH;
import static com.antiy.avlsdk.storage.DBConstants.CACHE_TIMESTAMP;
import static com.antiy.avlsdk.storage.DBConstants.CACHE_VIRUS_NAME;
import static com.antiy.avlsdk.storage.DBConstants.DATABASE_NAME;
import static com.antiy.avlsdk.storage.DBConstants.DATABASE_VERSION;
import static com.antiy.avlsdk.storage.DBConstants.TABLE_BLACK_WHITE;
import static com.antiy.avlsdk.storage.DBConstants.TABLE_CACHE;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;

import com.antiy.avlsdk.AVLEngine;
import com.antiy.avlsdk.entity.BlackWhiteEntity;
import com.antiy.avlsdk.entity.CacheEntity;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * 2 * Copyright (C), 2020-2024
 * 3 * FileName: DatabaseHelper
 * 4 * Author: wangbiao
 * 5 * Date: 2024/9/3 16:28
 * 6 * Description:处理数据库连接和操作的辅助类
 * 10
 */
public class DatabaseHelper extends SQLiteOpenHelper {

    public DatabaseHelper(Context context) {
        super(context, DATABASE_NAME, null, DATABASE_VERSION);
    }

    @Override
    public void onCreate(SQLiteDatabase db) {
        // 创建cache表
        String createSettingsTable = "CREATE TABLE IF NOT EXISTS " + TABLE_CACHE + "(" +
                CACHE_HASH + " TEXT PRIMARY KEY," +
                CACHE_VIRUS_NAME + " TEXT," +
                CACHE_TIMESTAMP + " long)";
        db.execSQL(createSettingsTable);

        // 创建BLACK_WHITE_LIST表
        String createLogsTable = "CREATE TABLE IF NOT EXISTS " + TABLE_BLACK_WHITE + " (" +
                BLACK_WHITE_COLUMN_HASH + " TEXT PRIMARY KEY, " +
                BLACK_WHITE_COLUMN_IS_BLACK + " TEXT) ";
        db.execSQL(createLogsTable);
    }

    @Override
    public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        // 删除老表 重新创建新表
        db.delete(TABLE_BLACK_WHITE, null, null);
        db.delete(TABLE_CACHE, null, null);
        onCreate(db);
    }

    public synchronized void insertBlackWhiteData(String hash, String data) {
        SQLiteDatabase db = this.getWritableDatabase();
        ContentValues values = new ContentValues();
        values.put(BLACK_WHITE_COLUMN_HASH, hash);
        values.put(BLACK_WHITE_COLUMN_IS_BLACK, data);
        db.insertWithOnConflict(TABLE_BLACK_WHITE, null, values, SQLiteDatabase.CONFLICT_REPLACE);
        db.close();
    }

    public synchronized HashMap<String, Boolean> getBlackWhiteListData() {
        SQLiteDatabase db = this.getReadableDatabase();
        Cursor cursor = db.query(DBConstants.TABLE_BLACK_WHITE, null, null, null, null, null, null);
        HashMap<String, Boolean> hashMap = new HashMap<>();
        if (cursor.moveToFirst()) {
            do {
                String hash = cursor.getString(cursor.getColumnIndexOrThrow(BLACK_WHITE_COLUMN_HASH));
                String isBlack = cursor.getString(cursor.getColumnIndexOrThrow(BLACK_WHITE_COLUMN_IS_BLACK));
                hashMap.put(hash, Boolean.valueOf(isBlack));
            } while (cursor.moveToNext());
        }

        cursor.close();
        db.close();
        return hashMap;
    }

    /**
     * 查询指定hash值是否在黑白名单中
     *
     * @param hash 要查询的hash值
     * @return 如果找到匹配记录返回BlackWhiteEntity对象，否则返回null
     */
    public synchronized BlackWhiteEntity matchBlackWhiteData(String hash) {
        AVLEngine.Logger.info("start query black white list,hash: " + hash);
        SQLiteDatabase db = null;
        Cursor cursor = null;
        try {
            db = this.getReadableDatabase();
            // 查询指定hash值的记录
            cursor = db.query(
                    TABLE_BLACK_WHITE,          // 表名
                    new String[]{               // 需要查询的列
                            BLACK_WHITE_COLUMN_HASH,
                            BLACK_WHITE_COLUMN_IS_BLACK
                    },
                    BLACK_WHITE_COLUMN_HASH + "=?",  // WHERE子句
                    new String[]{hash},              // WHERE子句的参数
                    null, null, null
            );

            // 如果找到匹配记录
            if (cursor != null && cursor.moveToFirst()) {
                String isBlack = cursor.getString(cursor.getColumnIndexOrThrow(BLACK_WHITE_COLUMN_IS_BLACK));
                AVLEngine.Logger.info("Find matching records: hash=" + hash + ", isBlack=" + isBlack);
                return new BlackWhiteEntity(hash, Boolean.parseBoolean(isBlack));
            }
            return null;
        } catch (Exception e) {
            AVLEngine.Logger.error("Exception occurred while querying the blacklist and whitelist:" + e.getMessage());
            e.printStackTrace();
            return null;
        } finally {
            // 确保资源被正确释放
            if (cursor != null && !cursor.isClosed()) {
                cursor.close();
            }
            if (db != null) {
                db.close();
            }
        }
    }

    public synchronized void insertCacheData(String hash, String virusName, long time) {
        SQLiteDatabase db = this.getWritableDatabase();
        ContentValues values = new ContentValues();
        values.put(CACHE_HASH, hash);
        values.put(CACHE_VIRUS_NAME, virusName);
        values.put(CACHE_TIMESTAMP, time);
        db.insertWithOnConflict(TABLE_CACHE, null, values, SQLiteDatabase.CONFLICT_REPLACE);
        db.close();
    }

    public synchronized CacheEntity getCacheData(String key) {
        AVLEngine.Logger.info("Start querying cached data，key:" + key);
        SQLiteDatabase db = null;
        Cursor cursor = null;
        try {
            // 获取可读数据库对象
            db = this.getReadableDatabase();
            // 查询 TABLE_CACHE 表中符合条件的记录
            cursor = db.query(
                    TABLE_CACHE,
                    new String[]{CACHE_HASH, CACHE_VIRUS_NAME, CACHE_TIMESTAMP}, // 仅查询所需列
                    CACHE_HASH + " = ?", // 查询条件
                    new String[]{key}, // 条件参数
                    null, null, null);

            boolean hasResult = cursor.moveToFirst();

            // 如果有查询结果
            if (hasResult) {
                // 获取列值
                String hash = cursor.getString(cursor.getColumnIndexOrThrow(CACHE_HASH));
                String virusName = cursor.getString(cursor.getColumnIndexOrThrow(CACHE_VIRUS_NAME));
                long timestamp = cursor.getLong(cursor.getColumnIndexOrThrow(CACHE_TIMESTAMP));
                // 封装并返回结果
                return new CacheEntity(hash, virusName, timestamp);
            } else {
                AVLEngine.Logger.info("No matching cache data found");
            }
        } catch (Exception e) {
            // 打印异常日志
            AVLEngine.Logger.error("Exception occurred while querying cached data:" + e.getMessage());
            e.printStackTrace();
        } finally {
            // 确保游标和数据库被关闭
            if (cursor != null) {
                cursor.close();
            }
            if (db != null) {
                db.close();
            }
        }

        // 如果查询没有结果，返回 null
        return null;
    }

    public synchronized List<CacheEntity> getAllCacheData() {
        SQLiteDatabase db = this.getReadableDatabase();
        Cursor cursor = db.query(TABLE_CACHE, null, null, null, null, null, null);
        List<CacheEntity> caches = new ArrayList<>();
        if (cursor.moveToFirst()) {
            do {
                String hash = cursor.getString(cursor.getColumnIndexOrThrow(CACHE_HASH));
                String virusName = cursor.getString(cursor.getColumnIndexOrThrow(CACHE_VIRUS_NAME));
                long timestamp = cursor.getLong(cursor.getColumnIndexOrThrow(CACHE_TIMESTAMP));
                caches.add(new CacheEntity(hash, virusName, timestamp));
            } while (cursor.moveToNext());
        }
        if (cursor != null) cursor.close();
        db.close();
        return caches;
    }

    public synchronized void deleteData(String tableName, String columnName, String key) {
        SQLiteDatabase db = this.getWritableDatabase();
        db.delete(tableName, columnName + "=?", new String[]{key});
        db.close();
    }

    public synchronized void clearAllData(String tableName) {
        SQLiteDatabase db = this.getWritableDatabase();
        db.delete(tableName, null, null);
        db.close();
    }

    /**
     * 清理缓存的超过时间的数据
     */
    public synchronized void maintainDatabase() {
        SQLiteDatabase db = this.getWritableDatabase();
        db.beginTransaction();
        try {
            // 清理过期数据
            long currentTime = System.currentTimeMillis();
            db.execSQL("DELETE FROM " + TABLE_CACHE + " WHERE (" + currentTime + " - " + CACHE_TIMESTAMP + ") > " + DataManager.getInstance().getHistoryCacheTimeout());
            AVLEngine.Logger.info("Deleting data exceeding the cache duration threshold");

            // 检查是否超过长度阈值，如果是则删除最旧的记录
            Cursor cursor = db.rawQuery("SELECT COUNT(*) FROM " + TABLE_CACHE, null);
            cursor.moveToFirst();
            int count = cursor.getInt(0);
            cursor.close();

            if (count > DataManager.getInstance().getHistoryCacheSize()) {
                long deleteCount = count - DataManager.getInstance().getHistoryCacheSize();
                String deleteSql = "DELETE FROM " + TABLE_CACHE +
                        " WHERE " + CACHE_HASH + " IN (" +
                        "SELECT " + CACHE_HASH +
                        " FROM " + TABLE_CACHE +
                        " ORDER BY " + CACHE_TIMESTAMP + " ASC" +
                        " LIMIT " + deleteCount +
                        ")";
                db.execSQL(deleteSql);
                AVLEngine.Logger.info("Deleting data exceeding the cache size threshold");
            }
            db.setTransactionSuccessful();
        } finally {
            db.endTransaction();
        }
    }
}
