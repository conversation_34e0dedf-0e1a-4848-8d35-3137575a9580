package com.antiy.avlsdk.utils;

import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.pm.Signature;
import android.util.Base64;

import com.antiy.avlsdk.AVLEngine;
import com.jaredrummler.apkparser.ApkParser;

import java.io.File;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * Author: wangbiao
 * Date: 2024/9/14 09:25
 * Description:
 */
public class AppUtil {
    /**
     * 从SD卡上的APK文件路径获取该APK的包名。
     *
     * @param apkFilePath SD卡上的APK文件路径
     * @return APK的包名
     */
    public static String getPackageNameFromApk(String apkFilePath) {
        try (ApkParser apkParser = ApkParser.create(new File(apkFilePath))) {
            return apkParser.getApkMeta().packageName;
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    /**
     * 从SD卡上的APK文件路径获取该APK的签名信息。
     *
     * @param apkFilePath SD卡上的APK文件路径
     * @return APK的签名信息的SHA-1指纹
     */
    public static String getSignatureFromApk(Context context , String apkFilePath) {
        PackageManager pm = context.getPackageManager();
        PackageInfo packageInfo = null;
        try {
            // 获取APK文件的PackageInfo，并获取签名信息
            packageInfo = pm.getPackageArchiveInfo(apkFilePath, PackageManager.GET_SIGNATURES);
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (packageInfo != null && packageInfo.signatures != null && packageInfo.signatures.length > 0) {
            Signature[] signatures = packageInfo.signatures;
            // 这里仅取第一个签名进行处理
            Signature signature = signatures[0];

            // 使用SHA-1算法生成签名的摘要
            return getSha1Fingerprint(signature.toByteArray());
        } else {
            return null;
        }
    }

    /**
     * 使用SHA-1算法生成签名的摘要。
     *
     * @param signatureBytes 签名的字节数组
     * @return SHA-1指纹
     */
    private static String getSha1Fingerprint(byte[] signatureBytes) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA1");
            byte[] digest = md.digest(signatureBytes);
            return Base64.encodeToString(digest, Base64.DEFAULT);
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
            AVLEngine.Logger.error("SHA-1 algorithm not found");
        }
        return "";
    }
    /**
     * 获取应用包名
     */
    public static String getPackageName(Context context) {
        try {
            return context.getPackageName();
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }
}
