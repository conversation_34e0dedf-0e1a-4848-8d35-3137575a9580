# AVL引擎扫描缓存处理方案

## 📋 方案概述
AVL杀毒引擎采用基于SHA256哈希值的智能缓存机制，通过缓存扫描结果显著提升扫描性能，避免重复扫描相同文件。

---

## 🔑 缓存Key设计

### 核心策略：SHA256哈希值作为缓存键
```
缓存Key = EncryptorHelper.calcPathSHA256(filePath)
```

### 优势特点
- ✅ **内容唯一性**：相同内容文件具有相同缓存key
- ✅ **路径无关性**：文件移动不影响缓存命中
- ✅ **安全性高**：SHA256算法确保key唯一性
- ✅ **版本敏感**：文件修改后自动失效

---

## 🔄 缓存处理流程

### 扫描前检查
1. **计算文件SHA256** → `EncryptorHelper.calcPathSHA256()`
2. **查询缓存** → `CacheManager.hasCache(sha256)`
3. **命中缓存** → 直接返回结果，跳过扫描
4. **未命中** → 执行实际扫描

### 扫描后存储
```java
CacheManager.storeScanResult(path, virusName, sha256)
↓
DataManager.saveCacheData(sha256, virusName, timestamp)
↓
DatabaseHelper.insertCacheData() // 存储到SQLite
```

---

## 🧹 智能缓存清理

### 双重清理策略
| 清理类型 | 触发条件 | 清理规则 |
|---------|---------|---------|
| **时间清理** | 每次扫描完成 | 删除超过**7天**的缓存记录 |
| **数量清理** | 缓存数量>1000 | 删除最旧的记录至**1000条** |

### 清理时机
- ✅ 正常扫描完成后：`scanFinish()` → `maintainDatabase()`
- ✅ 异常处理后：`catch块` → `maintainDatabase()`
- ✅ 确保资源不会无限增长

---

## 🚀 性能优化亮点

### 1. 预计算哈希优化
- **CloudScanner**：批量扫描时复用预计算的SHA256值
- **避免重复计算**：单文件多次引用时性能提升显著

### 2. 扫描模式适配
- **本地扫描**：每文件检查缓存，快速跳过已扫描文件
- **云端扫描**：批量处理，减少网络请求次数

### 3. 数据库优化
- **SQLite存储**：高效的本地数据库
- **索引优化**：基于SHA256的快速查询
- **事务处理**：批量操作确保数据一致性

---

## 📊 方案效果

### 性能提升
- 🔥 **缓存命中率**：重复文件扫描时间接近0
- 🔥 **扫描速度**：大幅减少重复计算和网络请求
- 🔥 **资源占用**：智能清理避免存储空间浪费

### 用户体验
- ⚡ **响应更快**：常用文件秒级返回结果
- ⚡ **流量节省**：减少云端查询次数
- ⚡ **稳定可靠**：异常情况下自动维护缓存

---

## 🎯 技术架构

```
客户端调用
    ↓
AVLEngine.scanDir()
    ↓
ScannerFactory → CloudScanner/LocalScanner
    ↓
FileScanner.scan()
    ↓
缓存检查 → CacheManager → DataManager → DatabaseHelper
    ↓
扫描执行 → 结果缓存 → 缓存维护
```

**核心组件协作，确保缓存机制高效运行**
