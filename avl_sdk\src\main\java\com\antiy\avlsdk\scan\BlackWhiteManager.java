package com.antiy.avlsdk.scan;

import com.antiy.avlsdk.AVLEngine;
import com.antiy.avlsdk.entity.BlackWhiteEntity;
import com.antiy.avlsdk.storage.EncryptorHelper;
import com.antiy.avlsdk.storage.DataManager;

/**
 * Author: wangbiao
 * Date: 2024/9/19 14:57
 * Description:黑白名单管理
 */
public class BlackWhiteManager {
    private static volatile BlackWhiteManager instance;

    private BlackWhiteManager() {
    }

    public static BlackWhiteManager getInstance() {
        if (instance == null) {
            synchronized (BlackWhiteManager.class) {
                if (instance == null) {
                    instance = new BlackWhiteManager();
                }
            }
        }
        return instance;
    }


    public BlackWhiteEntity match(String hash){
        AVLEngine.Logger.info("Matching path in BlackWhiteManager:" + hash);
        BlackWhiteEntity result = DataManager.getInstance().getBlackWhiteData(hash);
        AVLEngine.Logger.info("Match result in BlackWhiteManager:" + result);
        return result;
    }
}
