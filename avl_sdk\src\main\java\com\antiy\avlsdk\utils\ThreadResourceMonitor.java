package com.antiy.avlsdk.utils;

import android.annotation.SuppressLint;
import android.os.Process;
import android.os.SystemClock;
import android.system.Os;
import android.system.OsConstants;

import com.antiy.avlsdk.AVLEngine;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Arrays;

public class ThreadResourceMonitor {
    private long startCpuTime;
    private long startRealTime;
    private long peakMemoryUsage;
    private final int threadId;
    private final int pid;
    private final long clockTicksPerSecond;
    private final String logFilePath;
    private PrintWriter logWriter;

    public ThreadResourceMonitor(String logFilePath) {
        this.threadId = Process.myTid();
        this.pid = Process.myPid();
        this.clockTicksPerSecond = Os.sysconf(OsConstants._SC_CLK_TCK);
        this.startCpuTime = getThreadCpuTime();
        this.startRealTime = SystemClock.elapsedRealtimeNanos();
        this.peakMemoryUsage = 0;
        this.logFilePath = logFilePath;
        initLogWriter();
    }

    private void initLogWriter() {
        try {
            this.logWriter = new PrintWriter(new BufferedWriter(new FileWriter(logFilePath, true)));
        } catch (IOException e) {
            AVLEngine.Logger.error("Error initializing log writer: " + e.getMessage());
        }
    }

    public void updateMetrics() {
        MemoryInfo memInfo = getProcessMemoryInfo();
        peakMemoryUsage = Math.max(peakMemoryUsage, memInfo.rss);
        logToFile(getReport());
    }

    @SuppressLint("DefaultLocale")
    public String getReport() {
        long endCpuTime = getThreadCpuTime();
        long endRealTime = SystemClock.elapsedRealtimeNanos();

        long cpuTimeNanos = (endCpuTime - startCpuTime) * (1_000_000_000L / clockTicksPerSecond);
        long realTimeNanos = endRealTime - startRealTime;

        double cpuUsage = (double) cpuTimeNanos / realTimeNanos * 100.0;
        MemoryInfo memInfo = getProcessMemoryInfo();
        // RSS 常驻内存大小
        return String.format("Thread CPU Usage: %.2f%%\n" +
//                        "Peak RSS: %.2f MB\n" +
                        "Current RSS(常驻内存): %.2f MB\n",
                cpuUsage,
//                peakMemoryUsage / (1024.0 * 1024.0),
                memInfo.rss / (1024.0 * 1024.0));
    }

    private long getThreadCpuTime() {
        try {
            BufferedReader reader = new BufferedReader(new FileReader("/proc/" + pid + "/task/" + threadId + "/stat"));
            String[] stats = reader.readLine().split("\\s+");
            logToFile("Thread stats: " + Arrays.toString(stats));
            reader.close();
            // 代表用户态和内核态的 CPU 时间
            long utime = Long.parseLong(stats[13]);
            long stime = Long.parseLong(stats[14]);
            return utime + stime;
        } catch (IOException e) {
            logToFile("Error getting CPU time: " + e.getMessage());
            return 0;
        }
    }

    private MemoryInfo getProcessMemoryInfo() {
        MemoryInfo memInfo = new MemoryInfo();
        try {
            BufferedReader reader = new BufferedReader(new FileReader("/proc/" + pid + "/status"));
            String line;
            while ((line = reader.readLine()) != null) {
                if (line.startsWith("VmRSS:")) {
                    memInfo.rss = Long.parseLong(line.split("\\s+")[1]) * 1024; // Convert KB to bytes
                } else if (line.startsWith("VmSize:")) {
                    memInfo.vsize = Long.parseLong(line.split("\\s+")[1]) * 1024; // Convert KB to bytes
                }
                if (memInfo.rss > 0 && memInfo.vsize > 0) {
                    break;
                }
            }
            reader.close();
        } catch (IOException e) {
            logToFile("Error getting memory usage: " + e.getMessage());
        }
        return memInfo;
    }

    private void logToFile(String message) {
        if (logWriter != null) {
            logWriter.println(message);
            logWriter.flush();
        }
    }

    public void close() {
        if (logWriter != null) {
            logWriter.close();
        }
    }

    private static class MemoryInfo {
        long rss;    // Resident Set Size
        long vsize;  // Virtual Memory Size
    }
}