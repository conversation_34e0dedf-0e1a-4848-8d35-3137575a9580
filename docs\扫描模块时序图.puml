@startuml
actor User

User -> FolderScanner: scanFolder(folder)
FolderScanner -> FolderScanner:判断文件个数

alt 文件个数 > 阈值
    FolderScanner -> CloudScanner: scan(fileList)
    CloudScanner --> FolderScanner: 云扫描结果
    FolderScanner -> User: 返回结果
else
    loop 每个文件
        FolderScanner -> FileScanner: scanFile(file)
        FileScanner -> FileChecker: isValidType(file)
        alt 文件类型合法
            FileChecker --> FileScanner: 合法
            FileScanner -> CacheManager: hasCache(file)
            alt 有缓存
                CacheManager -> FileScanner: getCacheResult(file)
                FileScanner -> FolderScanner: 返回缓存结果
            else
                FileScanner -> BalckWhiteListMatcher : match(file_hash)
                alt file_hash in 黑白名单
                   FolderScanner <-- BalckWhiteListMatcher : 返回黑白名单匹配结果
                else
                    FileScanner -> FileChecker: isFileSizeAboveThreshold(file)
                    alt 文件大小 > 阈值
                        FileScanner -> CloudScanner: scan(file)
                        CloudScanner --> FileScanner: 返回云扫描结果
                    else
                        alt 文件是APK/JAR/DEX类型
                            FileScanner -> AbsEngine: MobileEngine.scan(file)
                        else
                            FileScanner -> AbsEngine: PcEngine.scan(file)
                        end if
                        AbsEngine --> FileScanner: 返回引擎扫描结果
                    end if
                    FileScanner -> CacheManager: storeScanResult(file, result)
                end
            end if
            FileScanner -> FolderScanner: 返回扫描结果
        else
            FileChecker --> FileScanner: 不合法
            FileScanner -> FolderScanner: 停止扫描该文件

        end if
    end
    FolderScanner -> User: 返回扫描完成结果
end if
@enduml
