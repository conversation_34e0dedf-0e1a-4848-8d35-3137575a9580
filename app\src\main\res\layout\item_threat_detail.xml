<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="16dp"
    android:layout_marginVertical="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="1dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 威胁标题和风险等级 -->
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/ivThreatIcon"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_centerVertical="true"
                android:background="@drawable/circle_background_light_red"
                android:padding="8dp"
                android:src="@drawable/ic_virus_db" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="16dp"
                android:layout_toStartOf="@id/tvRiskLevel"
                android:layout_toEndOf="@id/ivThreatIcon"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvThreatName"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="恶意软件"
                    android:textColor="@color/black"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvThreatLocation"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="位置：/download/app_installer.apk"
                    android:textColor="@color/gray"
                    android:textSize="12sp" />
            </LinearLayout>

            <TextView
                android:id="@+id/tvRiskLevel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:background="@drawable/rounded_button_background"
                android:backgroundTint="@color/danger_red"
                android:paddingHorizontal="12dp"
                android:paddingVertical="4dp"
                android:text="高风险"
                android:textColor="@color/white"
                android:textSize="12sp" />
        </RelativeLayout>

        <!-- 威胁描述 -->
        <TextView
            android:id="@+id/tvThreatDescription"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:text="此文件包含已知的恶意代码，可能会窃取您的个人信息"
            android:textColor="@color/black"
            android:textSize="14sp" />

        <!-- 操作按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:orientation="horizontal">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnDelete"
                style="@style/Widget.MaterialComponents.Button"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:layout_marginEnd="8dp"
                android:minWidth="100dp"
                android:paddingHorizontal="16dp"
                android:text="删除"
                android:textColor="@color/white"
                app:cornerRadius="20dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnIgnore"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:layout_marginStart="8dp"
                android:minWidth="100dp"
                android:paddingHorizontal="16dp"
                android:text="忽略"
                android:textColor="@color/gray"
                app:cornerRadius="20dp"
                app:strokeColor="@color/gray"
                app:strokeWidth="1dp" />
        </LinearLayout>
    </LinearLayout>
</androidx.cardview.widget.CardView> 