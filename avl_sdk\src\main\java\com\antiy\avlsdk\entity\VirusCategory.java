package com.antiy.avlsdk.entity;

/**
 * 病毒类别枚举
 * 用于标识不同类型的病毒，每个枚举值包含对应的字符串标识
 */
public enum VirusCategory {
    TROJAN("Trojan"),      // 木马程序
    G_WARE("G-Ware"),      // 流氓软件
    TOOL("Tool"),          // 黑客工具
    RISK_WARE("RiskWare"), // 风险应用
    PORN_WARE("PornWare"), // 色情软件
    AD_WARE("AdWare"),     // 广告软件
    PAY_WARE("PayWare"),   // 付费插件
    WORM("Worm"),          // 蠕虫病毒
    WARN("Warn"),          // 警告
    UN_KNOW("Un-Know");

    private final String value;

    VirusCategory(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static VirusCategory fromString(String text) {
        if (text != null) {
            for (VirusCategory category : VirusCategory.values()) {
                if (category.value.equalsIgnoreCase(text)) {
                    return category;
                }
            }
        }
        return UN_KNOW;
    }
}
