package com.antiy.avlsdk.storage;

import android.content.Context;
import android.os.Build;
import android.os.Environment;
import android.util.Base64;

import com.antiy.avlsdk.AVLEngine;

import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.nio.charset.StandardCharsets;
import java.security.KeyStore;
import java.security.SecureRandom;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;

/**
 * 安全存储管理类
 * 用于在设备上安全地存储敏感数据
 */
public class SecureStorage {
    private static final String KEYSTORE_PROVIDER = "AndroidKeyStore";
    private static final String AES_MODE = "AES/GCM/NoPadding";
    private static final String KEY_ALIAS = "com.antiy.avlsdk.storage.SecureStorage";
    private static final int GCM_TAG_LENGTH = 128;
    private static final String KEY_BACKUP_FILE = ".key_backup";

    // 存储位置优先级：外部存储 > 设备保护存储 > 应用私有目录
    private static final String STORAGE_FOLDER = ".antiy";

    private static SecureStorage instance;
    private final Context context;
    private final File storageDir;
    private SecretKey secretKey;

    private SecureStorage(Context context) {
        this.context = context.getApplicationContext();
        this.storageDir = initializeStorageDirectory();
        initializeKeystore();
    }

    public static SecureStorage getInstance(Context context) {
        if (instance == null) {
            synchronized (SecureStorage.class) {
                if (instance == null) {
                    instance = new SecureStorage(context);
                }
            }
        }
        return instance;
    }

    /**
     * 初始化存储目录
     * 该方法尝试按优先级顺序初始化和返回一个可用的存储目录
     * 首先尝试使用外部存储，如果不可用，则尝试使用设备保护存储（Android N+），最后 fallback 到应用私有目录
     *
     * @return 初始化的存储目录文件对象，如果都无法使用则返回null
     */
    private File initializeStorageDirectory() {
        File selectedDir = null;

        // 1. 首选：尝试使用外部存储
        if (Environment.getExternalStorageState().equals(Environment.MEDIA_MOUNTED)) {
            File dir = new File(Environment.getExternalStorageDirectory(), STORAGE_FOLDER);
            if (ensureDirectoryExists(dir)) {
                AVLEngine.Logger.info("Using external storage: " + dir.getAbsolutePath());
                selectedDir = dir;
            }
        }

        // 2. 备选：尝试使用设备保护存储（Android N+）
        if (selectedDir == null && Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            File dir = new File(context.createDeviceProtectedStorageContext().getDataDir(), STORAGE_FOLDER);
            if (ensureDirectoryExists(dir)) {
                AVLEngine.Logger.info("Using device protected storage: " + dir.getAbsolutePath());
                selectedDir = dir;
            }
        }

        // 3. 最后：使用应用私有目录
        if (selectedDir == null) {
            File dir = new File(context.getFilesDir(), STORAGE_FOLDER);
            if (ensureDirectoryExists(dir)) {
                AVLEngine.Logger.info("Using app private storage: " + dir.getAbsolutePath());
                selectedDir = dir;
            }
        }

        return selectedDir;
    }

    private boolean ensureDirectoryExists(File dir) {
        try {
            if (!dir.exists()) {
                boolean created = dir.mkdirs();
                if (!created) {
                    AVLEngine.Logger.error("Failed to create directory: " + dir.getAbsolutePath());
                    return false;
                }
            }
            // 测试目录权限
            File testFile = new File(dir, ".test");
            boolean canWrite = testFile.createNewFile();
            boolean canRead = testFile.exists();
            if (testFile.exists()) {
                testFile.delete();
            }
            if (!canWrite || !canRead) {
                AVLEngine.Logger.error("Directory not accessible: " + dir.getAbsolutePath());
                return false;
            }
            return true;
        } catch (Exception e) {
            AVLEngine.Logger.error("Error checking directory: " + e.getMessage());
            return false;
        }
    }

    /**
     * 初始化密钥库
     * 此方法首先尝试从备份中恢复密钥如果恢复成功，则使用恢复的密钥
     * 如果恢复失败，则尝试从密钥库中获取现有密钥或生成新密钥，并备份
     * 如果在密钥库操作过程中发生错误，则生成新的随机密钥并备份
     */
    private void initializeKeystore() {
        // 首先尝试从备份文件恢复密钥
        secretKey = loadKeyFromBackup();
        if (secretKey != null) {
            AVLEngine.Logger.info("Key restored from backup");
            return;
        }

        try {
            KeyStore keyStore = KeyStore.getInstance(KEYSTORE_PROVIDER);
            keyStore.load(null);

            // 检查是否已存在密钥
            if (!keyStore.containsAlias(KEY_ALIAS)) {
                // 生成新密钥
                secretKey = generateNewKey();
                // 备份新生成的密钥
                backupKey(secretKey);
            } else {
                // 获取已存在的密钥
                secretKey = (SecretKey) keyStore.getKey(KEY_ALIAS, null);
                // 备份获取到的密钥
                backupKey(secretKey);
            }
        } catch (Exception e) {
            AVLEngine.Logger.error("Failed to initialize keystore: " + e.getMessage());
            // 如果Keystore出错，生成随机密钥
            secretKey = generateNewKey();
            // 备份新生成的密钥
            backupKey(secretKey);
        }
    }

    private SecretKey generateNewKey() {
        try {
            byte[] keyBytes = new byte[32];
            new SecureRandom().nextBytes(keyBytes);
            return new SecretKeySpec(keyBytes, "AES");
        } catch (Exception e) {
            AVLEngine.Logger.error("Failed to generate new key: " + e.getMessage());
            return null;
        }
    }

    private void backupKey(SecretKey key) {
        if (storageDir == null || key == null) return;
        
        try {
            File backupFile = new File(storageDir, KEY_BACKUP_FILE);
            FileWriter writer = new FileWriter(backupFile);
            String keyData = Base64.encodeToString(key.getEncoded(), Base64.NO_WRAP);
            writer.write(keyData);
            writer.flush();
            writer.close();
            AVLEngine.Logger.info("Key backup created");
        } catch (Exception e) {
            AVLEngine.Logger.error("Failed to backup key: " + e.getMessage());
        }
    }

    /**
     * 从备份中加载密钥
     * 此方法尝试从指定的存储目录中读取密钥备份文件如果文件存在，则读取并解析该文件，
     * 以恢复之前存储的密钥如果文件不存在或读取过程中发生任何错误，则返回null
     *
     * @return SecretKey对象，如果成功从备份文件中加载密钥；否则返回null
     */
    private SecretKey loadKeyFromBackup() {
        AVLEngine.Logger.error("start load key from backup");
        // 检查存储目录是否已设置，如果未设置则无法进行后续操作
        if (storageDir == null) return null;

        try {
            // 构建密钥备份文件的路径
            File backupFile = new File(storageDir, KEY_BACKUP_FILE);
            // 检查密钥备份文件是否存在，如果不存在则返回null
            if (!backupFile.exists()) {
                return null;
            }

            // 读取密钥备份文件的内容
            FileReader reader = new FileReader(backupFile);
            StringBuilder content = new StringBuilder();
            char[] buffer = new char[1024];
            int read;
            while ((read = reader.read(buffer)) != -1) {
                content.append(buffer, 0, read);
            }
            reader.close();
            AVLEngine.Logger.error("load backup key:" + content);
            // 将读取到的内容解码为密钥字节数组
            byte[] keyBytes = Base64.decode(content.toString(), Base64.NO_WRAP);
            // 使用密钥字节数组生成并返回SecretKey对象
            return new SecretKeySpec(keyBytes, "AES");
        } catch (Exception e) {
            // 捕获并记录密钥加载过程中的任何异常
            AVLEngine.Logger.error("Failed to load key backup: " + e.getMessage());
            return null;
        }
    }

    /**
     * 加密并保存数据
     */
    public void saveData(String fileName, String data) {
        if (storageDir == null || secretKey == null) {
            AVLEngine.Logger.error("Storage not initialized");
            return;
        }

        try {
            // 生成随机IV
            byte[] iv = new byte[12];  // GCM模式使用12字节IV
            new SecureRandom().nextBytes(iv);

            // 初始化加密器
            final Cipher cipher = Cipher.getInstance(AES_MODE);
            GCMParameterSpec parameterSpec = new GCMParameterSpec(GCM_TAG_LENGTH, iv);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey, parameterSpec);

            // 加密数据
            byte[] encryptedData = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));

            // 组合IV和加密数据
            byte[] combined = new byte[12 + encryptedData.length];
            System.arraycopy(iv, 0, combined, 0, 12);
            System.arraycopy(encryptedData, 0, combined, 12, encryptedData.length);

            // Base64编码并保存
            String encoded = Base64.encodeToString(combined, Base64.NO_WRAP);
            File file = new File(storageDir, "." + fileName);
            FileWriter writer = new FileWriter(file);
            writer.write(encoded);
            writer.flush();
            writer.close();

            AVLEngine.Logger.info("Data saved to: " + file.getAbsolutePath());

        } catch (Exception e) {
            AVLEngine.Logger.error("Failed to save encrypted data: " + e.getMessage());
        }
    }

    /**
     * 读取并解密数据
     */
    public String readData(String fileName) {
        if (storageDir == null || secretKey == null) {
            AVLEngine.Logger.error("Storage not initialized");
            return null;
        }

        try {
            File file = new File(storageDir, "." + fileName);
            if (!file.exists()) {
                AVLEngine.Logger.info("File not found: " + file.getAbsolutePath());
                return null;
            }

            // 读取文件内容
            StringBuilder content = new StringBuilder();
            FileReader reader = new FileReader(file);
            char[] buffer = new char[1024];
            int read;
            while ((read = reader.read(buffer)) != -1) {
                content.append(buffer, 0, read);
            }
            reader.close();

            // Base64解码
            byte[] combined = Base64.decode(content.toString(), Base64.NO_WRAP);
            if (combined.length < 12) {
                AVLEngine.Logger.error("Invalid encrypted data format");
                return null;
            }

            // 分离IV和加密数据
            byte[] iv = new byte[12];
            byte[] encrypted = new byte[combined.length - 12];
            System.arraycopy(combined, 0, iv, 0, 12);
            System.arraycopy(combined, 12, encrypted, 0, encrypted.length);

            // 初始化解密器
            final Cipher cipher = Cipher.getInstance(AES_MODE);
            GCMParameterSpec parameterSpec = new GCMParameterSpec(GCM_TAG_LENGTH, iv);
            cipher.init(Cipher.DECRYPT_MODE, secretKey, parameterSpec);

            // 解密数据
            byte[] decrypted = cipher.doFinal(encrypted);
            return new String(decrypted, StandardCharsets.UTF_8);

        } catch (Exception e) {
            AVLEngine.Logger.error("Failed to read encrypted data: " + e.getMessage());
            return null;
        }
    }

    /**
     * 检查文件是否存在
     */
    public boolean exists(String fileName) {
        if (storageDir == null) {
            return false;
        }
        File file = new File(storageDir, "." + fileName);
        return file.exists();
    }
} 