<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <!-- Hash输入 -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="文件Hash值"
        app:boxStrokeColor="?attr/colorPrimary"
        app:hintTextColor="?attr/colorPrimary">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/etHash"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="text"
            android:maxLines="3"
            android:minLines="1" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- 类型选择标签 -->
    <TextView
        android:id="@+id/tvTypeLabel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="选择类型"
        android:textColor="@color/black"
        android:textSize="16sp"
        android:textStyle="bold" />

    <!-- 类型选择 -->
    <RadioGroup
        android:id="@+id/radioGroupType"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:orientation="horizontal">

        <com.google.android.material.radiobutton.MaterialRadioButton
            android:id="@+id/radioWhite"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="白名单"
            android:textColor="@android:color/holo_green_dark" />

        <com.google.android.material.radiobutton.MaterialRadioButton
            android:id="@+id/radioBlack"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="黑名单"
            android:textColor="@android:color/holo_red_dark" />

    </RadioGroup>

    <!-- 说明文本 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:background="@drawable/bg_info_card"
        android:padding="12dp"
        android:text="• 白名单：跳过扫描受信任的文件\n• 黑名单：强制检查特定文件\n• Hash值通常为32位或64位十六进制字符串"
        android:textColor="@color/gray"
        android:textSize="12sp" />

</LinearLayout>
