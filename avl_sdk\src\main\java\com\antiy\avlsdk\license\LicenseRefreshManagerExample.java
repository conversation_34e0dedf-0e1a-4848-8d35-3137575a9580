package com.antiy.avlsdk.license;

import android.content.Context;

import com.antiy.avlsdk.AVLEngine;

/**
 * LicenseRefreshManager使用示例
 * 展示如何在AVLEngine中集成和使用LicenseRefreshManager
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class LicenseRefreshManagerExample {
    
    /**
     * 示例1：基本使用方式
     * 在AVLEngine初始化时创建LicenseRefreshManager
     */
    public static void basicUsageExample(Context context) {
        // 创建LicenseRefreshManager实例
        LicenseRefreshManager refreshManager = new LicenseRefreshManager.Builder(context)
                .setRefreshPolicy(new TimeBasedRefreshPolicy(context, 7 * 24 * 60 * 60 * 1000L)) // 7天
                .setNetworkChecker(new NetworkChecker(context))
                .setRefreshDelay(30 * 1000) // 30秒延迟
                .setRetryInterval(60 * 1000) // 60秒重试间隔
                .setMaxRetries(3) // 最大重试3次
                .setRefreshTimeout(60) // 60秒超时
                .build();
        
        // 在扫描时检查并刷新license
        refreshManager.checkAndRefresh(new RefreshCallback() {
            @Override
            public void onRefreshStart() {
                if (AVLEngine.Logger != null) {
                    AVLEngine.Logger.info("License刷新开始");
                }
            }
            
            @Override
            public void onRefreshSuccess() {
                if (AVLEngine.Logger != null) {
                    AVLEngine.Logger.info("License刷新成功");
                }
            }
            
            @Override
            public void onRefreshFailed(String reason) {
                if (AVLEngine.Logger != null) {
                    AVLEngine.Logger.error("License刷新失败: " + reason);
                }
            }
            
            @Override
            public void onRefreshSkipped(String reason) {
                if (AVLEngine.Logger != null) {
                    AVLEngine.Logger.info("License刷新跳过: " + reason);
                }
            }
        });
    }
    
    /**
     * 示例2：自定义刷新策略
     * 创建自定义的刷新策略
     */
    public static void customPolicyExample(Context context) {
        // 自定义刷新策略：每次启动都刷新
        RefreshPolicy alwaysRefreshPolicy = new RefreshPolicy() {
            @Override
            public boolean shouldRefresh() {
                return true; // 总是需要刷新
            }
            
            @Override
            public void onRefreshSuccess() {
                if (AVLEngine.Logger != null) {
                    AVLEngine.Logger.info("自定义策略：刷新成功");
                }
            }
            
            @Override
            public void onRefreshFailed(String reason) {
                if (AVLEngine.Logger != null) {
                    AVLEngine.Logger.error("自定义策略：刷新失败 - " + reason);
                }
            }
            
            @Override
            public String getDescription() {
                return "总是刷新策略";
            }
        };
        
        LicenseRefreshManager refreshManager = new LicenseRefreshManager.Builder(context)
                .setRefreshPolicy(alwaysRefreshPolicy)
                .build();
        
        // 使用自定义策略
        refreshManager.checkAndRefresh(new RefreshCallback.Empty() {
            @Override
            public void onRefreshSuccess() {
                if (AVLEngine.Logger != null) {
                    AVLEngine.Logger.info("使用自定义策略刷新成功");
                }
            }
        });
    }
    
    /**
     * 示例3：强制刷新
     * 在某些特殊情况下强制刷新license
     */
    public static void forceRefreshExample(Context context) {
        LicenseRefreshManager refreshManager = new LicenseRefreshManager.Builder(context)
                .build();
        
        // 强制刷新，忽略策略判断
        refreshManager.forceRefresh(new RefreshCallback() {
            @Override
            public void onRefreshStart() {
                if (AVLEngine.Logger != null) {
                    AVLEngine.Logger.info("强制刷新开始");
                }
            }
            
            @Override
            public void onRefreshSuccess() {
                if (AVLEngine.Logger != null) {
                    AVLEngine.Logger.info("强制刷新成功");
                }
            }
            
            @Override
            public void onRefreshFailed(String reason) {
                if (AVLEngine.Logger != null) {
                    AVLEngine.Logger.error("强制刷新失败: " + reason);
                }
            }
            
            @Override
            public void onRefreshSkipped(String reason) {
                // 强制刷新不会被跳过
            }
        });
    }
    
    /**
     * 示例4：状态监控
     * 监控刷新管理器的状态
     */
    public static void statusMonitoringExample(Context context) {
        LicenseRefreshManager refreshManager = new LicenseRefreshManager.Builder(context)
                .build();
        
        // 检查当前状态
        RefreshState currentState = refreshManager.getCurrentState();
        if (AVLEngine.Logger != null) {
            AVLEngine.Logger.info("当前刷新状态: " + currentState);
        }
        
        // 检查是否正在刷新
        boolean isRefreshing = refreshManager.isRefreshing();
        if (AVLEngine.Logger != null) {
            AVLEngine.Logger.info("是否正在刷新: " + isRefreshing);
        }
        
        // 获取策略描述
        String policyDescription = refreshManager.getRefreshPolicyDescription();
        if (AVLEngine.Logger != null) {
            AVLEngine.Logger.info("刷新策略: " + policyDescription);
        }
    }
    
    /**
     * 示例5：在AVLEngine中的集成方式
     * 展示如何在AVLEngine的scanDir方法中集成
     */
    public static class AVLEngineIntegrationExample {
        private LicenseRefreshManager licenseRefreshManager;
        
        public void initializeLicenseManager(Context context) {
            // 在AVLEngine初始化时创建LicenseRefreshManager
            this.licenseRefreshManager = new LicenseRefreshManager.Builder(context)
                    .setRefreshPolicy(new TimeBasedRefreshPolicy(context, 7 * 24 * 60 * 60 * 1000L))
                    .setNetworkChecker(new NetworkChecker(context))
                    .build();
        }
        
        public void scanDirWithLicenseRefresh(String path) {
            // 在扫描开始前检查license
            if (licenseRefreshManager != null) {
                licenseRefreshManager.checkAndRefresh(new RefreshCallback.Empty() {
                    @Override
                    public void onRefreshStart() {
                        if (AVLEngine.Logger != null) {
                            AVLEngine.Logger.info("扫描前License刷新开始");
                        }
                    }
                    
                    @Override
                    public void onRefreshSuccess() {
                        if (AVLEngine.Logger != null) {
                            AVLEngine.Logger.info("扫描前License刷新成功，继续扫描");
                        }
                        // 这里可以继续执行扫描逻辑
                    }
                    
                    @Override
                    public void onRefreshFailed(String reason) {
                        if (AVLEngine.Logger != null) {
                            AVLEngine.Logger.error("扫描前License刷新失败: " + reason + "，但继续扫描");
                        }
                        // 即使刷新失败，也可以继续扫描
                    }
                    
                    @Override
                    public void onRefreshSkipped(String reason) {
                        if (AVLEngine.Logger != null) {
                            AVLEngine.Logger.info("扫描前License刷新跳过: " + reason + "，继续扫描");
                        }
                        // 刷新被跳过，继续扫描
                    }
                });
            }
            
            // 执行实际的扫描逻辑
            // ... 现有的扫描代码 ...
        }
    }
}
