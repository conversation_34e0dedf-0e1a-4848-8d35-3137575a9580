package com.antiy.avlsdk.utils;

import android.text.TextUtils;
import android.util.Log;

import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;

/**
 * SDK调试log 打印工具类<br>
 * 修改自<a href="https://github.com/Blankj/ALog">ALog</a>,详细Log打印规则请参考:
 * <a href="https://wiki.avlyun.org/pages/viewpage.action?pageId=39241491">调试Log打印规范</a>
 *
 * <AUTHOR> on 2017/4/18.
 */

public final class AVLLog {
    /**
     * log等级verbose(未混淆包打印，混淆包不打印)
     */
    private static final int VERBOSE = 0x01;
    /**
     * log等级debug(混淆包可打印，需客户配置)
     */
    private static final int DEBUG = 0x02;
    /**
     * log等级fail(混淆未混淆均会打印)
     */
    private static final int FAIL = 0x04;
    /**
     * log的最大长度
     */
    private static final int MAX_LEN = 3500;
    /**
     * null字符串
     */
    private static final String NULL = "null";
    /**
     * log总开关，默认开
     */
    private static boolean sLogSwitch = true;
    /**
     * log标签
     */
    private static String sGlobalTag = SdkConst.AVLSDK;
    /**
     * log过滤器
     */
    private static int sLogFilter = FAIL;

    /**
     * 正式版本中会被移除
     */
    static {
        getUnProguardLogLevel();
    }

    private AVLLog() {
        throw new UnsupportedOperationException("u can't instantiate me...");
    }

    /**
     * 设置全局的log TAG
     *
     * @param tag TAG
     */
    public static void setGlobalTag(String tag) {
        if (!TextUtils.isEmpty(tag)) {
            sGlobalTag = tag;
        }
    }
    //sdk_huawei_ai clear start

    /**
     * 设置debug log 开启关闭(不设置默认关闭)
     *
     * @param enable true:打印Debug log false:不打印Debug log
     */
    public static void setLogEnable(boolean enable) {
        if (!enable) {
            sLogFilter = FAIL;
            return;
        }
        int logFilter = DEBUG;
        if ((sLogFilter & VERBOSE) != 0) {
            logFilter = VERBOSE;
        }
        sLogFilter = logFilter;
    }
    //sdk_huawei_ai clear end

    /**
     * 设置未混淆版本SDK打印最低log等级为VERBOSE
     */
    private static void getUnProguardLogLevel() {
        sLogFilter = VERBOSE;
    }

    /**
     * 格式化异常信息
     *
     * @param tr 待处理的异常
     * @return 处理后的异常信息
     */
    public static String getStackTraceString(Throwable tr) {
        if (tr == null) {
            return "";
        }
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        tr.printStackTrace(pw);
        pw.flush();
        String info = sw.toString();
        pw.close();
        try {
            sw.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return info;
    }

    /**
     * 打印VERBOSE等级的log
     *
     * @param msg 需要打印的信息
     */
    public static void v(String msg) {
        log(VERBOSE, sGlobalTag, msg);
    }

    /**
     * 打印VERBOSE等级的log
     *
     * @param tag TAG
     * @param msg 需要打印的信息
     */
    public static void v(String tag, String msg) {
        log(VERBOSE, sGlobalTag, tag + ':' + msg);
    }

    /**
     * 打印VERBOSE等级的log
     *
     * @param tag       TAG
     * @param msg       需要打印的信息
     * @param throwable 异常
     */
    public static void v(String tag, String msg, Throwable throwable) {
        log(VERBOSE, sGlobalTag, tag + ':' + msg + '\n' + getStackTraceString(throwable));
    }

    /**
     * 打印VERBOSE等级的log
     *
     * @param tag     TAG
     * @param format  需要打印的待格式化信息
     * @param objects format中占位符对应的待格式化的信息
     */
    public static void v(String tag, String format, Object... objects) {
        log(VERBOSE, sGlobalTag, tag + ':' + String.format(format, objects));
    }

    /**
     * 打印VERBOSE等级的log
     *
     * @param tag    TAG
     * @param caller 待回调打印的类
     * @param params 待打印的内容，具体内容在caller的实现类中定义
     */
    @SuppressWarnings("fb-contrib:RFI_SET_ACCESSIBLE")
    public static void v(String tag, Class<?> caller, Object... params) {
        if (IAVLLogCaller.class.isAssignableFrom(caller)) {
            try {
                IAVLLogCaller logCaller = (IAVLLogCaller) caller.newInstance();
                Class[] paramsClass = logCaller.getConstructorParamsClass();
                if (paramsClass != null && paramsClass.length > 0) {
                    Constructor<?> constructor = caller.getDeclaredConstructor(paramsClass);
                    constructor.setAccessible(true);
                    logCaller = (IAVLLogCaller) constructor.newInstance(params);
                }
                logCaller.call();
            } catch (NoSuchMethodException e) {
                AVLLog.v(tag, "caller get constructor failed", e);
            } catch (IllegalAccessException e) {
                AVLLog.v(tag, "caller get IllegalAccessException", e);
            } catch (InstantiationException e) {
                AVLLog.v(tag, "caller get InstantiationException", e);
            } catch (InvocationTargetException e) {
                AVLLog.v(tag, "caller get InvocationTargetException", e);
            }
        }
    }

    /**
     * 打印DEBUG等级的log
     *
     * @param msg 需要打印的信息
     */
    public static void d(String msg) {
        log(DEBUG, sGlobalTag, msg);
    }

    /**
     * 打印DEBUG等级的log
     *
     * @param throwable 异常
     */
    public static void d(Throwable throwable) {
        log(DEBUG, sGlobalTag, getStackTraceString(throwable));
    }

    /**
     * 打印DEBUG等级的log
     *
     * @param tag TAG
     * @param msg 需要打印的信息
     */
    public static void d(String tag, String msg) {
        log(DEBUG, sGlobalTag, tag + ':' + msg);
    }

    /**
     * 打印DEBUG等级的log
     *
     * @param msg       需要打印的信息
     * @param throwable 异常
     */
    public static void d(String msg, Throwable throwable) {
        log(DEBUG, sGlobalTag, msg + '\n' + getStackTraceString(throwable));
    }

    /**
     * 打印DEBUG等级的log
     *
     * @param tag       TAG
     * @param msg       需要打印的信息
     * @param throwable 异常
     */
    public static void d(String tag, String msg, Throwable throwable) {
        log(DEBUG, sGlobalTag, tag + ':' + msg + '\n' + getStackTraceString(throwable));
    }

    /**
     * 打印DEBUG等级的log
     *
     * @param tag     TAG
     * @param format  需要打印的待格式化信息
     * @param objects format中占位符对应的待格式化的信息
     */
    public static void d(String tag, String format, Object... objects) {
        log(DEBUG, sGlobalTag, tag + ':' + String.format(format, objects));
    }

    /**
     * 打印FAIL等级的log
     *
     * @param msg 需要打印的信息
     */
    public static void f(String msg) {
        log(FAIL, sGlobalTag, msg);
    }

    /**
     * 打印FAIL等级的log
     *
     * @param throwable 异常
     */
    public static void f(Throwable throwable) {
        log(FAIL, sGlobalTag, getStackTraceString(throwable));
    }

    /**
     * 打印FAIL等级的log
     *
     * @param tag TAG
     * @param msg 需要打印的信息
     */
    public static void f(String tag, String msg) {
        log(FAIL, sGlobalTag, tag + ':' + msg);
    }

    /**
     * 打印FAIL等级的log
     *
     * @param msg       需要打印的信息
     * @param throwable 异常
     */
    public static void f(String msg, Throwable throwable) {
        log(FAIL, sGlobalTag, msg + '\n' + getStackTraceString(throwable));
    }

    /**
     * 打印FAIL等级的log
     *
     * @param tag       TAG
     * @param msg       需要打印的信息
     * @param throwable 异常
     */
    public static void f(String tag, String msg, Throwable throwable) {
        log(FAIL, sGlobalTag, tag + ':' + msg + '\n' + getStackTraceString(throwable));
    }

    /**
     * 打印FAIL等级的log
     *
     * @param tag     TAG
     * @param format  需要打印的待格式化信息
     * @param objects format中占位符对应的待格式化的信息
     */
    public static void f(String tag, String format, Object... objects) {
        log(FAIL, sGlobalTag, tag + ':' + String.format(format, objects));
    }

    /**
     * 打印当前可打印等级的log
     *
     * @param type     log等级
     * @param tag      TAG
     * @param contents 待打印的内容信息
     */
    private static void log(int type, String tag, String contents) {
        if (!sLogSwitch) {
            return;
        }
        String msg = contents == null ? NULL : contents;
        switch (type) {
            case VERBOSE:
            case DEBUG:
            case FAIL:
                if (type >= sLogFilter) {
                    printLog(type, tag, msg);
                }
                break;
            default:
        }
    }

    /**
     * 对超出最大长度的log分段打印
     *
     * @param type log等级
     * @param tag  TAG
     * @param msg  待打印的log信息
     */
    private static void printLog(int type, String tag, String msg) {
        int len = msg.length();
        int prefixLength = 0;
        if (type == VERBOSE) {
            prefixLength = assemblyPrefix().length();
        }
        int splitLen = MAX_LEN - prefixLength;
        int countOfSub = len / splitLen;
        if (countOfSub > 0) {
            print(type, tag, msg.substring(0, splitLen));
            String sub;
            int index = splitLen;
            for (int i = 1; i < countOfSub; i++) {
                sub = msg.substring(index, index + splitLen);
                print(type, tag, sub);
                index += splitLen;
            }
            sub = msg.substring(index, len);
            print(type, tag, sub);
        } else {
            print(type, tag, msg);
        }
    }

    /**
     * 打印不同等级的log
     *
     * @param type log等级
     * @param tag  TAG
     * @param msg  待打印的log信息
     */
    private static void print(final int type, final String tag, String msg) {
        switch (type) {
            case VERBOSE:
                Log.println(Log.VERBOSE, tag, buildMessage(msg));
                break;
            case DEBUG:
                Log.println(Log.DEBUG, tag, msg);
                break;
            case FAIL:
                Log.println(Log.WARN, tag, msg);
                break;
            default:
        }
    }

    /**
     * 在日志中追加类名，方法名，行数
     *
     * @param msg 原始的日志
     * @return 追加后的日志
     */
    private static String buildMessage(String msg) {
        return new StringBuilder().append(assemblyPrefix())
                .append(msg).toString();
    }

    /**
     * 获取类名，方法名，行数等前缀
     *
     * @return
     */
    private static String assemblyPrefix() {
        try {
            StackTraceElement[] trace = new Throwable().fillInStackTrace().getStackTrace();
            StackTraceElement caller = null;
            int length = trace.length;
            for (int i = 0; i < length; i++) {
                String clazz = trace[i].getClassName();
                if (!clazz.equals(AVLLog.class.getName())) {
                    caller = trace[i];
                    break;
                }
            }
            if (caller == null) {
                return "";
            }
            return new StringBuilder().append(caller.getClassName()).append(".")
                    .append(caller.getMethodName()).append("().")
                    .append(caller.getLineNumber()).append(": ").toString();
        } catch (Exception e) {
            Log.println(Log.WARN, "Log", e.getMessage());
        }
        return "";
    }


}