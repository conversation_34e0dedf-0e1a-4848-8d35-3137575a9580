# 安天AVL SDK架构优化升级方案

## 一、项目现状分析

### 1.1 整体架构概述

AVL SDK是一个病毒扫描和防护的安全SDK，主要由以下几个核心模块组成：

- **AVL主模块（avl_sdk）**：提供核心功能接口、扫描引擎的控制和管理
- **PC引擎模块（avl_pc）**：专门处理非Android原生文件的扫描
- **性能监控模块（avl_monitor）**：提供CPU使用率监控能力

目前SDK采用典型的分层架构：
- API层：对外提供接口
- 核心层：实现业务逻辑
- 引擎层：封装底层扫描引擎
- 存储层：负责数据持久化
- 通信层：处理网络请求和进程间通信

### 1.2 核心问题

通过代码分析，识别出以下几个关键问题：

1. **初始化复杂度高**：`AVLEngine.init()`方法包含了太多步骤，且逻辑过于集中
2. **模块间耦合度高**：AVLEngine类承担了过多责任，包含了各类业务逻辑
3. **异步处理不统一**：更新模块使用RxJava，而扫描模块使用同步调用和回调方式
4. **错误处理机制不完善**：各模块错误处理方式不一致，部分只记录日志而不返回结构化错误
5. **内存和性能问题**：文件扫描过程中可能产生过多临时对象，CPU占用监控不够精细


## 二、现有架构详细分析

### 2.1 当前架构详解

#### 2.1.1 整体架构图

```plantuml
@startuml
package "AVL SDK Current Architecture" {
    [App Layer] as App
    
    package "AVL SDK" {
        [AVLEngine] as Engine
        [AVLCoreEngine] as Core
        
        package "Business Modules" {
            [Scan Module] as Scan
            [Update Module] as Update
            [Auth Module] as Auth
            [Monitor Module] as Monitor
        }
        
        package "Infrastructure" {
            [Storage] as Storage
            [Network] as Network
            [Native Engine] as Native
        }
    }
    
    App --> Engine
    Engine --> Core
    Core --> Scan
    Core --> Update
    Core --> Auth
    Core --> Monitor
    
    Scan --> Storage
    Scan --> Native
    Update --> Network
    Auth --> Network
    Monitor --> Storage
}
@enduml
```

#### 2.1.2 核心类职责

1. **AVLEngine类**
```java
public class AVLEngine {
    private static AVLEngine instance;
    private AVLCoreEngine coreEngine;
    private ScanManager scanManager;
    private UpdateManager updateManager;
    
    // 单例模式实现
    public static synchronized AVLEngine getInstance() {
        if (instance == null) {
            instance = new AVLEngine();
        }
        return instance;
    }
    
    // 初始化方法包含过多职责
    public ResultInit init(Context context, String id) {
        // 1. 上下文初始化
        // 2. 配置加载
        // 3. 数据库初始化
        // 4. 网络组件初始化
        // 5. 引擎初始化
        // 6. 授权验证
        // ...共计10多个步骤
    }
    
    // 业务方法直接调用多个模块
    public ResultScan scanFile(String path) {
        if (!updateManager.isLibraryValid()) {
            updateManager.updateLibrary();
        }
        return scanManager.scanFile(path);
    }
}
```

2. **扫描模块示例**
```java
public class ScanManager {
    private NativeScanner nativeScanner;
    private DatabaseHelper dbHelper;
    
    public ResultScan scanFile(String path) {
        // 业务逻辑和实现细节混合
        if (!checkPermission()) return error();
        if (!validateFile(path)) return error();
        
        // 直接操作数据库
        if (dbHelper.isInWhitelist(path)) {
            return ResultScan.safe();
        }
        
        // 直接调用native方法
        byte[] fileData = readFile(path);
        return nativeScanner.scan(fileData);
    }
}
```

### 2.2 现有架构问题详解

#### 2.2.1 模块耦合问题

1. **更新模块与引擎耦合**
```java
public class VirusDatabaseUpdater {
    public Single<ResultUpdate> checkUpdate() {
        return Single.create(emitter -> {
            // 直接依赖AVLEngine获取版本信息
            String confUrl = DOWNLOAD_PREFIX + AVLEngine.getVdbVersion() + ".conf";
            
            // 直接依赖AVLEngine进行网络请求
            AVLEngine.getInstance().getNetworkManager().download(
                confUrl,
                new DownloadCallback() {
                    // ...
                }
            );
        });
    }
}
```

2. **状态管理分散**
```java
public class UpdateStrategy {
    // 更新策略中直接管理PC引擎状态
    public void resetPcEngine() {
        File pcBakDir = new File(AVLEnginePC.getInstance().getAVLPCPath() + AVL_PC_BACKUP_SUFFIX);
        File pcDir = new File(AVLEnginePC.getInstance().getAVLPCPath());
        
        if (pcBakDir.exists()) {
            FileUtil.deleteDirectory(pcDir);
            pcBakDir.renameTo(pcDir);
        }
    }

    public boolean performUpdate(String packagePath, Pair<UpdateTypeEnum,UpdateTypeEnum> pair) {
        // 更新过程中直接操作多个引擎实例
        switch (pair.first) {
            case FULL:
            case INCREMENTAL:
                String version = AVLCoreEngine.getInstance().getSigLibVersion();
                result = AVLCoreEngine.getInstance().installPackage(packagePath, 1,
                        packageName.substring(0, packageName.lastIndexOf(".")),
                        (packageName.endsWith(SdkConst.ZIP_SUFFIX)) ? INSTALL_FULL : INSTALL_INCREMENT);
                break;
        }
    }
}
```

#### 2.2.2 异步处理问题

1. **异步实现不统一**
```java
public class VirusDatabaseUpdater {
    // 使用RxJava处理异步
    public Single<ResultUpdate> checkUpdate() {
        return Single.create(emitter -> {
            // ...
        })
        .subscribeOn(Schedulers.io())
        .doOnError(error -> 
            AVLEngine.Logger.error("Update check failed: " + error.getMessage())
        );
    }
}

public class UpdateStrategy {
    // 使用同步方式执行
    public boolean pcVirusUpdateCheck() {
        String exePath = AVLEngine.getInstance().getContext().getFilesDir() + PC_VIRUS_UPDATE_CHECK_PATH;
        try {
            Process process = Runtime.getRuntime().exec(exePath);
            process.waitFor();
            return process.exitValue() == 0;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
}
```

2. **错误处理不统一**
```java
public class VirusDatabaseUpdater {
    private Single<ResultUpdate> downloadAndUpdate(VirusUpdateEntity virusUpdate) {
        return Single.<ResultUpdate>create(emitter -> {
            AVLEngine.getInstance().getNetworkManager().download(
                downloadUrl,
                new DownloadCallback() {
                    @Override
                    public void onFinish(int code, String responseFileName, String localFilePath) {
                        try {
                            // 文件校验失败直接返回结果对象
                            if (!fileHash.equals(virusUpdate.getHash())) {
                                emitter.onSuccess(new ResultUpdate(true,false));
                                return;
                            }
                            
                            // 更新失败抛出异常
                            if (!result.isUpdateSucceeded()) {
                                emitter.onError(new Exception("update fail"));
                            }
                        } catch (Exception e) {
                            emitter.onError(e);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        // 网络错误转换为异常
                        emitter.onError(new Exception(msg));
                    }
                }
            );
        });
    }
}
```

#### 2.2.3 扫描模块架构问题

1. **继承层次设计问题**
```java
public abstract class BaseScanner {
    protected List<File> files;
    protected ScanListener callback;
    protected BlockingQueue<ScanTask> taskQueue;
    protected volatile boolean isScanning = false;

    public BaseScanner(List<File> files, ScanListener callback) {
        this.files = files;
        this.callback = callback;
        this.taskQueue = new LinkedBlockingQueue<>();
    }
}

// LocalScanner和CloudScanner虽然继承自BaseScanner，但实现逻辑差异很大
public class LocalScanner extends BaseScanner {
    private final AtomicInteger processedFiles = new AtomicInteger(0);
    private final AtomicBoolean hasFinished = new AtomicBoolean(false);
    
    @Override
    public void startScan() {
        // 本地扫描实现...
    }
}

public class CloudScanner extends BaseScanner {
    private final ExecutorService hashExecutor;
    private final ExecutorService scanExecutor;
    private final AtomicInteger currentIndex = new AtomicInteger(0);
    
    @Override
    public void startScan() {
        // 云端扫描实现...
    }
}
```

2. **状态管理和并发处理问题**
```java
public class FileScanner {
    public static ResultScan scan(String filePath) {
        // 全局状态检查
        if (!AVLEngine.getInstance().getInitResult().isSuccess) {
            return new ResultScan("engine is init fail,please init sdk engine");
        }
        
        // 文件大小判断和云扫描切换
        long length = new File(filePath).length();
        long spCloudSizeThreshold = DataManager.getInstance().getCloudSizeThreshold();
        if (length > spCloudSizeThreshold && AVLEngine.getInstance().getNetworkManager().isAvailable()) {
            return cloudScan(filePath);
        }
        
        // 文件类型判断和引擎选择
        if (FileChecker.getInstance().isApkOrJarOrDex(new File(filePath))) {
            ResultScan resultScan = MobileEngine.scan(filePath);
            CacheManager.storeScanResult(filePath, resultScan.virusName);
            return resultScan;
        } else {
            ResultScan resultScan = PcEngine.scan(filePath);
            CacheManager.storeScanResult(filePath, resultScan.virusName);
            return resultScan;
        }
    }
}
```

3. **异步处理和回调嵌套**
```java
public class CloudScanner extends BaseScanner {
    private void processScan() {
        try {
            while (isScanning || !taskQueue.isEmpty()) {
                ScanTask task = taskQueue.poll(1, TimeUnit.SECONDS);
                if (task != null) {
                    // 回调嵌套
                    callback.scanFileStart(task.index, task.file.getAbsolutePath());
                    AVLEngine.getInstance().getNetworkManager().upload(
                        task.file.getAbsolutePath(),
                        new UploadCallback() {
                            @Override
                            public void onFinish(int code, String responseFileName) {
                                // 上传完成后的云查杀回调
                                cloudQuery(task, responseFileName, new QueryCallback() {
                                    @Override
                                    public void onResult(ResultScan result) {
                                        callback.scanFileFinish(task.index, task.file.getAbsolutePath(), result);
                                    }
                                });
                            }
                        }
                    );
                }
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
}
```

4. **错误处理不统一**
```java
public class LocalScanner extends BaseScanner {
    private void processLocalScan(ScanTask task) {
        try {
            callback.scanFileStart(task.index, task.file.getAbsolutePath());
            ResultScan localResult = FileScanner.scan(task.file.getAbsolutePath());
            callback.scanFileFinish(task.index, task.file.getAbsolutePath(), localResult);
        } catch (Exception e) {
            // 异常只记录日志
            AVLEngine.Logger.error("Processing file failed: " + task.file + ", Error: " + e.getMessage());
            processedFiles.incrementAndGet();
        }
    }
}

public class CloudScanner extends BaseScanner {
    private void cloudQuery(ScanTask task, String hash, QueryCallback callback) {
        if (!isScanning) {
            // 直接返回本地扫描结果
            processLocalScanBatch(Collections.singletonList(task));
            return;
        }
        
        try {
            // 网络错误降级处理
            if (!AVLEngine.getInstance().getNetworkManager().isAvailable()) {
                processLocalScanBatch(Collections.singletonList(task));
                return;
            }
            // 其他实现...
        } catch (Exception e) {
            // 异常转换为回调
            callback.onResult(new ResultScan(false));
        }
    }
}
```

### 2.3 新旧架构对比

| 特性 | 现有架构 | 优化后架构 | 优势说明 |
|------|----------|------------|----------|
| 模块化程度 | 模块间强耦合 | 领域驱动设计，模块独立 | 更易维护和扩展 |
| 依赖管理 | 直接依赖 | 依赖注入，接口隔离 | 提高可测试性 |
| 异步处理 | 多种方式混用 | 统一使用CompletableFuture | 简化异步代码 |
| 错误处理 | 分散，不统一 | 统一错误处理体系 | 提升可靠性 |
| 代码组织 | 技术层次划分 | 业务领域划分 | 更符合业务逻辑 |
| 扩展性 | 修改现有代码 | 插件式架构 | 降低修改成本 |
| 测试难度 | 难以单元测试 | 易于测试 | 提高代码质量 |
| 性能开销 | 资源使用不优化 | 优化资源管理 | 提升性能 |

## 三、优化方案详细设计

### 3.1 新架构设计

#### 3.1.1 整体架构图

```plantuml
@startuml
package "Clean Architecture" {
    package "Interface Layer" {
        [AVLEngine]
        [Configuration]
    }
    
    package "Domain Layer" {
        [ScanService]
        [UpdateService]
        [AuthService]
        [MonitorService]
    }
    
    package "Infrastructure Layer" {
        [StorageImpl]
        [NetworkImpl]
        [NativeEngine]
    }
}
@enduml
```

#### 3.1.2 核心接口设计

```java
// 领域服务接口
public interface ScanService {
    CompletableFuture<ScanResult> scanFile(String path);
    CompletableFuture<ScanResult> scanDirectory(String path);
    void cancelScan();
}

public interface UpdateService {
    CompletableFuture<Boolean> checkUpdate();
    CompletableFuture<UpdateResult> performUpdate();
}

// 基础设施接口
public interface ScanRepository {
    CompletableFuture<byte[]> readFile(String path);
    CompletableFuture<List<String>> listFiles(String directory);
}

public interface UpdateRepository {
    CompletableFuture<byte[]> downloadUpdate(String version);
    CompletableFuture<Boolean> saveUpdate(byte[] data);
}
```

### 3.2 关键改进实现

#### 3.2.1 依赖注入

```java
// 使用Dagger2实现依赖注入
@Module
public class AVLModule {
    @Provides
    @Singleton
    public ScanService provideScanService(
            ScanRepository scanRepo,
            UpdateService updateService) {
        return new ScanServiceImpl(scanRepo, updateService);
    }
    
    @Provides
    @Singleton
    public UpdateService provideUpdateService(
            UpdateRepository updateRepo) {
        return new UpdateServiceImpl(updateRepo);
    }
}
```

#### 3.2.2 异步处理统一

```java
public class ScanServiceImpl implements ScanService {
    private final ScanRepository scanRepo;
    private final UpdateService updateService;
    
    @Inject
    public ScanServiceImpl(ScanRepository scanRepo, 
                          UpdateService updateService) {
        this.scanRepo = scanRepo;
        this.updateService = updateService;
    }
    
    @Override
    public CompletableFuture<ScanResult> scanFile(String path) {
        return updateService.checkUpdate()
            .thenCompose(needsUpdate -> {
                if (needsUpdate) {
                    return updateService.performUpdate()
                        .thenCompose(__ -> doScan(path));
                }
                return doScan(path);
            });
    }
    
    private CompletableFuture<ScanResult> doScan(String path) {
        return scanRepo.readFile(path)
            .thenApply(this::performScan)
            .exceptionally(this::handleError);
    }
}
```

#### 3.2.3 错误处理统一

```java
public class AVLException extends Exception {
    private final ErrorCode code;
    private final String message;
    
    public AVLException(ErrorCode code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }
}

public class ErrorHandler {
    public static <T> CompletableFuture<T> handleError(Throwable error) {
        if (error instanceof AVLException) {
            return CompletableFuture.completedFuture(
                Result.error((AVLException) error));
        }
        return CompletableFuture.completedFuture(
            Result.error(new AVLException(
                ErrorCode.UNKNOWN, error.getMessage())));
    }
}
```

### 3.3 性能优化设计

#### 3.3.1 对象池化

```java
public class ScanResultPool {
    private final ObjectPool<ScanResult> pool;
    
    public ScanResultPool() {
        pool = new GenericObjectPool<>(new ScanResultFactory());
    }
    
    public ScanResult borrowResult() {
        return pool.borrowObject();
    }
    
    public void returnResult(ScanResult result) {
        pool.returnObject(result);
    }
}
```

#### 3.3.2 内存优化

```java
public class FileScanner {
    private static final int BUFFER_SIZE = 8192;
    private final byte[] buffer = new byte[BUFFER_SIZE];
    
    public ScanResult scanLargeFile(String path) {
        try (FileInputStream fis = new FileInputStream(path)) {
            // 使用固定buffer而不是每次创建新的
            int bytesRead;
            while ((bytesRead = fis.read(buffer)) != -1) {
                processChunk(buffer, bytesRead);
            }
        }
        return buildResult();
    }
}
```

### 3.4 架构优化补充设计

#### 3.4.1 模块化设计优化

建议将SDK按照以下方式进行更细粒度的模块划分：

```plantuml
@startuml
package "AVL SDK Architecture" {
    [Core Module] as core
    [Scan Module] as scan
    [Update Module] as update
    [Monitor Module] as monitor
    [Storage Module] as storage
    [Network Module] as network
    [Security Module] as security
    [Plugin Module] as plugin
    
    core --> scan
    core --> update
    core --> monitor
    core --> security
    
    scan --> storage
    scan --> plugin
    update --> network
    update --> storage
    monitor --> storage
    security --> storage
}
@enduml
```

模块职责说明：
- Core Module: 核心功能和模块协调
- Scan Module: 文件扫描和病毒检测
- Update Module: 病毒库和配置更新
- Monitor Module: 性能和资源监控
- Storage Module: 数据存储和缓存
- Network Module: 网络通信
- Security Module: 安全和加密
- Plugin Module: 插件化扩展

#### 3.4.2 插件化架构设计

引入插件化架构以支持功能扩展：

```java
public interface AVLPlugin {
    String getPluginId();
    void initialize(Context context);
    void onScanStart(ScanContext context);
    void onScanComplete(ScanResult result);
    void destroy();
}

public class PluginManager {
    private Map<String, AVLPlugin> plugins;
    
    public void registerPlugin(AVLPlugin plugin);
    public void unregisterPlugin(String pluginId);
    public void notifyPlugins(PluginEvent event);
}
```

#### 3.4.3 事件总线设计

引入事件总线优化模块间通信：

```java
public class AVLEventBus {
    private static final ExecutorService executor = 
        Executors.newCachedThreadPool();
    
    public void post(AVLEvent event);
    public void register(EventListener listener);
    public void unregister(EventListener listener);
}
```

#### 3.4.4 配置中心设计

统一配置管理机制：

```java
public class ConfigurationCenter {
    private final Map<String, Object> configurations;
    private final ConfigStorage storage;
    
    public void updateConfig(String key, Object value);
    public <T> T getConfig(String key, Class<T> type);
    public void loadConfigurations();
    public void saveConfigurations();
}
```

### 3.5 接口设计优化

#### 3.5.1 统一返回值封装

```java
public class AVLResult<T> {
    private final boolean success;
    private final T data;
    private final int code;
    private final String message;
    
    public static <T> AVLResult<T> success(T data);
    public static <T> AVLResult<T> error(int code, String message);
}
```

#### 3.5.2 API版本控制

```java
public interface AVLAPI {
    @APIVersion(1)
    ScanResult scan(String path);
    
    @APIVersion(2)
    AVLResult<ScanResult> scanV2(String path);
}
```

### 3.6 安全性优化

#### 3.6.1 数据加密存储

```java
public class SecurityStorage {
    private final Cipher cipher;
    private final SecretKey key;
    
    public void secureStore(String key, byte[] data);
    public byte[] secureRetrieve(String key);
}
```

#### 3.6.2 通信加密

```java
public class SecureChannel {
    private final SSLContext sslContext;
    private final TrustManager trustManager;
    
    public void initializeSecureChannel();
    public void sendSecureRequest(Request request);
}
```

### 3.7 监控体系优化

#### 3.7.1 性能指标收集

```java
public class MetricsCollector {
    private final MetricsStorage storage;
    private final MetricsAggregator aggregator;
    
    public void recordMetric(String name, double value);
    public void recordLatency(String operation, long startTime);
    public Map<String, MetricsSummary> getMetricsSummary();
}
```

#### 3.7.2 健康检查机制

```java
public class HealthChecker {
    private final List<HealthIndicator> indicators;
    
    public HealthStatus check();
    public void registerIndicator(HealthIndicator indicator);
}
```

## 四、技术选型考虑

### 5.1 Java vs Kotlin评估

考虑到现有代码库是Java编写的，全面迁移到Kotlin的成本较高，我们建议采取渐进式改进策略：

1. **短期（保持Java）**：
   - 主要使用Java进行重构
   - 应用Java 8特性（如Lambda、Stream API）提高代码质量
   - 保持与现有系统的兼容性

2. **中期（混合使用）**：
   - 新模块可以考虑使用Kotlin开发
   - 关键性能模块保留Java
   - 利用Java-Kotlin互操作性

3. **长期（可选迁移）**：
   - 根据项目发展需要评估完全迁移到Kotlin的必要性
   - 如需迁移，可采用模块化渐进式迁移

### 5.2 异步框架选择

1. **保留RxJava**：继续使用RxJava 3作为复杂异步操作的框架
2. **引入CompletableFuture**：作为标准Java API，简化基本异步操作
3. **避免引入协程**：由于主要使用Java，暂不考虑Kotlin协程

### 5.3 存储方案选择

1. **保留SQLite**：作为主要的结构化存储方案
2. **优化加密存储**：提升敏感数据的安全性
3. **考虑Room**：评估是否引入Room简化数据访问层

## 六、结语

本SDK升级方案通过详细对比现有架构和优化后架构，清晰展示了改进点和预期收益。新架构将显著提升代码质量、可维护性和性能表现，同时保持与现有系统的兼容性。通过分阶段实施，可以平滑完成升级过程，为后续功能扩展打下坚实基础。
