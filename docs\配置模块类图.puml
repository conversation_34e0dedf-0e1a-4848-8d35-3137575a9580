@startuml
title AVL SDK 配置管理类图

class AVLEngine {
  -Config config
  +void loadConfig(Config config)
  +void updateConfig(ConfigUpdater updater)
  +ResultScan scanFile(String path)
  +void scanDir(String path, ScanListener listener)
}

class Config {
  -HashMap<String,Boolean> blackWhiteMap
  -List<ScanFileType> scanTypes
  -int cloudThreshold
  -long cloudSizeThreshold
  -int silentThreshold
  -long historyTimeout
  -long historySize
  +void create()
  +void updateBlackWhiteList()
  +void updateScanTypes()
  +void updateCloudThreshold()
  +void updateCloudSizeThreshold()
  +void updateSilentThreshold()
  +void updateHistoryTimeout()
  +void updateHistorySize()
}

class "Config.Builder" as Builder {
  -HashMap<String,Boolean> blackWhiteMap
  -List<ScanFileType> scanTypes
  -int cloudThreshold
  -long cloudSizeThreshold
  -int silentThreshold
  -long historyTimeout
  -long historySize
  +Builder setBlackWhiteList()
  +Builder setScanType()
  +Builder setCloudCheckThreshold()
  +Builder setCloudSizeThreshold()
  +Builder setSilentPerformanceThreshold()
  +Builder setHistoryTimeout()
  +Builder setHistorySize()
  +Config build()
}

interface ConfigUpdater {
  +void update(Config config)
}

class DataManager {
  -static DataManager instance
  -DatabaseHelper dbHelper
  -EncryptorHelper encryptor
  -SharedPreferences preferences
  +int getCloudCheckThreshold()
  +long getCloudSizeThreshold()
  +int getSilentPerformanceThreshold()
  +long getHistoryCacheSize()
  +long getHistoryCacheTimeout()
  +void saveBlackWhiteList()
  +void saveScanType()
  +void saveCloudCheckThreshold()
  +void saveCloudSizeThreshold()
  +void saveSilentPerformanceThreshold()
  +void saveHistoryTimeout()
  +void saveHistorySize()
  +void maintainDatabase()
}

class DatabaseHelper {
  +void insertBlackWhiteData()
  +HashMap<String,Boolean> getBlackWhiteListData()
  +void insertCacheData()
  +CacheEntity getCacheData()
  +void maintainDatabase()
}

enum ConfigKey {
  BLACK_WHITE_LIST
  SCAN_TYPE
  HISTORY_SIZE
  HISTORY_TIMEOUT
  CLOUDSCAN_COUNT
  CLOUDSCAN_SIZE
  SILENTSCAN_RES
}

enum ScanFileType {
  JAR
  DEX
  APK
  MP3
  MP4
  JPG
  PNG
  GIF
  ELF
  +List<String> toStringList()
  +List<ScanFileType> fromStringList()
}

class FileChecker {
  -List<ScanFileType> VALID_TYPES
  +boolean isValidType(File file)
  +boolean isApkOrJarOrDex(File file)
  -boolean isValidByMagicNumber(File file)
}

class ScannerFactory {
  +IScanner createScanner(ScanMode mode, List<File> files, ScanListener callback)
}

AVLEngine o-- Config
Config *-- Builder
AVLEngine ..> ConfigUpdater : uses
ConfigUpdater ..> Config : modifies
Config ..> DataManager : uses for persistence
DataManager o-- DatabaseHelper
DataManager ..> ConfigKey : uses as keys
Config ..> ScanFileType : contains list of
FileChecker ..> ScanFileType : uses for validation
ScannerFactory ..> DataManager : uses config from

@enduml