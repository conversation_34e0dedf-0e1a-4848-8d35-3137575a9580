//
// Created by zos on 9/12/24.
//

#ifndef DEMO_AVLM_INTERFACE_H
#define DEMO_AVLM_INTERFACE_H


/**
 * 宏定义
 */
// 用于安卓扫描的配置选项
#define AVLM_PLATFORM_Android       0x01000000
#define AVLM_ADR_ScanOpt_Default    (0x1 << 0)
#define AVLM_ADR_ScanOpt_FILENAME   (0x1 << 1)
#define AVLM_ADR_ScanOpt_RELATION   (0x1 << 2)
#define AVLM_ADR_ScanOpt_DEXOPDATA  (0x1 << 3)
#define AVLM_ADR_ScanOpt_AMFILE     (0x1 << 4)
#define AVLM_ADR_ScanOpt_SIGN       (0x1 << 5)
#define AVLM_ADR_ScanOpt_DEXSYM     (0x1 << 6)
#define AVLM_ADR_ScanOpt_ELFOPC     (0X1 << 10)
#define AVLM_ADR_ScanOpt_EMBED_LEV1 (0x1 << 13)
#define AVLM_ADR_ScanOpt_EMBED_LEV2 (0x1 << 14)
#define AVLM_ADR_ScanOpt_DEXOPCODE  (0x1 << 15)
#define AVLM_ADR_ScanOpt_EXPLOIT    (0x1 << 17)
#define AVLM_ADR_ScanOpt_PACK       (0x1 << 16)
#define AVLM_ADR_ScanOpt_ADWARE     (0x1 << 21)
#define AVLM_ADR_ScanOpt_ALL        0x00FFFFFF

// 配置引擎支持的病毒类型
#define AVLM_CATEGORY_VIRUS    (0x1 << 0)
#define AVLM_CATEGORY_WORM     (0x1 << 1)
#define AVLM_CATEGORY_TROJAN   (0x1 << 2)
#define AVLM_CATEGORY_GWARE    (0x1 << 3)
#define AVLM_CATEGORY_TOOL     (0x1 << 4)
#define AVLM_CATEGORY_RISKWARE (0x1 << 5)
#define AVLM_CATEGORY_AVTEST   (0x1 << 6)
#define AVLM_CATEGORY_PORNWARE (0x1 << 7)
#define AVLM_CATEGORY_PAYWARE  (0x1 << 8)
#define AVLM_CATEGORY_ADWARE   (0x1 << 9)
#define AVLM_CATEGORY_WARN     (0x1 << 10)
#define AVLM_CATEGORY_PACK     (0x1 << 11)
#define AVLM_CATEGORY_WHITE    (0x1 << 12)
#define AVLM_CATEGORY_NOTVIR   (0x1 << 13)
// 组合类型
#define AVLM_CATEGORY_MIN (AVLM_CATEGORY_VIRUS | AVLM_CATEGORY_WORM | AVLM_CATEGORY_TROJAN | AVLM_CATEGORY_GWARE)
#define AVLM_CATEGORY_DEFAULT \
        (AVLM_CATEGORY_VIRUS | AVLM_CATEGORY_WORM | AVLM_CATEGORY_TROJAN | AVLM_CATEGORY_GWARE | AVLM_CATEGORY_WHITE | AVLM_CATEGORY_RISKWARE)
#define AVLM_CATEGORY_ALL                                                                                                                            \
        (AVLM_CATEGORY_DEFAULT | AVLM_CATEGORY_TOOL | AVLM_CATEGORY_AVTEST | AVLM_CATEGORY_PORNWARE | AVLM_CATEGORY_PAYWARE | AVLM_CATEGORY_ADWARE | \
         AVLM_CATEGORY_WARN | AVLM_CATEGORY_PACK | AVLM_CATEGORY_NOTVIR)
#define AVLM_CATEGORY_FASTSCAN \
        (AVLM_CATEGORY_VIRUS | AVLM_CATEGORY_WORM | AVLM_CATEGORY_TROJAN | AVLM_CATEGORY_GWARE | AVLM_CATEGORY_WHITE | AVLM_CATEGORY_RISKWARE)
#define AVLM_CATEGORY_DEEPSCAN                                                                                                                  \
        (AVLM_CATEGORY_DEFAULT | AVLM_CATEGORY_TOOL | AVLM_CATEGORY_AVTEST | AVLM_CATEGORY_PORNWARE | AVLM_CATEGORY_WARN | AVLM_CATEGORY_PACK | \
         AVLM_CATEGORY_NOTVIR)
#define AVLM_CATEGORY_PAY (AVLM_CATEGORY_DEFAULT | AVLM_CATEGORY_PAYWARE)

// 函数符号名称宏
#ifdef AVLM_USE_AUTH
#        define AVLM_AuthInit      "vbry"
#        define AVLM_AuthRelease   "boim"
#        define AVLM_AuthGetProof  "xlhz"
#        define AVLM_AuthFreeProof "jnyt"
#else
#        define AVLM_Init    "AVLM_Init"
#        define AVLM_Release "AVLM_Release"
#endif
#define AVLM_InstallPackage     "AVLM_InstallPackage"
#define AVLM_SetScanOpt         "AVLM_SetScanOpt"
#define AVLM_SetScanCategoryOpt "AVLM_SetScanCategoryOpt"
#define AVLM_Scan               "AVLM_Scan"
#define AVLM_ScanWithScanOpt    "AVLM_ScanWithScanOpt"
#define AVLM_ScanWithScanOptEx  "AVLM_ScanWithScanOptEx"
#define AVLM_ScanWithArgs       "AVLM_ScanWithArgs"
#define AVLM_ScanFdWithArgs     "AVLM_ScanFdWithArgs"
#define AVLM_GetVirnameEx       "AVLM_GetVirnameEx"
#define AVLM_GetWhitenameEx     "AVLM_GetWhitenameEx"
#define AVLM_Result_Free        "AVLM_Result_Free"
#define AVLM_GetEngineVersion   "AVLM_GetEngineVersion"
#define AVLM_GetSigLibVersion   "AVLM_GetSigLibVersion"

/*
 * 类型定义
 */
typedef void* AVLM_Result;

typedef struct _ScanArgs1 {
        unsigned int scanOpt;        // 扫描开关
        unsigned int categoryFlag;   // 种类开关
        unsigned int logOpt;         // 日志开关
#ifndef _AVLM_DISABLE_APP_INFO_EXTRACT
        long long firstInstallTime;   // 应用的首次安装时间
#endif
} ScanArgs1;

/*
 * 函数定义
 */
#ifdef AVLM_USE_AUTH
typedef int (*AVLM_AuthInit_FUNC)(char* pcPath, char* pcUuid, char* pcLicense, int iLicenseLen);
typedef int (*AVLM_AuthRelease_FUNC)();
typedef char* (*AVLM_AuthGetProof_FUNC)(char* pcLicense, int iLicenseLen);
typedef void (*AVLM_AuthFreeProof_FUNC)(char* pcLicense);
#else
typedef int (*AVLM_Init_FUNC)(char*);
typedef int (*AVLM_Release_FUNC)();
typedef int (*AVLM_InitEx_FUNC)(char* aPath, unsigned int aFlag);
#endif

typedef int (*AVLM_SetScanOpt_FUNC)(unsigned int aFlag);
typedef int (*AVLM_SetScanCategoryOpt_FUNC)(unsigned int aFlag);
typedef void* (*AVLM_Scan_FUNC)(char* aPath);
typedef void* (*AVLM_ScanWithScanOpt_FUNC)(char* aPath, unsigned int aScanOpt);
typedef void* (*AVLM_ScanWithScanOptEx_FUNC)(char* aPath, unsigned int aScanOpt, unsigned int aCategoryFlag);
typedef char* (*AVLM_ScanWithArgs_FUNC)(char* aPath, void* pArgs);
typedef char* (*AVLM_ScanFdWithArgs_FUNC)(int aFd, void* pArgs);
typedef char* (*AVLM_GetVirnameEx_FUNC)(void* aAResult);
typedef char* (*AVLM_GetWhitenameEx_FUNC)(void* aAResult);
typedef void (*AVLM_Result_Free_FUNC)(void* aAResult);
typedef char* (*AVLM_GetCertHash_FUNC)(char* aPath);
typedef int (*AVLM_InstallPackage_FUNC)(char* aPackagePath, int aPackageType, char* aToVersion, char* aSigPackageType);
typedef char* (*AVLM_GetInfo_FUNC)();

#endif   // DEMO_AVLM_INTERFACE_H
