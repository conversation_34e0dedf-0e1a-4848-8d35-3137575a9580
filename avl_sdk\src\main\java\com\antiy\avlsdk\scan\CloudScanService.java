package com.antiy.avlsdk.scan;

import static com.antiy.avlsdk.utils.SdkConst.CLOUD_CHECK_URL;
import static com.antiy.avlsdk.utils.SdkConst.SUCCESS_CODE;

import com.antiy.avlsdk.AVLEngine;
import com.antiy.avlsdk.callback.RequestCallback;
import com.antiy.avlsdk.entity.BaseCloudScanResponse;
import com.antiy.avlsdk.entity.RequestMethod;
import com.antiy.avlsdk.entity.ResultCloudScan;
import com.antiy.avlsdk.utils.JsonUtils;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.List;


public class CloudScanService {

    private static volatile CloudScanService mInstance;

    private CloudScanService() {

    }

    public static CloudScanService getInstance() {
        if (mInstance == null) {
            synchronized (CloudScanService.class) {
                if (mInstance == null) {
                    mInstance = new CloudScanService();
                }
            }
        }
        return mInstance;
    }

    public interface CloudScanCallback {
        void onSuccess(List<ResultCloudScan> results);

        void onError(String error);
    }

    public void batchScanAsync(List<String> hashes, CloudScanCallback callback) {
        JSONObject param  = createBatchScanRequest(hashes);
        AVLEngine.getInstance().getNetworkManager().request(CLOUD_CHECK_URL, RequestMethod.POST, param, new RequestCallback() {
            @Override
            public void onError(String msg) {
                callback.onError("Network request failed: " + msg);
            }

            @Override
            public void onFinish(int code, Object responseBody) {
                BaseCloudScanResponse entity = JsonUtils.toObject(responseBody.toString(), BaseCloudScanResponse.class);
                if (entity != null && entity.code == SUCCESS_CODE) {
                    AVLEngine.Logger.info( "CloudScanService Parsed results: " + entity.data.toString());  // 添加解析结果日志
                    callback.onSuccess(entity.data);
                }else {
                    AVLEngine.Logger.error( "CloudScanService Server error with code: " + entity.code);
                }

            }
        });
    }

    private JSONObject createBatchScanRequest(List<String> hashes) {
        String jsonBody = JsonUtils.listToJson(hashes);
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("hash", new JSONArray(jsonBody));
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return jsonObject;
    }


}
