package com.antiy.demo.model

/**
 * 威胁详情数据类
 */
data class ThreatDetail(
    val name: String,           // 威胁名称
    val location: String,       // 威胁位置
    val description: String,    // 威胁描述
    val riskLevel: RiskLevel,   // 风险等级
    val type: ThreatType        // 威胁类型
) {
    // 风险等级枚举
    enum class RiskLevel {
        HIGH, MEDIUM, LOW
    }
    
    // 威胁类型枚举
    enum class ThreatType {
        MALWARE, SUSPICIOUS_BEHAVIOR, PRIVACY_RISK
    }
} 