package com.antiy.avlsdk.entity;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 病毒信息结果类
 * 包含病毒的类别、行为特征、风险等级和执行建议
 */
public class VirusInfo {
    private final VirusCategory category;           // 病毒类别
    private final List<BehaviorCode> behaviors;     // 行为特征列表
    private final RiskLevel riskLevel;             // 风险等级
    private final ActionSuggestion suggestion;      // 执行建议

    public VirusInfo(VirusCategory category, List<BehaviorCode> behaviors,
                     RiskLevel riskLevel, ActionSuggestion suggestion) {
        this.category = category;
        this.behaviors = behaviors != null ? new ArrayList<>(behaviors) : new ArrayList<>();
        this.riskLevel = riskLevel != null ? riskLevel : RiskLevel.LOW;
        this.suggestion = suggestion != null ? suggestion : ActionSuggestion.USE_CAUTION;
    }

    public VirusCategory getCategory() {
        return category;
    }

    public List<BehaviorCode> getBehaviors() {
        return Collections.unmodifiableList(behaviors);
    }

    public RiskLevel getRiskLevel() {
        return riskLevel;
    }

    public ActionSuggestion getSuggestion() {
        return suggestion;
    }

    @Override
    public String toString() {
        return "VirusInfo{" +
                "category=" + category +
                ", behaviors=" + behaviors +
                ", riskLevel=" + riskLevel +
                ", suggestion=" + suggestion +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        VirusInfo virusInfo = (VirusInfo) o;
        return Objects.equals(category, virusInfo.category) &&
                Objects.equals(behaviors, virusInfo.behaviors) &&
                riskLevel == virusInfo.riskLevel &&
                suggestion == virusInfo.suggestion;
    }

    @Override
    public int hashCode() {
        return Objects.hash(category, behaviors, riskLevel, suggestion);
    }
}
