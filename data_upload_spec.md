### **长城私有云车机SDK数据回传功能技术文档**

#### 1. 背景

为了更好地了解和支持在长城私有云上运行我们SDK的设备情况，并为后续的产品优化、数据分析和运营决策提供数据支持，我们需要在现有的车机（In-Vehicle Infotainment, IVI）SDK中增加数据回传功能。本文档旨在阐述该功能的技术实现方案，包括回传数据的具体内容、获取方式以及回传时机。

#### 2. 数据回传规范

每次回传需要包含以下设备信息：

| 字段 | 英文标识 | 数据类型 | 说明 |
| :--- | :--- | :--- | :--- |
| 设备品牌 | `brand` | String | 设备的品牌，例如 "Haval", "WEY" 等。 |
| 设备型号 | `model` | String | 设备的具体型号。 |
| 设备语言 | `language` | String | 设备当前设置的系统语言。 |
| 设备唯一标识 | `deviceId` | String | 用于唯一识别一台设备的ID，优先使用ANDROID_ID。 |
| 车架号ID | `vehicleId` | String | 车辆的唯一标识，来源于AVLEngine初始化时的参数ID。 |

#### 3. 数据获取方案 (Android)

##### 3.1 设备品牌 (Brand)

可以通过读取 Android 系统的 `Build` 类来获取。

**代码示例:**
```java
import android.os.Build;

String deviceBrand = Build.BRAND;
```

##### 3.2 设备型号 (Model)

同样通过 `Build` 类获取。

**代码示例:**
```java
import android.os.Build;

String deviceModel = Build.MODEL;
```

##### 3.3 设备语言 (Language)

可以通过 `Locale` 类获取设备当前设置的语言。建议使用 `toLanguageTag()` 方法获取符合 IETF BCP 47 标准的语言标签（例如 "zh-CN"）。

**代码示例:**
```java
import java.util.Locale;

// 获取默认的区域设置
Locale currentLocale = Locale.getDefault(); 
// 获取语言的 BCP 47 标签
String languageTag = currentLocale.toLanguageTag(); 
```

##### 3.4 设备唯一标识 (Unique Device Identifier)

设备唯一标识用于区分不同的设备。在车机场景下，我们优先使用ANDROID_ID作为设备标识。

##### 3.5 车架号ID (Vehicle Identifier)

车架号ID用于标识具体的车辆，来源于AVLEngine初始化时传入的参数ID，该ID与车辆业务紧密相关。

**设备ID方案对比:**

1.  **`ANDROID_ID` (推荐方案)**:
    *   **优点**:
        *   获取简单，无需特殊权限。
        *   在Android 8.0及以上版本，对于同一应用在同一设备上具有稳定性。
        *   在车机场景下，通常不会频繁恢复出厂设置，稳定性较好。
    *   **缺点**:
        *   在 Android 8.0 (API 26) 以下的设备，恢复出厂设置会重置 `ANDROID_ID`。
        *   在某些设备上可能返回 `null` 或空字符串。
    *   **结论**: 在车机场景下，作为设备ID的首选方案，需要做好异常处理。



**车架号ID获取方案:**

车架号ID直接来源于AVLEngine初始化时传入的参数ID，该ID具有以下特点：
*   **业务相关性**: 与车辆本身绑定，具有明确的业务意义。
*   **稳定性**: 车架号通常不会变更，具有很好的稳定性。
*   **集成性**: 与现有AVLEngine初始化流程无缝集成，无需额外获取。

**推荐方案实现:**
```java
import android.content.Context;
import android.provider.Settings;
import android.text.TextUtils;

public class DeviceInfoManager {

    /**
     * 获取设备唯一标识（使用ANDROID_ID）
     * @param context 上下文
     * @return 设备唯一标识，可能为null或空字符串
     */
    public static String getDeviceId(Context context) {
        return getAndroidId(context);
    }

    /**
     * 获取车架号ID（来源于AVLEngine初始化参数）
     * @param vehicleId AVLEngine初始化时传入的车架号ID
     * @return 车架号ID
     */
    public static String getVehicleId(String vehicleId) {
        if (!TextUtils.isEmpty(vehicleId)) {
            return vehicleId;
        }
        throw new IllegalArgumentException("Vehicle ID cannot be null or empty");
    }

    /**
     * 获取ANDROID_ID
     * @param context 上下文
     * @return ANDROID_ID，可能为null或空字符串
     */
    private static String getAndroidId(Context context) {
        try {
            return Settings.Secure.getString(context.getContentResolver(), Settings.Secure.ANDROID_ID);
        } catch (Exception e) {
            return null;
        }
    }
}
```
**使用方式:**

```java
// 获取设备ID（使用ANDROID_ID，可能为null或空字符串）
String deviceId = DeviceInfoManager.getDeviceId(context);

// 获取车架号ID（来源于AVLEngine初始化参数）
String vehicleId = "your_vehicle_id_here"; // AVLEngine.init()方法中传入的ID参数
String vehicleIdentifier = DeviceInfoManager.getVehicleId(vehicleId);

// 数据回传时同时包含设备ID和车架号ID
// deviceId: 用于识别设备（可能为空）
// vehicleIdentifier: 用于识别车辆（作为主要标识）
```

**集成示例:**
```java
public class DataUploadManager {

    public void uploadDeviceInfo(Context context, String vehicleId) {
        // 收集设备信息
        String brand = Build.BRAND;
        String model = Build.MODEL;
        String language = Locale.getDefault().toLanguageTag();
        String deviceId = DeviceInfoManager.getDeviceId(context);
        String vehicleIdentifier = DeviceInfoManager.getVehicleId(vehicleId);

        // 构建上传数据
        JSONObject data = new JSONObject();
        try {
            data.put("brand", brand);
            data.put("model", model);
            data.put("language", language);
            data.put("deviceId", deviceId);
            data.put("vehicleId", vehicleIdentifier);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        // 执行上传逻辑
        uploadToServer(data);
    }
}
```

#### 4. 数据回传时机

采用 **SDK初始化时启动子线程回传** 的方案，确保数据上报不影响主流程。

*   **实现逻辑**:
    1.  SDK在每次初始化时，都会启动一个独立的子线程。
    2.  子线程负责执行完整的数据回传逻辑：
        a. 获取设备唯一标识 (`deviceId`)，使用ANDROID_ID（可能为空）。
        b. 获取AVLEngine初始化时传入的车架号ID (`vehicleId`)。
        c. 收集完整的设备信息（品牌、型号、语言、deviceId、vehicleId）。
        d. 将设备信息异步回传到后端服务器。
    3.  将回传操作放在子线程中，可以避免阻塞SDK的主初始化流程，确保即使在网络不佳或服务器响应慢的情况下，也不会影响应用的启动性能。

*   **优点**:
    *   **不阻塞主线程**: 异步回传，不影响SDK初始化速度和应用性能。
    *   **逻辑解耦**: 回传逻辑与初始化主流程分离，代码更清晰。
    *   **信息更新**: 可以配置策略，定期更新设备信息（如语言变更），而不仅仅是首次。

*   **缺点**:
    *   **实现稍复杂**: 需要处理线程管理。
    *   **潜在的重复上报**: 如果不加控制，每次初始化都上报会增加服务器压力。但可以通过增加“每日一次”等策略来优化。

*   **结论**:
    该方案在确保不影响核心功能性能的前提下，完成了数据回传，且为后续更灵活的上报策略（如定期更新）提供了基础。

#### 5. 回传时序图

```mermaid
sequenceDiagram
    participant App as "App (Main Thread)"
    participant SDK as "车机SDK"
    participant BGThread as "SDK Background Thread"
    participant LocalStorage as "本地存储"
    participant Server as "后端服务器"

    App->>SDK: 调用初始化
    SDK->>BGThread: 启动子线程进行数据回传
    SDK-->>App: 初始化方法立即返回
    note over App: 初始化完成，不被阻塞

    par 数据回传
        BGThread->>BGThread: 获取ANDROID_ID作为deviceId (可能为空)
        BGThread->>BGThread: 获取AVLEngine传入的车架号ID (vehicleId)
        BGThread->>BGThread: 收集设备信息 (品牌, 型号, 语言, deviceId, vehicleId)
        BGThread->>+Server: 异步回传设备信息
        Server-->>-BGThread: 响应成功
    end
```

#### 6. 回传流程图

```mermaid
graph TD
    subgraph Main Thread
        A[开始: SDK 初始化] --> B[启动数据回传子线程];
        B --> C[结束: 初始化完成];
    end

    subgraph Background Thread
        D[开始回传] --> E[获取ANDROID_ID作为deviceId];
        E --> F[获取车架号ID];
        F --> G[收集设备信息<br/>品牌、型号、语言、deviceId、vehicleId];
        G --> H[回传数据到服务器];
        H --> I[结束回传];
    end
```

#### 7. 工期评估 (表格)

| 阶段 | 主要内容 | 预计工时 (人/天) | 备注 |
| :--- | :--- | :--- | :--- |
| **方案设计** | 技术文档撰写与评审 | 1 | 已完成 |
| **功能开发** | SDK端数据采集与上报逻辑实现 | 1 | 核心编码工作 |
| **联调测试** | 与后端进行接口联调 | 1 | 依赖后端接口 |

| **总计** | | **4** | |



#### 8. 工期排期 (甘特图)

```mermaid
gantt
    title 数据回传功能开发排期
    dateFormat  YYYY-MM-DD
    
    section 规划阶段
    方案设计与评审 :done, des1, 2025-06-19, 1d

    section 执行阶段
    功能开发 :dev1, after des1, 0.5d
    联调测试 :test1, after dev1, 0.5d
```

