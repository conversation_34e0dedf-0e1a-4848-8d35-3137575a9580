package com.antiy.avlsdk.utils;

import android.content.Context;
import android.content.res.AssetManager;
import android.os.Build;

import androidx.annotation.RequiresApi;

import com.antiy.avlsdk.AVLEngine;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.ArrayList;
import java.util.List;

public class FileUtil {

    public static boolean isApkFile(File file) {
        if (file == null || !file.exists()) {
            return false;
        }

        try (FileInputStream fis = new FileInputStream(file)) {
            byte[] bytes = new byte[2];
            fis.read(bytes, 0, 2);
            return bytes[0] == 'P' && bytes[1] == 'K'; // "PK" in ASCII
        } catch (IOException e) {
            // Handle exception if necessary
            e.printStackTrace();
            return false;
        }
    }

    public static boolean isForMobile(File file) {
        if (file == null || !file.exists())
            return false;

        try (FileInputStream fis = new FileInputStream(file)) {
            byte[] bytes = new byte[3];
            fis.read(bytes, 0, 3);
            return (bytes[0] == 'P' && bytes[1] == 'K') || (bytes[0] == 'd' && bytes[1] == 'e' && bytes[2] == 'x');
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
    }

    public static boolean isWordFile(File file) {
        return file.getAbsolutePath().endsWith(".doc") || file.getAbsolutePath().endsWith(".docx");
    }

    @RequiresApi(api = Build.VERSION_CODES.O)
    private static String probeContentType(Path path) {
        try {
            return Files.probeContentType(path);
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    public static boolean isMediaFile(File file) {
        return file.getAbsolutePath().endsWith(".mp3");
//        return FileFormatChecker.isMp4File(file.getAbsolutePath()) || file;
//        String mimeType = probeContentType(file.toPath());
//        Log.d("AVL", file.getAbsolutePath() + " " + mimeType);
//        for (String s : mMediaFormat) {
//            if (mimeType != null && mimeType.equalsIgnoreCase(s))
//                return true;
//        }
//
//        return false;
    }

    public static List<File> listAllFiles(File directory) {
        List<File> fileList = new ArrayList<File>();
        listFilesInDirectory(directory, fileList);
        return fileList;
    }

    private static void listFilesInDirectory(File directory, List<File> fileList) {
        if (directory != null && directory.isDirectory()) {
            File[] files = directory.listFiles();
            if (files != null && files.length > 0) {
                for (File file : files) {
                    if (file.isFile()) {
                        fileList.add(file);
                        AVLEngine.Logger.info("find file:" + file.getAbsolutePath());
                    } else if (file.isDirectory()) {
                        listFilesInDirectory(file, fileList);
                    }
                }
            }
        }
    }


    public static String formatFileSize(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.2f KB", bytes / 1024.0);
        } else if (bytes < 1024 * 1024 * 1024) {
            return String.format("%.2f MB", bytes / (1024.0 * 1024));
        } else {
            return String.format("%.2f GB", bytes / (1024.0 * 1024 * 1024));
        }
    }

    public static void copyFile(String sourceFile, String destFile) {
        FileInputStream inStream = null;
        FileOutputStream outStream = null;

        try {
            // 创建文件输入流来读取源文件
            inStream = new FileInputStream(sourceFile);

            // 创建文件输出流来写入目标文件
            // 注意：如果目标文件已存在，它将被覆盖
            outStream = new FileOutputStream(destFile,true);

            // 创建一个缓冲区
            byte[] buffer = new byte[1024];

            // 读取源文件并写入目标文件
            int length;
            while ((length = inStream.read(buffer)) > 0) {
                outStream.write(buffer, 0, length);
            }

            // 刷新输出流，确保所有数据都被写入
            outStream.write("***************************************************************".getBytes());
            outStream.write("\n".getBytes());
            outStream.flush();

        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            // 关闭流以释放资源
            try {
                if (inStream != null) {
                    inStream.close();
                }
                if (outStream != null) {
                    outStream.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 递归删除目录及其所有内容。
     *
     * @param directory 要删除的目录路径
     * @return 删除是否成功
     */
    public static boolean deleteDirectory(File directory) {
        if (directory.exists()) {
            File[] entries = directory.listFiles();
            if (entries != null) {
                for (File entry : entries) {
                    if (entry.isDirectory()) {
                        deleteDirectory(entry); // 递归删除子目录
                    } else {
                        if (!entry.delete()) { // 尝试删除文件
                            return false;
                        }
                    }
                }
            }
        }

        // 最后尝试删除目录本身
        return directory.delete();
    }

    /**
     * 删除文件
     * @param deleteFile
     */
    public static void deleteFile(File deleteFile) {
        if (deleteFile.exists()) {
            if (deleteFile.isDirectory()) {
                // 递归删除目录
                deleteDirectory(deleteFile);
            } else {
                deleteFile.delete(); // 删除文件
            }
        }
    }

    /**
     * 替换文件,把源文件替换目标文件
     * @param sourcePath
     * @param targetPath
     */
    public static boolean replaceFile(String sourcePath,String targetPath){
        File sourceFile = new File(sourcePath);
        if(!sourceFile.exists()){
            return false;
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            try {
                Files.move(Paths.get(sourcePath ),Paths.get(targetPath), StandardCopyOption.REPLACE_EXISTING);
            } catch (IOException e) {
                e.printStackTrace();
                return false;
            }
        }
        return true;
    }


    @RequiresApi(api = Build.VERSION_CODES.KITKAT)
    public static void copyAssetFileToFilesDir(Context context, String assetFileName, String destinationFileName) {
        AssetManager assetManager = context.getAssets();
        File file = new File(destinationFileName);

        try (InputStream is = assetManager.open(assetFileName);
             OutputStream os = new FileOutputStream(file)) {
            byte[] buffer = new byte[1024];
            int read;
            while ((read = is.read(buffer)) != -1) {
                os.write(buffer, 0, read);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 检查文件夹是否包含文件
     * @param folderPath 文件夹路径
     * @return 文件夹是否包含文件(如果文件夹不存在则返回false)
     */
    public static boolean isFolderHasFiles(String folderPath) {
        File folder = new File(folderPath);
        if (!folder.exists() || !folder.isDirectory()) {
            return false;
        }
        File[] files = folder.listFiles();
        return files != null && files.length > 0;
    }
}
