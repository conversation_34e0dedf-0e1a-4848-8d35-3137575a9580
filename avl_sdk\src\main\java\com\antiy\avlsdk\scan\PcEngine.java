package com.antiy.avlsdk.scan;

import android.text.TextUtils;

import com.antiy.avlsdk.AVLEngine;
import com.antiy.avlsdk.BuildConfig;
import com.antiy.avlsdk.entity.ResultScan;
import com.antiy.avlsdk.pc.AVLEnginePC;

/**
 * Author: wangbiao
 * Date: 2024/9/9 11:27
 * Description:
 */
public class PcEngine {
    public static ResultScan scan(String filePath,String sha256) {
        String result = AVLEnginePC.getInstance().scan(filePath);
        AVLEngine.Logger.info("pc engine result：" + result);
        ResultScan resultScan = new ResultScan();
        resultScan.isMalicious = !TextUtils.isEmpty(result);
        resultScan.virusName = TextUtils.isEmpty(result) ? "" : result;

        resultScan.isCloudScan = false;
        if (!BuildConfig.FLAVOR.equals("changan")){
            resultScan.sha256sum = sha256;
        }
        return resultScan;
    }
}
