# VirusDangerChecker 接口说明文档

## 1. 功能概述
`VirusDangerChecker` 是一个病毒风险描述生成器，用于将病毒名称解析并生成标准化的病毒信息对象。该工具可以从病毒名中提取类别和行为特征，并返回包含病毒类别、行为特征列表、风险等级和执行建议的结果。

## 2. 输入格式
病毒名称的标准格式为：`类别/详细信息[行为特征代码1,行为特征代码2,...]`

示例：
- `PornWare/Android.GSexplayer.hm[exp,rog]`
- `Trojan/Android.Spy.hm[prv,rmt,spy]`
- `AdWare/Android.AdDisplay.hm[ads,rog]`

## 3. 返回结果
方法返回 `VirusInfo` 对象，包含：
- `category`: 病毒类别枚举值（VirusCategory）
- `behaviors`: 行为特征枚举列表（List<BehaviorCode>）
- `riskLevel`: 风险等级枚举值（HIGH/MEDIUM/LOW）
- `suggestion`: 执行建议枚举值（CLEAN_NOW/USE_CAUTION）

### 风险等级说明
| 等级 | 描述 |
|------|------|
| HIGH | 高风险 |
| MEDIUM | 中风险 |
| LOW | 低风险 |

### 执行建议说明
| 建议 | 描述 |
|------|------|
| CLEAN_NOW | 建议立即清理 |
| USE_CAUTION | 建议谨慎使用 |

## 4. 支持的病毒类别
### 4.1 病毒类别枚举 (VirusCategory)
| 类别 | 值 | 说明 |
|------|-----|------|
| TROJAN | "Trojan" | 木马程序 |
| G_WARE | "G-Ware" | 流氓软件 |
| TOOL | "Tool" | 黑客工具 |
| RISK_WARE | "RiskWare" | 风险应用 |
| PORN_WARE | "PornWare" | 色情软件 |
| AD_WARE | "AdWare" | 广告软件 |
| PAY_WARE | "PayWare" | 付费插件 |
| WORM | "Worm" | 蠕虫病毒 |
| WARN | "Warn" | 警告 |

### 4.2 默认配置说明
| 类别 | 默认风险等级 | 默认执行建议 |
|------|--------------|--------------|
| TROJAN | 高 | CLEAN_NOW |
| G_WARE | 中 | CLEAN_NOW |
| TOOL | 中 | USE_CAUTION |
| RISK_WARE | 低 | USE_CAUTION |
| PORN_WARE | 中 | CLEAN_NOW |
| AD_WARE | 低 | USE_CAUTION |
| PAY_WARE | 低 | USE_CAUTION |
| WORM | 高 | CLEAN_NOW |
| WARN | 低 | USE_CAUTION |

## 5. 支持的行为特征
### 5.1 行为特征枚举 (BehaviorCode)
| 代码 | 值 | 说明 |
|------|-----|------|
| PRV | "prv" | 窃取隐私信息 |
| RMT | "rmt" | 接收远程指令 |
| PAY | "pay" | 恶意扣费 |
| SPR | "spr" | 自动扩散恶意代码 |
| EXP | "exp" | 消耗流量或发送短信 |
| SYS | "sys" | 破坏系统 |
| FRA | "fra" | 伪装或透明欺诈 |
| ROG | "rog" | 恶意占用系统资源或弹出广告 |
| ADS | "ads" | 推送广告 |
| SMS | "sms" | 频繁发送短信 |
| SPY | "spy" | 间谍或窃取大量隐私信息 |
| BKD | "bkd" | 后门风险 |
| RTT | "rtt" | 私自提权 |
| LCK | "lck" | 锁屏勒索 |

## 6. API 说明

### 6.1 初始化
```java
public static void init(Context context)
```

#### 说明
在应用启动时调用，用于加载默认配置。

#### 示例
```java
// 在 Application 或首个 Activity 的 onCreate 中初始化
VirusDangerChecker.init(context);
```

### 6.2 解析病毒名称
```java
public static VirusInfo parseVirusName(String virusName)
```

#### 参数
- `virusName`: 病毒名称字符串，格式如上述"输入格式"所示

#### 返回值
返回 `VirusInfo` 对象，包含以下属性：
- `category`: 病毒类别（VirusCategory 枚举值）
- `behaviors`: 行为特征列表（List<BehaviorCode>）
- `riskLevel`: 风险等级（RiskLevel 枚举值）
- `suggestion`: 执行建议（ActionSuggestion 枚举值）

#### 示例
```java
// 解析病毒名称
String virusName = "Trojan/Android.Spy.hm[prv,rmt,spy]";
VirusInfo info = VirusDangerChecker.parseVirusName(virusName);

// 获取病毒类别
VirusCategory category = info.getCategory();  // TROJAN

// 获取行为特征列表
List<BehaviorCode> behaviors = info.getBehaviors();  // [PRV, RMT, SPY]

// 获取风险等级
RiskLevel riskLevel = info.getRiskLevel();  // HIGH

// 获取执行建议
ActionSuggestion suggestion = info.getSuggestion();  // CLEAN_NOW
```

## 7. 注意事项
1. 病毒名称大小写敏感
2. 行为特征代码之间使用英文逗号分隔
3. 未知的病毒类别将返回 `null` 作为类别值
4. 未知的行为特征代码将被忽略
5. 空输入或 null 输入将返回包含默认值的 VirusInfo 对象：
   - category: null
   - behaviors: 空列表
   - riskLevel: LOW
   - suggestion: USE_CAUTION
6. 返回的行为特征列表是不可修改的（unmodifiable）
7. 所有枚举值都不会返回 null

## 8. 错误处理
1. 输入为 null 或空字符串时会返回默认的 VirusInfo 对象
2. 无效的病毒类别会导致 category 为 null
3. 无效的行为特征代码会被忽略
4. 所有的 getter 方法都不会返回 null（行为特征列表在没有数据时返回空列表）

## 9. 线程安全性
1. VirusInfo 类是不可变的（immutable），天然线程安全
2. 所有返回的集合都是不可修改的（unmodifiable）
3. 所有的枚举类型都是线程安全的
4. parseVirusName 方法是线程安全的




