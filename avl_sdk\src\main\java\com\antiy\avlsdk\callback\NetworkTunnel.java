package com.antiy.avlsdk.callback;

import com.antiy.avlsdk.entity.RequestMethod;

/**
 * 2 * Copyright (C), 2020-2024
 * 3 * FileName: NetworkTunnel
 * 4 * Author: wangbiao
 * 5 * Date: 2024/9/2 10:14
 * 6 * Description: 网络传输通道接口
 * 10
 */
public interface NetworkTunnel {
    /**
     * 网络是否可用
     * @return true：可用，false：不可用
     */
    boolean isAvailable();

    /**
     * 请求转发方法，通过RequestCallback回调返回给调用方
     * @param uri 目标uri，不包含scheme://hostname部分
     * @param method 枚举类，可用的http方法
     * @param param 请求参数
     * @param callback 请求回调
     */
    void request(
            String uri,
            RequestMethod method,
            Object param,
            RequestCallback callback);

    /**
     * 文件下载转发方法，下载完成通过DownloadCallback回调返回给调用方
     * @param uri 目标下载路径，不包含scheme://hostname部分
     * @param callback 下载回调
     */
    void download(String uri, DownloadCallback callback);

    /**
     * 取消请求的方法
     */
    default void cancelRequest() {}
}
