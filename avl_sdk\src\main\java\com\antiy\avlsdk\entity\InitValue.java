package com.antiy.avlsdk.entity;

public enum InitValue {
    SUCCESS(0, "初始化成功"),
    UNKNOWN_ERROR(-1, "其它失败"),
    DB_VERIFY_FAILED(-2, "病毒库校验失败"),
    TOKEN_DECRYPT_FAILED(-3, "解密授权凭证失败"),
    AUTH_FAILED(-4, "鉴权失败"),
    ALREADY_INITIALIZED(-5, "重复初始化");

    private final int code;
    private final String description;

    InitValue(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static InitValue fromCode(int code) {
        for (InitValue result : InitValue.values()) {
            if (result.getCode() == code) {
                return result;
            }
        }
        return UNKNOWN_ERROR;
    }
} 