import csv
import re
import os
import codecs
import argparse

def process_csv(input_file, output_file, custom_header=None):
    """
    处理CSV文件，确保病毒名称保持完整不被逗号分割
    
    参数:
        input_file (str): 输入CSV文件路径
        output_file (str): 输出CSV文件路径
        custom_header (list): 自定义表头，如果提供则替换原表头
    """
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"错误: 输入文件 '{input_file}' 不存在")
        return False
    
    # 尝试不同的编码方式读取文件
    encodings = ['utf-8', 'gbk', 'gb2312', 'latin-1']
    file_content = None
    
    for encoding in encodings:
        try:
            with codecs.open(input_file, 'r', encoding=encoding, errors='replace') as f:
                # 读取文件内容
                file_content = f.read()
                # 移除BOM标记（如果有）
                if file_content.startswith('\ufeff'):
                    file_content = file_content[1:]
                
                print(f"成功使用 {encoding} 编码读取文件")
                break
        except Exception as e:
            print(f"尝试使用 {encoding} 编码读取失败: {str(e)}")
    
    if file_content is None:
        print("错误: 无法读取CSV文件，请检查文件格式")
        return False
    
    # 处理数据 - 特殊处理方括号内的逗号
    # 先替换方括号内的逗号为特殊标记
    pattern = r'\[(.*?)\]'
    
    def replace_commas(match):
        return '[' + match.group(1).replace(',', '###COMMA###') + ']'
    
    processed_content = re.sub(pattern, replace_commas, file_content)
    
    # 使用csv模块解析处理后的内容
    rows = []
    try:
        csv_reader = csv.reader(processed_content.splitlines())
        rows = list(csv_reader)
    except Exception as e:
        print(f"解析CSV内容时出错: {str(e)}")
        return False
    
    # 处理每一行数据
    processed_rows = []
    
    # 如果提供了自定义表头，使用它
    if custom_header:
        processed_rows.append(custom_header)
        start_row = 0  # 从第一行开始处理（跳过原表头）
    else:
        # 使用默认表头
        processed_rows.append(["HASH", "病毒名称"])
        start_row = 1  # 从第二行开始处理（跳过原表头）
    
    # 处理数据行
    for row in rows[start_row:]:
        if not row:  # 跳过空行
            continue
            
        if len(row) >= 1:  # 至少有HASH值
            # 第一列是HASH
            hash_value = row[0].strip()
            
            # 合并第二列及之后的所有非空列作为病毒名
            virus_parts = []
            for i in range(1, len(row)):
                if row[i] and not re.match(r'^[\s]*$', row[i]):
                    virus_parts.append(row[i].strip())
            
            # 将病毒名称部分合并成一个完整的字符串
            virus_name = " ".join(virus_parts) if virus_parts else ""
            
            # 还原特殊标记为逗号
            virus_name = virus_name.replace('###COMMA###', ',')
            
            # 创建新行，只包含HASH和完整的病毒名
            processed_rows.append([hash_value, virus_name])
    
    # 创建输出目录（如果不存在）
    output_dir = os.path.dirname(output_file)
    if output_dir and not os.path.exists(output_dir):
        try:
            os.makedirs(output_dir)
            print(f"创建输出目录: {output_dir}")
        except Exception as e:
            print(f"创建输出目录时出错: {str(e)}")
            return False
    
    # 写入输出文件
    try:
        with open(output_file, 'w', encoding='utf-8', newline='') as f:
            csv_writer = csv.writer(f)
            for row in processed_rows:
                csv_writer.writerow(row)
        
        print(f"处理完成! 结果已保存到 '{output_file}'")
        
        # 显示处理结果的前几行
        print("\n处理结果预览:")
        for i, row in enumerate(processed_rows[:min(6, len(processed_rows))]):
            if i == 0:
                print(f"表头: {row}")
            else:
                print(f"行 {i}: HASH={row[0]}, 病毒名={row[1]}")
        
        # 统计信息
        print(f"\n总共处理了 {len(processed_rows) - 1} 行数据")
        
        return True
    except Exception as e:
        print(f"写入输出文件时出错: {str(e)}")
        return False

def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='处理CSV文件，确保病毒名称保持完整不被逗号分割')
    parser.add_argument('-i', '--input', default='excel/scan_results.csv',
                        help='输入CSV文件路径 (默认: excel/scan_results.csv)')
    parser.add_argument('-o', '--output', default='excel/scan_results_processed.csv',
                        help='输出CSV文件路径 (默认: excel/scan_results_processed.csv)')
    parser.add_argument('--header', nargs='+', 
                        help='自定义表头 (例如: --header HASH 病毒名称)')
    
    args = parser.parse_args()
    
    input_file = args.input
    output_file = args.output
    custom_header = args.header
    
    print(f"开始处理文件: {input_file}")
    print(f"输出文件将保存为: {output_file}")
    if custom_header:
        print(f"使用自定义表头: {custom_header}")
    
    # 处理CSV文件
    process_csv(input_file, output_file, custom_header)

if __name__ == "__main__":
    main() 