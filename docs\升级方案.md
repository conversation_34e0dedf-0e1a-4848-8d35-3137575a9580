# SDK 升级优化技术方案

## 1. 引言

### 1.1 背景

当前 `avl_sdk` 作为核心安全能力组件，在现有业务中扮演着重要角色。随着业务发展和技术演进，为了提高 SDK 的可维护性、可扩展性、健壮性、性能以及开发者体验，需要对其进行一次全面的结构优化和技术升级。

### 1.2 目标

本次升级旨在达成以下目标：

*   **提升架构合理性:** 引入清洁架构（Clean Architecture），实现更好的分层和解耦。
*   **增强模块化:** 对核心功能（如激活、配置、扫描、更新、监控、存储）进行更清晰的模块划分。
*   **提高可测试性:** 使核心逻辑更易于进行单元测试和集成测试。
*   **改善代码质量:** 统一编码规范，提升代码可读性和健壮性。
*   **优化性能与资源消耗:** 审视关键路径，优化算法和资源使用。
*   **规范化 API:** 提供更清晰、稳定、易用的对外接口。
*   **技术栈现代化:** 评估并引入有助于提升开发效率和代码质量的新技术（如考虑 Kotlin）。

## 2. 现状分析

### 2.1 现有架构概述

根据 `docs` 目录下的设计文档（`概要设计.puml`, `详细设计.puml` 及各模块 Puml 图）和 `avl_sdk` 源码结构，当前 SDK 主要包含以下模块：

*   **激活模块 (Activation):** 处理 SDK 的授权和激活流程。
*   **配置模块 (Configuration):** 管理 SDK 的各项配置参数。
*   **扫描模块 (Scanning):** 核心功能，执行文件或内存扫描。
*   **病毒库更新模块 (Update):** 负责病毒定义库的下载和更新。
*   **性能监控模块 (Monitoring):** (`avl_monitor` 可能关联) 监控 SDK 运行性能指标。
*   **存储模块 (Storage):** 负责本地数据的持久化（如配置、病毒库信息、日志）。
*   **核心引擎:** (可能包含 C/C++ 代码) 底层扫描和分析能力。
*   **对外接口层:** 提供给宿主 App 调用的 API。

### 2.2 优势

*   功能完整，满足基本业务需求。
*   包含一定的设计文档基础。

### 2.3 待改进点

*   **架构耦合:** 不同模块之间可能存在较强的耦合，修改和扩展困难。
*   **职责不清:** 部分类的职责可能过于庞杂。
*   **可测试性不足:** 核心业务逻辑可能难以独立进行单元测试。
*   **API 设计:** 对外接口可能不够统一或易用性有待提升。
*   **错误处理:** 错误处理机制可能不够完善和一致。
*   **异步处理:** 异步任务（如网络请求、文件 IO、扫描）的管理可能可以优化。
*   **技术栈:** 主要使用 Java，未能利用 Kotlin 等现代语言特性提升开发效率和代码健壮性。

## 3. 目标架构设计

采用**清洁架构 (Clean Architecture)** 思想，将 SDK 划分为以下几个主要层次：

```puml
@startuml
package "SDK Interface / Presentation Layer" {
  [SDK API Facade] <<interface>>
  [Data Transfer Objects (DTOs)]
}

package "Domain Layer" {
  [Use Cases / Interactors]
  [Domain Entities / Models]
  [Repository Interfaces] <<interface>>
}

package "Data Layer" {
  [Repository Implementations]
  [Data Sources (Remote, Local)] <<interface>>
  [Network Client]
  [Database/File Storage]
  [Engine Wrapper (JNI/Native)]
}

[SDK API Facade] --> [Use Cases / Interactors]
[Use Cases / Interactors] --> [Repository Interfaces]
[Use Cases / Interactors] --> [Domain Entities / Models]
[Repository Implementations] ..> [Repository Interfaces] : implements
[Repository Implementations] --> [Data Sources (Remote, Local)]
[Data Sources (Remote, Local)] --> [Network Client]
[Data Sources (Remote, Local)] --> [Database/File Storage]
[Data Sources (Remote, Local)] --> [Engine Wrapper (JNI/Native)]

note right of [SDK API Facade]
  对外提供统一、稳定的接口
  处理输入校验和数据转换 (DTO <-> Domain)
end note

note right of [Use Cases / Interactors]
  封装核心业务逻辑
  协调 Repository 完成具体任务
  不依赖具体实现
end note

note right of [Repository Interfaces]
  定义数据访问契约
end note

note right of [Repository Implementations]
  实现数据访问逻辑
  聚合、管理多个 Data Source
  处理数据缓存策略
end note

note right of [Data Sources (Remote, Local)]
  负责具体的数据获取/存储
  (网络、数据库、文件、JNI 调用)
end note
@enduml
```

**建议的 SDK 模块内部项目结构 (以 `scanning` 模块为例):**

```
avl_sdk/
  src/main/
    java/  # 或 kotlin/
      com/example/avlsdk/
        # --- SDK Interface Layer --- (对外 API)
        AvlSdkApi.kt # SDK 统一入口 Facade
        dto/
          ScanRequestDto.kt
          ScanResultDto.kt

        # --- Feature Modules --- (按功能划分)
        scanning/
          # --- Domain Layer ---
          domain/
            model/ # 领域实体
              ScanTarget.kt
              ScanViolation.kt
            repository/ # 仓库接口
              ScanRepository.kt
            usecase/ # 业务用例
              StartScanUseCase.kt
              GetScanStatusUseCase.kt

          # --- Data Layer ---
          data/
            repository/ # 仓库实现
              ScanRepositoryImpl.kt
            datasource/
              local/ # 本地数据源 (DB, File, JNI)
                ScanEngineDataSource.kt # JNI 引擎封装
                ScanHistoryDataSource.kt # 扫描历史 (DB/File)
              remote/ # 远程数据源 (可选, 如云查)
                CloudScanDataSource.kt
            mapper/ # 数据模型转换
              ScanResultMapper.kt
            model/ # 数据层模型 (网络 DTO, DB Entity)
              EngineScanResult.kt

          # --- Dependency Injection ---
          di/
            ScanningModule.kt # Hilt Module

        activation/
          # (类似结构: domain/, data/, di/)
        configuration/
          # (类似结构)
        update/
          # (类似结构)
        # ... 其他模块 ...

        # --- Core Infrastructure --- (共享基础组件)
        core/
          network/
            ApiClient.kt
          database/
            AppDatabase.kt
          error/
            SdkError.kt
            Result.kt # 通用结果封装
          di/
            CoreModule.kt
          util/
            # ... 工具类 ...
```

*   **SDK Interface / Presentation Layer:**
    *   **职责:** 定义和暴露 SDK 的公共 API。这是宿主 App 与 SDK 交互的唯一入口。
    *   **组件:** `AvlSdkApi.kt` (提供统一接口 Facade)、`dto/` (用于 API 数据传输)。
    *   **原则:** 保持 API 稳定、简洁、易用。隐藏内部实现细节。处理与宿主 App 的数据格式转换。
*   **Domain Layer:**
    *   **职责:** 包含核心业务规则和逻辑，独立于具体的技术实现。
    *   **组件:** `usecase/` (封装具体业务流程)、`model/` (核心业务对象)、`repository/` (定义数据访问的契约接口)。
    *   **原则:** 不依赖任何外层（Data, Presentation）。只包含纯粹的业务逻辑。使用 Kotlin 编写。
*   **Data Layer:**
    *   **职责:** 负责所有数据的获取、存储和管理。
    *   **组件:** `repository/` (实现 Domain 层定义的接口)、`datasource/` (区分远程 Remote 和本地 Local)、`mapper/` (数据模型转换)、`model/` (数据层特定模型)。
    *   **原则:** 实现 Domain 层接口，处理数据映射 (Data Entity <-> Domain Entity)，管理数据来源（网络、缓存、本地、JNI）。可以使用 Kotlin 或 Java。

**模块化:** 在此分层基础上，按功能（`scanning`, `activation`, `configuration`, `update`, etc.）组织代码，每个功能模块内部再遵循 Domain-Data 的结构。共享的基础组件放入 `core` 包。

## 4. 技术选型与策略

### 4.1 依赖注入 (Dependency Injection)

*   **目的:** 解耦组件，方便替换和测试。
*   **方案:**
    *   **如果维持 Java:** Dagger 2 (功能强大但学习曲线陡峭) 或 Hilt (基于 Dagger，更易用，推荐用于 Android 项目)。如果项目结构简单，也可考虑手动注入。
    *   **如果迁移 Kotlin:** Hilt 是首选。Koin 也是一个流行的选项（更轻量，纯 Kotlin）。
*   **建议:** 引入 Hilt (即使暂时维持 Java，Hilt 也支持 Java)。

**示例 (Hilt Module - Kotlin):**

```kotlin
package com.example.avlsdk.scanning.di

import com.example.avlsdk.scanning.data.datasource.local.ScanEngineDataSource
import com.example.avlsdk.scanning.data.repository.ScanRepositoryImpl
import com.example.avlsdk.scanning.domain.repository.ScanRepository
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class) // 或者更细粒度的 Component
abstract class ScanningModule {

    @Binds
    @Singleton
    abstract fun bindScanRepository(impl: ScanRepositoryImpl): ScanRepository

    // 如果 ScanEngineDataSource 不是接口，用 @Provides
    // companion object {
    //     @Provides
    //     @Singleton
    //     fun provideScanEngineDataSource(): ScanEngineDataSource {
    //         // return ScanEngineDataSource(...) // 如何实例化？
    //     }
    // }
}
```

### 4.2 异步处理

*   **目的:** 高效、可靠地处理后台任务（网络、IO、扫描）。
*   **方案:**
    *   **如果维持 Java:** `java.util.concurrent` (Executors, Future) 结合回调。可以考虑引入 RxJava 2/3 来简化复杂的异步流。
    *   **如果迁移 Kotlin:** **强烈建议使用 Kotlin Coroutines**。它能以同步的方式编写异步代码，极大提高可读性和可维护性，并提供结构化并发管理。
*   **建议:** 若决定迁移 Kotlin，则全面拥抱 Coroutines。若维持 Java，则标准化 `Executors` 的使用，并考虑是否引入 RxJava 简化回调地狱。

**示例 (Coroutine in UseCase and Repository - Kotlin):**

```kotlin
package com.example.avlsdk.scanning.domain.usecase

import com.example.avlsdk.core.error.Result
import com.example.avlsdk.scanning.domain.model.ScanTarget
import com.example.avlsdk.scanning.domain.model.ScanViolation
import com.example.avlsdk.scanning.domain.repository.ScanRepository
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject

class StartScanUseCase @Inject constructor(
    private val scanRepository: ScanRepository,
    private val ioDispatcher: CoroutineDispatcher = Dispatchers.IO // 通过 DI 注入 Dispatcher
) {
    suspend operator fun invoke(target: ScanTarget): Result<List<ScanViolation>> {
        return withContext(ioDispatcher) { // 切换到 IO 线程执行
            scanRepository.startScan(target)
        }
    }
}

package com.example.avlsdk.scanning.domain.repository

import com.example.avlsdk.core.error.Result
import com.example.avlsdk.scanning.domain.model.ScanTarget
import com.example.avlsdk.scanning.domain.model.ScanViolation

interface ScanRepository {
    suspend fun startScan(target: ScanTarget): Result<List<ScanViolation>>
    // 其他扫描相关方法...
}

package com.example.avlsdk.scanning.data.repository

import com.example.avlsdk.core.error.Result
import com.example.avlsdk.scanning.data.datasource.local.ScanEngineDataSource
import com.example.avlsdk.scanning.domain.model.ScanTarget
import com.example.avlsdk.scanning.domain.model.ScanViolation
import com.example.avlsdk.scanning.domain.repository.ScanRepository
import javax.inject.Inject

class ScanRepositoryImpl @Inject constructor(
    private val engineDataSource: ScanEngineDataSource
    // private val historyDataSource: ScanHistoryDataSource // 可能需要其他数据源
) : ScanRepository {

    override suspend fun startScan(target: ScanTarget): Result<List<ScanViolation>> {
        return try {
            // 1. 调用底层引擎数据源执行扫描
            val engineResult = engineDataSource.performScan(target.path) // 假设 JNI 调用是阻塞的或内部异步

            // 2. 处理引擎结果，映射到 Domain Model
            if (engineResult.isSuccess) {
                val violations = engineResult.violations.map { ScanViolation(it.filePath, it.virusName) } // 示例映射
                // 3. (可选) 记录扫描历史
                // historyDataSource.saveScanResult(target, violations)
                Result.Success(violations)
            } else {
                Result.Error(SdkError.ScanFailed(engineResult.errorCode))
            }
        } catch (e: Exception) {
            // 处理 JNI 调用异常或其他异常
            Result.Error(SdkError.UnexpectedError(e))
        }
    }
}
```

### 4.3 错误处理

*   **目的:** 提供统一、明确的错误反馈机制。
*   **方案:**
    *   定义统一的 `Result` 或 `Either` 类型（可以使用 Kotlin 的 `Result` 或自定义 Java 泛型类）来封装成功或失败的结果，取代抛出大量受检异常或返回 null/错误码。
    *   在 Use Case 和 Repository 接口中明确返回此 `Result` 类型。
    *   API Facade 将内部错误转换为对宿主 App 友好的错误码或消息。
*   **建议:** 设计一套基于 `Result` 模式的错误处理机制。

**示例 (Result and SdkError - Kotlin):**

```kotlin
package com.example.avlsdk.core.error

sealed class Result<out T> {
    data class Success<out T>(val data: T) : Result<T>()
    data class Error(val error: SdkError) : Result<Nothing>()
}

sealed class SdkError(val message: String? = null, val cause: Throwable? = null) {
    // 通用错误
    object NetworkError : SdkError(\"Network connection failed\")
    data class DatabaseError(override val cause: Throwable?) : SdkError(\"Database operation failed\", cause)
    data class UnexpectedError(override val cause: Throwable?) : SdkError(\"An unexpected error occurred\", cause)

    // 激活模块错误
    object ActivationFailed : SdkError(\"Activation failed\")
    object LicenseExpired : SdkError(\"License has expired\")

    // 扫描模块错误
    data class ScanFailed(val code: Int) : SdkError(\"Scan operation failed with code: $code\")
    object ScanEngineInitFailed : SdkError(\"Scan engine initialization failed\")

    // 更新模块错误
    object UpdateCheckFailed : SdkError(\"Failed to check for updates\")
    object UpdateDownloadFailed : SdkError(\"Failed to download update\")
    object UpdateApplyFailed : SdkError(\"Failed to apply update\")

    // 配置模块错误
    object ConfigLoadFailed : SdkError(\"Failed to load configuration\")
    object ConfigSaveFailed : SdkError(\"Failed to save configuration\")
}
```

### 4.4 Java -> Kotlin 迁移考量

*   **优势 (Kotlin):**
    *   **空安全 (Null Safety):** 大幅减少 `NullPointerException`。
    *   **协程 (Coroutines):** 简化异步编程。
    *   **代码简洁:** 语法糖、数据类、扩展函数等减少样板代码。
    *   **函数式编程:** 支持高阶函数、Lambda。
    *   **互操作性:** 与 Java 100% 兼容。
*   **成本与风险:**
    *   **学习曲线:** 团队成员需要熟悉 Kotlin。
    *   **迁移工作量:** 将现有 Java 代码转换为 Idiomatic Kotlin 需要时间。
    *   **混合代码:** 可能导致构建时间增加，需要管理两种语言的规范。
*   **迁移策略建议:**
    *   **策略 A: 全面迁移:** 一次性将所有 Java 代码转为 Kotlin。**风险高，工作量大，不推荐。**
    *   **策略 B: 渐进式迁移 (推荐):**
        1.  **新功能/模块使用 Kotlin 编写。**
        2.  **重构旧模块时，将其迁移到 Kotlin。**
        3.  **优先迁移与异步、数据处理相关的模块，以最大化 Coroutines 和数据类的优势。**
        4.  配置好 Java 与 Kotlin 的互操作。
    *   **策略 C: 维持 Java，应用现代 Java 实践:** 使用 Java 8+ 特性 (Streams, Optional)，引入 Dagger/Hilt，遵循 Clean Architecture，但不进行语言迁移。**成本最低，但无法享受 Kotlin 的诸多优势。**

*   **结论:** **推荐采用渐进式迁移 (策略 B)**。在升级过程中，新代码和被重构的代码使用 Kotlin，逐步提高 Kotlin 代码比例。这样可以在控制风险和工作量的同时，逐步享受 Kotlin 带来的好处。

## 5. 详细升级步骤

### 5.1 阶段一：准备与基础建设

1.  **环境配置:**
    *   升级 Gradle 及相关插件到最新稳定版本。
    *   配置支持 Kotlin（如果决定迁移）。
    *   引入并配置 Linter (如 Checkstyle for Java, Ktlint for Kotlin) 和静态分析工具 (如 SonarQube)。
    *   统一代码格式化工具。
2.  **引入依赖注入:**
    *   选择 DI 框架 (推荐 Hilt)。
    *   在项目中集成 DI 框架，并为后续模块改造做准备。
3.  **基础库与工具类:**
    *   梳理现有的工具类，移除冗余，优化实现。
    *   引入或标准化日志库 (如 Timber)。
    *   定义基础的 `Result` 类型用于错误处理。

### 5.2 阶段二：核心模块按层重构 (逐个模块进行 - 细化)

**以 `Scanning` 模块为例，详细步骤：**

1.  **创建模块结构:** 在 `com.example.avlsdk` 下创建 `scanning` 包，并在其中创建 `domain`, `data`, `di` 子包。
2.  **识别 Domain 实体:** 分析现有扫描逻辑，识别核心概念如 `ScanTarget` (扫描目标，如文件路径、应用包名), `ScanResult` (原始扫描结果), `ScanViolation` (发现的风险项)。在 `scanning/domain/model/` 中用 Kotlin Data Class 定义它们。
3.  **定义 Repository 接口:** 在 `scanning/domain/repository/` 中创建 `ScanRepository.kt` 接口，定义核心操作，如 `suspend fun startScan(target: ScanTarget): Result<List<ScanViolation>>`，`suspend fun getScanStatus(scanId: String): Result<ScanProgress>` 等。接口方法使用 `suspend` 关键字，并返回 `Result` 类型。
4.  **定义 Use Cases:** 在 `scanning/domain/usecase/` 中为每个主要业务场景创建 UseCase 类，如 `StartScanUseCase.kt`, `GetScanStatusUseCase.kt`。UseCase 构造函数接收 `ScanRepository` 接口作为依赖。其 `invoke` 或执行方法调用 Repository 接口完成任务，并处理业务逻辑编排。
5.  **创建 Data Source 接口与实现:**
    *   **本地引擎数据源:** 在 `scanning/data/datasource/local/` 创建 `ScanEngineDataSource.kt`。封装对现有 JNI 接口的调用。如果 JNI 调用是阻塞的，在 DataSource 的方法中可以通过 `withContext(Dispatchers.IO)` 切换线程，但接口本身仍声明为 `suspend`。
    *   **本地历史数据源 (如果需要):** 创建 `ScanHistoryDataSource.kt`，封装对数据库或文件的读写操作。
    *   **远程数据源 (如果需要):** 创建 `CloudScanDataSource.kt`，封装网络请求。
6.  **实现 Repository:** 在 `scanning/data/repository/` 创建 `ScanRepositoryImpl.kt`，实现 `ScanRepository` 接口。构造函数注入所需的 DataSources (`ScanEngineDataSource`, etc.)。
    *   在 `startScan` 实现中，调用 `ScanEngineDataSource` 的方法执行扫描。
    *   处理 DataSource 返回的数据/错误，进行必要的映射 (Data Model -> Domain Model)。
    *   如果需要，调用其他 DataSource (如保存历史记录)。
    *   使用 `try-catch` 结合 `Result.Error` 来封装异常。
7.  **实现数据映射 (Mapper):** 如果 Data 层模型 (如 JNI 返回结构) 和 Domain 层模型差异较大，可以在 `scanning/data/mapper/` 创建 Mapper 类来负责转换。
8.  **配置依赖注入:** 在 `scanning/di/ScanningModule.kt` 中使用 Hilt 提供 `ScanRepository` 的实现绑定 (`@Binds`) 和 DataSources 的实例提供 (`@Provides` 或 `@Binds` 如果有接口)。
9.  **编写单元测试:**
    *   **Domain Layer:** 使用 Mockito-Kotlin 或 Mockk 测试 UseCases，模拟 `ScanRepository` 的行为，验证业务逻辑。
    *   **Data Layer:** 测试 `ScanRepositoryImpl`，模拟 DataSources 的行为，验证数据整合和映射逻辑。可以测试 DataSources，模拟 JNI、网络或数据库的响应。
10. **替换旧实现:** 逐步将旧的扫描相关代码替换为调用新的 Use Case。

**对其他模块 (Activation, Configuration, Update, etc.) 重复以上 1-10 步骤。**

### 5.3 阶段三：SDK 对外 API 重构 (细化)

1.  **设计 `AvlSdkApi.kt` Facade:**
    *   创建 `AvlSdkApi.kt` 类或接口。
    *   构造函数注入所有模块的顶层 UseCases (e.g., `StartScanUseCase`, `ActivateUseCase`, `GetConfigUseCase`, `CheckUpdateUseCase`)。
    *   为每个对外暴露的功能提供一个公共方法，如 `fun startScan(request: ScanRequestDto, callback: ScanCallback)`。
2.  **定义 DTOs:** 在 `dto/` 目录下为 API 的输入和输出创建 Kotlin Data Class (`ScanRequestDto`, `ScanResultDto`, `ActivationStatusDto`, etc.)。
3.  **实现 Facade 方法:**
    *   在 Facade 方法内部:
        *   校验输入 `request` DTO。
        *   将 DTO 转换为 Domain Model (如果需要)。
        *   启动一个 Coroutine Scope (如 `GlobalScope.launch` 或与 SDK 生命周期绑定的 Scope)。
        *   在 Coroutine 内调用相应的 `UseCase`。
        *   处理 `UseCase` 返回的 `Result`:
            *   **Success:** 将 Domain Model 转换为 DTO，通过 `callback` (或返回 `Flow`/`LiveData`) 返回给调用者。
            *   **Error:** 将 `SdkError` 转换为对外的错误码或错误 DTO，通过 `callback` 返回。
        *   确保回调在正确的线程执行 (通常是主线程，使用 `withContext(Dispatchers.Main)` 切换)。
4.  **提供初始化方法:** 提供一个静态方法或 Hilt 入口点来获取 `AvlSdkApi` 的实例。

**示例 (API Facade - Kotlin):**

```kotlin
package com.example.avlsdk

import android.content.Context
import com.example.avlsdk.core.error.Result
import com.example.avlsdk.core.error.SdkError
import com.example.avlsdk.dto.ScanRequestDto
import com.example.avlsdk.dto.ScanResultDto
import com.example.avlsdk.scanning.domain.model.ScanTarget
import com.example.avlsdk.scanning.domain.usecase.StartScanUseCase
import dagger.hilt.EntryPoint
import dagger.hilt.InstallIn
import dagger.hilt.android.EntryPointAccessors
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.*
import javax.inject.Inject
import javax.inject.Singleton

// 定义回调接口
interface ScanCallback {
    fun onScanStarted()
    fun onProgress(progress: Int)
    fun onViolationFound(result: ScanResultDto)
    fun onScanCompleted(violations: List<ScanResultDto>)
    fun onError(errorCode: Int, message: String?)
}

@Singleton // 使 Facade 成为单例
class AvlSdkApi @Inject internal constructor(
    private val startScanUseCase: StartScanUseCase
    // 注入其他 UseCases...
    // private val activateUseCase: ActivateUseCase,
    // private val getConfigUseCase: GetConfigUseCase,
    private val mainDispatcher: CoroutineDispatcher = Dispatchers.Main,
    private val sdkScope: CoroutineScope = GlobalScope // 或者提供一个可管理的 Scope
) {

    fun startScan(request: ScanRequestDto, callback: ScanCallback) {
        callback.onScanStarted() // 立即回调开始

        sdkScope.launch {
            // DTO -> Domain Model 转换 (示例)
            val scanTarget = ScanTarget(request.path)

            when (val result = startScanUseCase(scanTarget)) {
                is Result.Success -> {
                    // Domain Model -> DTO 转换
                    val resultDtos = result.data.map { ScanResultDto(it.filePath, it.virusName) }
                    withContext(mainDispatcher) {
                        callback.onScanCompleted(resultDtos)
                    }
                }
                is Result.Error -> {
                    val (code, msg) = mapErrorToPublic(result.error)
                    withContext(mainDispatcher) {
                        callback.onError(code, msg)
                    }
                }
            }
        }
    }

    // 其他 API 方法，如 activate, getConfig, checkForUpdate...

    private fun mapErrorToPublic(error: SdkError): Pair<Int, String?> {
        // 将内部 SdkError 映射为对外的错误码和消息
        return when (error) {
            is SdkError.ScanFailed -> Pair(1001, \"扫描失败: ${error.code}\")
            is SdkError.NetworkError -> Pair(2001, \"网络错误\")
            is SdkError.ActivationFailed -> Pair(3001, \"激活失败\")
            // ... 其他错误映射
            else -> Pair(9999, \"未知错误\")
        }
    }

    // Hilt 入口点，用于非 Hilt 环境下获取实例
    @EntryPoint
    @InstallIn(SingletonComponent::class)
    interface AvlSdkApiEntryPoint {
        fun getAvlSdkApi(): AvlSdkApi
    }

    companion object {
        @Volatile
        private var instance: AvlSdkApi? = null

        fun getInstance(context: Context): AvlSdkApi {
            return instance ?: synchronized(this) {
                instance ?: EntryPointAccessors.fromApplication(
                    context.applicationContext,
                    AvlSdkApiEntryPoint::class.java
                ).getAvlSdkApi().also { instance = it }
            }
        }
    }
}

```

### 5.4 阶段四：`avl_pc` & `avl_monitor` 整合 (同前)

1.  **分析依赖关系:** 明确 `avl_pc` 和 `avl_monitor` 与 `avl_sdk` 的关系。它们是 `avl_sdk` 的扩展、特定平台的实现，还是独立的消费者？
2.  **适配新架构:**
    *   如果它们依赖 `avl_sdk` 的内部实现，需要修改为依赖新的 `SDK API Facade`。
    *   如果它们自身也包含复杂的逻辑，考虑将它们也按照 Clean Architecture 或类似模式进行重构。
    *   确保 `avl_monitor` 的监控数据能有效地上报或与 `avl_sdk` 的核心逻辑解耦。

### 5.5 阶段五：集成测试与文档 (同前)

1.  **集成测试:**
    *   编写集成测试，验证跨模块的业务流程（如：配置 -> 扫描 -> 上报）。
    *   测试 SDK 在不同 Android 版本和设备上的表现。
2.  **文档更新:**
    *   更新所有设计文档（PUML 图等）以反映新的架构。
    *   编写详细的开发者文档，说明如何集成和使用新版 SDK API。
    *   更新 `README.md`。

## 6. 风险与缓解措施

*   **风险:** API 兼容性破坏。
    *   **缓解:** 提供适配层或弃用旧 API 并给出迁移指南；充分沟通变更。
*   **风险:** 迁移 Kotlin 引入的不稳定性或性能问题。
    *   **缓解:** 渐进式迁移；加强 Code Review；进行充分的性能测试。
*   **风险:** 重构引入新的 Bug。
    *   **缓解:** 加强单元测试和集成测试覆盖率；分阶段发布（灰度发布）。
*   **风险:** 工作量评估不准，项目延期。
    *   **缓解:** 分阶段进行，每个阶段设定明确目标和里程碑；定期评估进度和风险。

## 7. 时间与资源估算 (初步)

这是一个相对复杂的重构项目，具体时间取决于团队规模、成员熟悉度以及需要重构的代码量。初步估计：

*   **阶段一 (准备):** 1-2 周
*   **阶段二 (核心模块重构):** 4-8 周 (取决于模块数量和复杂度)
*   **阶段三 (API 重构):** 1-2 周
*   **阶段四 (`avl_pc`/`avl_monitor` 整合):** 1-2 周 (取决于其复杂度)
*   **阶段五 (测试与文档):** 2-3 周

**总计:** 约 9 - 17 周。**这是一个粗略估计，需要根据实际情况进行调整。**

## 8. 结论

通过本次升级优化，`avl_sdk` 将拥有更清晰的架构、更高的代码质量、更好的可测试性和可维护性。引入清洁架构和依赖注入将为未来的功能扩展和技术演进奠定坚实的基础。采用渐进式迁移 Kotlin 的策略可以在控制风险的同时，逐步获得现代语言带来的优势。这将最终提升 SDK 的整体竞争力和开发者体验。
