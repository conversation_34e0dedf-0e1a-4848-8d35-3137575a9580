package com.antiy.demo.activity

import android.content.Context
import android.content.Intent
import android.os.Build
import android.view.LayoutInflater
import android.view.WindowManager
import androidx.core.content.ContextCompat
import androidx.core.view.WindowCompat
import com.antiy.demo.R
import com.antiy.demo.base.BaseActivity
import com.antiy.demo.databinding.ActivityHelpFeedbackBinding

class HelpFeedbackActivity : BaseActivity<ActivityHelpFeedbackBinding>() {

    override fun getViewBinding(inflater: LayoutInflater): ActivityHelpFeedbackBinding {
        return ActivityHelpFeedbackBinding.inflate(inflater)
    }

    companion object {
        fun start(context: Context) {
            context.startActivity(Intent(context, HelpFeedbackActivity::class.java))
        }
    }

    override fun initView() {
        // 设置沉浸式状态栏
        setupStatusBar()
        
        // 设置返回按钮
        setupBackButton()
        
        // 设置点击事件
        setupClickListeners()
    }
    
    private fun setupStatusBar() {
        // 确保状态栏正确显示
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            // 设置状态栏为亮色模式，图标为深色
            WindowCompat.getInsetsController(window, window.decorView)?.apply {
                isAppearanceLightStatusBars = true // 设置为true，因为顶部是白色背景，需要黑色状态栏图标
            }
            
            // 设置状态栏为透明
            window.statusBarColor = ContextCompat.getColor(this, android.R.color.transparent)
            
            // 设置内容延伸到状态栏下方，但状态栏仍然可见
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
            
            // 使内容延伸到系统栏下方
            WindowCompat.setDecorFitsSystemWindows(window, false)
        }
    }
    
    private fun setupBackButton() {
        binding.btnBack.setOnClickListener {
            finish()
        }
    }
    
    private fun setupClickListeners() {
        // 常见问题点击事件
        binding.cardScanHelp.setOnClickListener {
            // TODO: 跳转到病毒扫描帮助页面或显示详细信息
        }
        
        binding.cardScheduleHelp.setOnClickListener {
            // TODO: 跳转到定时扫描帮助页面或显示详细信息
        }
        
        binding.cardVirusFoundHelp.setOnClickListener {
            // TODO: 跳转到病毒处理帮助页面或显示详细信息
        }
        
        // 联系方式点击事件
        binding.cardOnlineSupport.setOnClickListener {
            // TODO: 跳转到在线客服聊天界面
        }
        
        binding.cardEmailSupport.setOnClickListener {
            // 发送邮件
            val emailIntent = Intent(Intent.ACTION_SEND).apply {
                type = "message/rfc822"
                putExtra(Intent.EXTRA_EMAIL, arrayOf(binding.tvSupportEmail.text.toString()))
                putExtra(Intent.EXTRA_SUBJECT, "[安全卫士]用户反馈")
            }
            
            try {
                startActivity(Intent.createChooser(emailIntent, "选择邮件客户端"))
            } catch (e: Exception) {
                // 处理没有邮件客户端的情况
            }
        }
    }
} 