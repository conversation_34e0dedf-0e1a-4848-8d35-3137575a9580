package com.antiy.avlsdk.scan;

import android.text.TextUtils;

import com.antiy.avlsdk.AVLCoreEngine;
import com.antiy.avlsdk.AVLEngine;
import com.antiy.avlsdk.entity.ResultScan;

/**
 * Author: wangbiao Date: 2024/9/9 11:26 Description:
 */
public class MobileEngine {
    private static long totalTime = 0;
    public static ResultScan scan(String filePath,String sha256) {
        long currentTime = System.currentTimeMillis();
        String result = AVLCoreEngine.getInstance().scan(filePath);
        long offsetTime = System.currentTimeMillis() - currentTime;
        totalTime += offsetTime;
        AVLEngine.Logger.info("mobile engine time:" + offsetTime + ",totaltime:" + totalTime);
        ResultScan resultScan = new ResultScan();
        resultScan.isMalicious = !TextUtils.isEmpty(result);
        resultScan.virusName = TextUtils.isEmpty(result) ? "" : result;
        if (!ScanResultConfig.shouldCalculateSha256()){
            resultScan.sha256sum = sha256;
        }
        resultScan.isCloudScan = false;
        AVLEngine.Logger.info("mobile engine result:" + resultScan + ",filePath:" + filePath);
        return resultScan;
    }
}
