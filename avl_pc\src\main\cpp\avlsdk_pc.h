//
// Created by zos on 2024/9/25.
//

#ifndef DEMO_AVLSDK_PC_H
#define DEMO_AVLSDK_PC_H

#include "AVLSDK_conf_idx.h"
#include "AVLSDK_rpt_idx.h"
#include "ID2Name_interface.h"
#include "engine.h"
#include "error_code.h"
#include "vlog.h"

#define MAX(a, b) ((a) > (b) ? (a) : (b))
#define MIN(a, b) ((a) < (b) ? (a) : (b))

#define DEFAULT_SDK_PATH     ("AVLSDK.so")
#define DEFAULT_ID2NAME_PATH ("aid2name.so")
#define DEFAULT_ID2N_DB_PATH ("NData")
#define DEFAULT_LOAD_CONFIG  ("Config/high_scan.ct")

typedef long (*P_AVL_SDK_Create)(void** pEngine);

typedef long (*P_AVL_SDK_Release)(void* pEngine);

typedef long (*P_AVL_SDK_LoadConfigFile)(void* pEngine, char* szFilename);

typedef long (*P_AVL_SDK_Init)(void* pEngine, const void* pVerificationCode);

typedef long (*P_AVL_SDK_SetConfigInt)(void* pEngine, long CfgIdx, long lValue);

typedef long (*P_AVL_SDK_SetConfigString)(void* pEngine, long CfgIdx, const char* pValue);

typedef long (*P_AVL_SDK_Scan)(void* pEngine, P_OBJ_PROVIDER pObj, P_OBJ_DISPOSER pObjDisposer);

typedef long (*P_AVL_SDK_QueryReportInt)(void* pEngine, void* pRptHandle, unsigned long key, long* value);

typedef long (*P_AVL_SDK_QueryReportStr)(void* pEngine, void* pRptHandle, unsigned long key, unsigned char** value);

typedef long (*P_AVL_SDK_QueryDBInfo)(void* pEngine, P_DB_INFO pDBInfo);

typedef long (*P_AVL_SDK_GetCurVersion)(char* buf, long len);

typedef long (*P_AVL_SDK_ReloadDB)(void* pEngine);

typedef long (*P_AVL_NTranser_Init)(const char* path, void** handle);

typedef long (*P_AVL_NTranser_QueryNameByID)(void* handle, const char* mod_name, long id, unsigned char* buf, unsigned long size);

typedef void (*P_AVL_NTranser_Release)(void* handle);

typedef struct _engine_param {
        void*                        p_engine;
        void*                        p_i2n_handle;
        P_AVL_SDK_QueryReportInt     p_query_rpt_int;
        P_AVL_SDK_QueryReportStr     p_query_rpt_str;
        P_AVL_NTranser_QueryNameByID p_query_name;
} ENGINE_PARAM, *P_ENGINE_PARAM;

typedef struct ScanResult {
        char          malwareName[128];
        char          description[256];
        unsigned char md5[16];
} ScanResult;

typedef struct EngineHandler {
        void* pengine_handle;
        void* p_i2n_handle;

        P_AVL_SDK_Create             p_create;
        P_AVL_SDK_Release            p_release;
        P_AVL_SDK_Init               p_init;
        P_AVL_SDK_LoadConfigFile     p_config;
        P_AVL_SDK_SetConfigInt       p_set_cfg_int;
        P_AVL_SDK_SetConfigString    p_set_cfg_str;
        P_AVL_SDK_Scan               p_scan;
        P_AVL_SDK_QueryReportInt     p_query_rpt_int;
        P_AVL_SDK_QueryReportStr     p_query_rpt_str;
        P_AVL_SDK_ReloadDB           p_reload;
        P_AVL_SDK_QueryDBInfo        p_query_db_info;
        P_AVL_SDK_GetCurVersion      p_get_cur_version;
        P_AVL_NTranser_Init          p_i2n_init;
        P_AVL_NTranser_Release       p_i2n_release;
        P_AVL_NTranser_QueryNameByID p_i2n_query_name;

        ScanResult pScanResult;
} EngineHandler;

long func_long_query_continue_callback(void* p_param);
long func_long_get_rslt_callback(P_OBJ_PROVIDER p_op, void* p_data, void* p_param);
void func_void_show_usage();
long native_scan(const char* p_filepath, ScanResult* result);
long native_init(const char* lib_dir);
long my_func_long_get_rslt_callback(P_OBJ_PROVIDER p_op, void* p_data, void* p_param);

#endif   // DEMO_AVLSDK_PC_H
