package com.antiy.avlsdk;

import static com.antiy.avlsdk.pc.PCConstant.VENDOR_DIR;
import static com.antiy.avlsdk.utils.SdkConst.ZIP_SUFFIX;

import android.content.Context;

import com.antiy.avlsdk.pc.AssetsUtil;
import com.antiy.avlsdk.pc.VendorZipUtils;
import com.antiy.avlsdk.utils.FileUtil;

import java.io.File;
import java.io.IOException;

/**
 * AVLCoreEngine 类是安天病毒引擎的 Java 层封装类
 *
 * <h2>主要功能:</h2>
 * <ol>
 *     <li>提供 Java 和 C++层的接口适配</li>
 *     <li>实现病毒引擎的初始化、扫描、资源释放等核心功能</li>
 *     <li>管理引擎运行所需的文件资源和路径</li>
 *     <li>提供授权认证相关的接口</li>
 * </ol>
 *
 * <h2>使用说明:</h2>
 * <ol>
 *     <li>使用{@link #getInstance()}获取单例实例</li>
 *     <li>调用{@link #prepareData(Context)}准备运行环境</li>
 *     <li>完成授权认证后调用{@link #init(String, String, String)}初始化引擎</li>
 *     <li>使用{@link #scan(String)}等接口进行病毒检测</li>
 *     <li>使用完毕后调用{@link #release()}释放资源</li>
 * </ol>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/9/6
 */
public class AVLCoreEngine {
    private static volatile AVLCoreEngine instance;
    private final String TARGET_DIR = "avlsdk_mobile";
    private static final String VIDS_DIR = AVLEngine.getInstance().getContext().getFilesDir().getAbsolutePath();

    private String mobileAVLPath;

    // 加载本地库，确保 native 方法的调用
    static {
        System.loadLibrary("avlm_engine");
    }

    private AVLCoreEngine() {
    }

    /**
     * 获取 AVLCoreEngine 的单例实例
     * 使用双重检查锁定模式 (Double-Checked Locking) 实现线程安全的单例模式
     *
     * @return AVLCoreEngine 的唯一实例
     */
    public static AVLCoreEngine getInstance() {
        if (instance == null) {  // 第一次检查，避免不必要的同步
            synchronized (AVLCoreEngine.class) {  // 同步锁，确保线程安全
                if (instance == null) {  // 第二次检查，避免重复创建实例
                    instance = new AVLCoreEngine();
                }
            }
        }
        return instance;
    }

    public String getMobileAVLPath() {
        return mobileAVLPath;
    }
    /**
     * 拷贝需要的数据到本地 耗时任务
     *
     * @param context
     */
    /**
     * 准备引擎运行所需的数据文件
     *
     * @param context Android 上下文对象，用于获取应用程序文件目录
     *                <p>
     *                功能说明：
     *                1. 检查应用私有目录下是否存在 avlsdk_mobile 目录及其文件
     *                2. 如果目录为空或不存在则返回，如果不为从 vendor/avlsdk 解压所需文件
     *                3. 解压的文件将用于 PC 引擎的运行环境
     *                <p>
     *                注意事项：
     *                - 该方法为耗时操作，建议在子线程中调用
     *                - 如果目标目录已存在且包含文件，则跳过解压操作
     *                - 解压失败时会打印异常堆栈，但不会抛出异常
     */
    public void prepareData(Context context) {
        // 拷贝 avlsdk_mobile 目录中的文件 准备 PC 引擎需要的内容
        File parentDir = new File(VIDS_DIR);
        if (!parentDir.exists()) {
            AVLEngine.Logger.error("私有目录不存在，创建目录失败");
            return;
        }
        File avlsdkMobile = new File(VIDS_DIR + File.separator + TARGET_DIR);
        mobileAVLPath = avlsdkMobile.getAbsolutePath();
        AVLEngine.Logger.error("avlsdk_mobile exist:" + avlsdkMobile.exists());
        AVLEngine.Logger.error("avlsdk_mobile has file:" + FileUtil.isFolderHasFiles(avlsdkMobile.getAbsolutePath()));
        AVLEngine.Logger.error("FLAVOR:" + BuildConfig.FLAVOR);
        if (avlsdkMobile.exists() && FileUtil.isFolderHasFiles(avlsdkMobile.getAbsolutePath()))
            return;
        try {
            AssetsUtil.unzipFromAssets(context, VIDS_DIR,TARGET_DIR + ZIP_SUFFIX);
        } catch (IOException e) {
            e.printStackTrace();
        }

    }

    /**
     * 获取 lib 路径地址
     *
     * @return
     */
    /**
     * 获取引擎库文件路径
     * 功能说明：
     * 1. 获取应用私有目录下avlsdk_mobile/libs目录的完整路径
     * 2. 该路径用于存放引擎运行所需的动态库文件
     * 返回值说明：
     * - 返回形如"/data/data/包名/files/avlsdk_mobile/libs"的完整路径
     * 注意事项：
     * - 调用此方法前需确保 AVLEngine 已正确初始化
     * - 返回的路径不保证目录一定存在，使用前应当检查
     *
     * @return 引擎库文件目录的完整路径
     */
    public String getLibPath() {
        return VIDS_DIR + File.separator + TARGET_DIR + File.separator + "libs";
    }

    /**
     * 获取 AVL 特征库文件路径
     * <p>
     * 功能说明：
     * 1. 获取应用私有目录下avlsdk_mobile/avl目录的完整路径
     * 2. 该路径用于存放病毒特征库文件
     * <p>
     * 返回值说明：
     * - 返回形如"/data/data/包名/files/avlsdk_mobile/avl"的完整路径
     * <p>
     * 注意事项：
     * - 调用此方法前需确保 AVLEngine 已正确初始化
     * - 返回的路径不保证目录一定存在，使用前应当检查
     *
     * @return 特征库文件目录的完整路径
     */
    public String getDbPath() {
        return VIDS_DIR + File.separator + TARGET_DIR + File.separator + "avl";
    }

    public String getPcUpdateCheckerPath() {
        return VIDS_DIR;
    }
    /**
     * 保存 uuid 和 client_id 给 jni
     *
     * @param uuid     客户端 uuid
     * @param clientId SDK 的识别符
     */
    public native void setupAuthParams(String uuid, String clientId);

    /**
     * 生成授权请求的请求 content 数据
     *
     * @param requestTime 请求发起时间
     * @param flag        随机数，范围 [0,31]
     * @return base64 编码的请求数据
     */
    public native String generateAuthRequest(String requestTime, int flag);

    /**
     * 解码授权凭证
     *
     * @param body         响应数据
     * @param responseTime 响应时间
     * @param flag         随机数
     * @return base64 编码的授权凭据
     */
    public native String decryptAuthToken(String body, String responseTime, int flag);

    /**
     * 初始化引擎
     *
     * @param libPath 引擎 so 库文件夹路径
     * @param dbPath  特征库文件夹路径
     * @param token   授权凭据
     * @return 0: 初始化成功; 非 0: 初始化失败
     */
    public native int init(String libPath, String dbPath, String token);

    /**
     * 释放引擎资源
     *
     * @return 1：成功，0：失败
     */
    public native int release();

    /**
     * 设置引擎的扫描选项
     *
     * @param flag 扫描选项标志（平台、检测开关等）
     * @return 1：成功，0：失败
     */
    public native int setScanOpt(int flag);

    /**
     * 设置扫描分类选项
     *
     * @param flag 分类选项（如病毒、木马等）
     * @return 1：成功，0：失败
     */
    public native int setScanCategoryOpt(int flag);

    /**
     * 注册一个信号处理函数以捕获 SIGINT
     */
    public native void installSignalHandler();

    /**
     * 还原默认的信号处理函数
     */
    public native void restoreSignalHandler();

    /**
     * 扫描指定文件
     *
     * @param filePath 目标文件路径
     * @return 病毒名称 如果病毒名是空，表示没病毒
     */
    public native String scan(String filePath);

    /**
     * 扫描给定的文件描述符。
     *
     * @param fileDescriptor 目标文件描述符，从 ContentURLProvider 中获取
     * @return 病毒名称 如果病毒名为空，表示没有病毒。
     */
    public native String scan(int fileDescriptor);

    /**
     * 获取特征库版本
     *
     * @return 特征库版本信息
     */
    public native String getSigLibVersion();

    /**
     * 获取引擎版本
     *
     * @return 引擎版本信息
     */
    public native String getEngineVersion();

    /**
     * 安装引擎或病毒库包
     *
     * @param packagePath    包路径
     * @param packageType    包类型（0: 引擎包，1: 病毒库包）
     * @param toVersion      版本信息
     * @param sigPackageType 包类型（zip, tar 等）
     * @return 非 0：成功，0：失败
     */
    public native int installPackage(String packagePath, int packageType, String toVersion,
                                     String sigPackageType);

    /**
     * 获取 apk 文件的 key 哈希
     *
     * @param packagePath 包文件路径
     * @return 字符串格式的包签名哈希
     */
    public native String getCertHash(String packagePath);

    /**
     * 获取 apk 文件的全部 key 哈希
     *
     * @param packagePath 包文件路径
     * @return 字符串格式的包签名哈希
     */
    public native String getCertFullHash(String packagePath);

}
