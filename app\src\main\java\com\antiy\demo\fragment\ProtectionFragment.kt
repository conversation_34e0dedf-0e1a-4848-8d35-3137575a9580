package com.antiy.demo.fragment

import android.view.LayoutInflater
import android.view.ViewGroup
import com.antiy.demo.base.BaseFragment
import com.antiy.demo.databinding.FragmentProtectionBinding

class ProtectionFragment : BaseFragment<FragmentProtectionBinding>() {


    override fun getViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): FragmentProtectionBinding {
        return FragmentProtectionBinding.inflate(inflater, container, false)
    }


    override fun initView() {
        // 设置初始状态
        binding.switchMainProtection.isChecked = true
        updateProtectionStatus(true)
        setModuleSwitchesEnabled(true)
    }

    override fun initListener() {
        // 主开关监听器
        binding.switchMainProtection.setOnCheckedChangeListener { _, isChecked ->
            updateProtectionStatus(isChecked)
            setModuleSwitchesEnabled(isChecked)
        }

        // 文件防护开关监听器
        binding.switchFileProtection.setOnCheckedChangeListener { _, isChecked ->
            handleModuleSwitch("文件防护", isChecked)
        }

        // 网络防护开关监听器
        binding.switchNetworkProtection.setOnCheckedChangeListener { _, isChecked ->
            handleModuleSwitch("网络防护", isChecked)
        }

        // 应用防护开关监听器
        binding.switchAppProtection.setOnCheckedChangeListener { _, isChecked ->
            handleModuleSwitch("应用防护", isChecked)
        }

        // 隐私防护开关监听器
        binding.switchPrivacyProtection.setOnCheckedChangeListener { _, isChecked ->
            handleModuleSwitch("隐私防护", isChecked)
        }
    }




    private fun updateProtectionStatus(isEnabled: Boolean) {
        binding.tvProtectionStatus.text = if (isEnabled) "您的设备受到全面保护" else "实时防护已关闭"
    }

    private fun setModuleSwitchesEnabled(enabled: Boolean) {
        binding.switchFileProtection.isEnabled = enabled
        binding.switchNetworkProtection.isEnabled = enabled
        binding.switchAppProtection.isEnabled = enabled
        binding.switchPrivacyProtection.isEnabled = enabled

        if (!enabled) {
            binding.switchFileProtection.isChecked = false
            binding.switchNetworkProtection.isChecked = false
            binding.switchAppProtection.isChecked = false
            binding.switchPrivacyProtection.isChecked = false
        }
    }

    private fun handleModuleSwitch(moduleName: String, isEnabled: Boolean) {
        // 这里可以添加具体的防护模块开启/关闭逻辑
        // 例如：保存设置到SharedPreferences，启动/停止相关服务等
    }
}