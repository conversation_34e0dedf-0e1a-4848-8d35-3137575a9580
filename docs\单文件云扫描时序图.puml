@startuml FileScanner单文件云扫描时序图(修复后)

!theme plain
skinparam backgroundColor #FFFFFF
skinparam sequenceArrowThickness 2
skinparam roundcorner 20
skinparam maxmessagesize 60
skinparam sequenceParticipant underline

' 定义颜色用于标记修复和优化点
skinparam note {
  BackgroundColor #E8F5E8
  BorderColor #4CAF50
}

actor C<PERSON> as "调用方"
participant FileScanner as "FileScanner"
participant AV<PERSON>ng<PERSON> as "AVLEngine"
participant DataManager as "DataManager"
participant NetworkManager as "NetworkManager"
participant Encry<PERSON><PERSON><PERSON><PERSON><PERSON> as "EncryptorHelper\n(哈希计算)"
participant CloudScanner as "CloudScanner\n(单文件实例)"
participant hashExecutor as "hashExecutor\n线程池"
participant scanExecutor as "scanExecutor\n线程池"
participant taskQueue as "taskQueue\n任务队列"
participant CloudScanService as "CloudScanService"
participant CountDownLatch as "CountDownLatch\n同步机制"
participant ScanListener as "ScanListener\n匿名回调"

== 单文件扫描初始化阶段 ==

Client -> FileScanner: scan(filePath)
activate FileScanner
note right: 默认isFromCloud=false\n开始单文件扫描流程

FileScanner -> AVLEngine: getInstance().getInitResult()
activate AVLEngine
AVLEngine --> FileScanner: InitResult
deactivate AVLEngine

FileScanner -> FileScanner: 检查文件是否存在
FileScanner -> FileScanner: 检查文件类型是否合法

== 缓存和黑白名单检查阶段 ==

FileScanner -> FileScanner: CacheManager.hasCache(filePath)
alt 有缓存
    FileScanner -> FileScanner: 返回缓存结果
    FileScanner --> Client: ResultScan(缓存)
    note right: 直接返回，不走云扫描
else 无缓存
    FileScanner -> EncryptorHelper: calcPathSHA256(filePath)
    activate EncryptorHelper
    EncryptorHelper --> FileScanner: hash值
    deactivate EncryptorHelper
    
    FileScanner -> FileScanner: BlackWhiteManager.match(hash)
    alt 在黑白名单中
        FileScanner --> Client: ResultScan(黑白名单结果)
        note right: 直接返回，不走云扫描
    end
end

== 文件大小判断和云扫描触发阶段 ==

FileScanner -> FileScanner: 获取文件大小 length
FileScanner -> DataManager: getCloudSizeThreshold()
activate DataManager
DataManager --> FileScanner: spCloudSizeThreshold
deactivate DataManager

FileScanner -> NetworkManager: isAvailable()
activate NetworkManager
NetworkManager --> FileScanner: 网络状态
deactivate NetworkManager

alt length > spCloudSizeThreshold && 网络可用 && !isFromCloud
    FileScanner -> FileScanner: cloudScan(filePath, hash)
    note right #E8F5E8: ✅ 性能优化:\n传递已计算的哈希值\n避免重复计算SHA256
    
    == 单文件云扫描执行阶段 ==

    FileScanner -> FileScanner: 设置超时时间(60秒)
    note right #E8F5E8: ✅ 安全改进:\n添加超时机制\n防止永久阻塞

    FileScanner -> CountDownLatch: new CountDownLatch(1)
    activate CountDownLatch

    FileScanner -> FileScanner: 创建单文件List<File>
    FileScanner -> FileScanner: 创建预计算哈希映射
    note right #E8F5E8: ✅ 性能优化:\n使用已计算的哈希值\n避免CloudScanner重复计算

    FileScanner -> CloudScanner: new CloudScanner(files, listener, false, preCalculatedHashes)
    activate CloudScanner
    note right: cloudCheck=false\n云扫描失败时不会降级到本地扫描\n\n✅ 新增: 支持预计算哈希值
    
    CloudScanner -> CloudScanner: 初始化hashExecutor线程池
    CloudScanner -> CloudScanner: 初始化scanExecutor线程池
    CloudScanner -> CloudScanner: 初始化taskQueue队列
    
    FileScanner -> CloudScanner: startScan()
    
    == CloudScanner内部多线程处理 ==
    
    CloudScanner -> hashExecutor: execute(lambda)
    activate hashExecutor
    note right: 为单个文件提交哈希任务
    
    hashExecutor -> taskQueue: put(new ScanTask(0, file))
    activate taskQueue
    taskQueue --> hashExecutor: 任务入队成功
    deactivate taskQueue
    deactivate hashExecutor
    
    CloudScanner -> scanExecutor: execute(this::processScan)
    activate scanExecutor
    
    scanExecutor -> scanExecutor: processScan()
    scanExecutor -> taskQueue: poll()
    activate taskQueue
    taskQueue --> scanExecutor: ScanTask
    deactivate taskQueue
    
    scanExecutor -> scanExecutor: 添加到batch(单个文件)
    scanExecutor -> scanExecutor: processCloudScanBatch(batch)
    
    == 哈希计算和云服务调用 ==

    scanExecutor -> scanExecutor: calculateBatchHashes(batch)
    note right #E8F5E8: ✅ 优化逻辑:\n优先使用预计算哈希值

    alt 有预计算哈希值
        scanExecutor -> scanExecutor: 使用preCalculatedHashes.get(filePath)
        note right #E8F5E8: 直接使用已计算的哈希值\n避免重复计算
    else 无预计算哈希值
        scanExecutor -> EncryptorHelper: calcPathSHA256(filePath)
        activate EncryptorHelper
        EncryptorHelper --> scanExecutor: SHA256哈希值
        deactivate EncryptorHelper
        note right: 现场计算哈希值
    end
    
    scanExecutor -> CloudScanService: batchScanAsync(hashes, callback)
    activate CloudScanService
    CloudScanService -> CloudScanService: 创建网络请求
    CloudScanService -> CloudScanService: 发送POST请求到云端
    
    alt 云扫描成功
        CloudScanService --> scanExecutor: onSuccess(results)
        deactivate CloudScanService
        
        scanExecutor -> scanExecutor: processCloudScanResults(batch, results)
        scanExecutor -> scanExecutor: processIndividualScanResult(task, cloudScan)
        
        scanExecutor -> ScanListener: scanFileStart(0, filePath)
        activate ScanListener
        ScanListener --> scanExecutor: 
        deactivate ScanListener
        
        scanExecutor -> scanExecutor: createScanResult(cloudScan)
        scanExecutor -> ScanListener: scanFileFinish(0, filePath, result)
        activate ScanListener
        ScanListener -> ScanListener: resultScan[0] = result
        ScanListener -> CountDownLatch: countDown()
        ScanListener --> scanExecutor: 
        deactivate ScanListener
        
    else 云扫描失败
        CloudScanService --> scanExecutor: onError(error)
        deactivate CloudScanService

        scanExecutor -> scanExecutor: handleScanError(batch, error)
        note right: 关键差异:\nisFromCloudCheck=false\n不会降级到本地扫描

        scanExecutor -> scanExecutor: processBatchError(batch)
        scanExecutor -> ScanListener: scanFileFinish(0, filePath, failResult)
        activate ScanListener
        ScanListener -> ScanListener: resultScan[0] = failResult
        note right #E8F5E8: ✅ 改进的错误处理:\n提供详细的错误信息\n包含失败原因
        ScanListener -> CountDownLatch: countDown()
        ScanListener --> scanExecutor:
        deactivate ScanListener
    end
    
    == 同步等待和资源清理 ==

    scanExecutor -> scanExecutor: finally块执行
    note right #E8F5E8: ✅ 修复点:\n移除自我关闭逻辑\n由统一的资源管理方法处理
    scanExecutor -> scanExecutor: 处理停止状态回调
    note right: 不再在finally中关闭线程池\n避免自我关闭问题
    deactivate scanExecutor

    FileScanner -> CountDownLatch: await(60, TimeUnit.SECONDS)
    note right #E8F5E8: ✅ 安全改进:\n添加60秒超时机制\n防止永久阻塞

    alt 在超时时间内完成
        CountDownLatch --> FileScanner: 正常完成
        FileScanner -> FileScanner: 检查结果有效性
        FileScanner -> FileScanner: return resultScan[0]
    else 超时
        CountDownLatch --> FileScanner: 超时
        FileScanner -> CloudScanner: stopScan()
        note right #E8F5E8: ✅ 超时处理:\n主动停止扫描\n释放资源
        FileScanner -> FileScanner: return 超时错误结果
    end

    deactivate CountDownLatch
    deactivate CloudScanner

    FileScanner --> Client: ResultScan(云扫描结果)
    
else 文件大小未超过阈值或网络不可用
    FileScanner -> FileScanner: 判断文件类型(APK/JAR/DEX)
    alt 是APK/JAR/DEX
        FileScanner -> FileScanner: MobileEngine.scan(filePath)
    else 其他文件类型
        FileScanner -> FileScanner: PcEngine.scan(filePath)
    end
    FileScanner --> Client: ResultScan(本地扫描结果)
end

== 异常处理和中断恢复机制 ==

alt 扫描过程中发生异常
    FileScanner -> FileScanner: 捕获异常
    alt InterruptedException
        FileScanner -> FileScanner: Thread.currentThread().interrupt()
        note right #E8F5E8: ✅ 中断恢复:\n正确恢复线程中断状态
        FileScanner -> CloudScanner: stopScan()
        FileScanner -> FileScanner: return 中断错误结果
    else 其他异常
        FileScanner -> CloudScanner: stopScan()
        FileScanner -> FileScanner: return 异常错误结果
        note right #E8F5E8: ✅ 异常处理:\n确保资源正确清理
    end
end

deactivate FileScanner

== 修复效果和架构改进分析 ==

note over FileScanner, CloudScanner
🎯 **单文件云扫描修复效果**:

✅ **安全性改进**:
1. **超时保护**: 60秒CountDownLatch.await()超时机制
2. **中断恢复**: 正确处理InterruptedException
3. **资源清理**: 异常时主动调用stopScan()
4. **状态检查**: 结果有效性验证

✅ **性能优化**:
1. **避免重复计算**: 传递预计算的SHA256哈希值
2. **减少开销**: CloudScanner直接使用已有哈希值
3. **智能选择**: 有预计算值时跳过现场计算

✅ **架构对比** (单文件 vs 批量):
- **触发条件**: 文件大小阈值 vs 文件数量阈值
- **降级策略**: 无降级 vs 本地扫描降级
- **同步机制**: 超时阻塞等待 vs 异步回调
- **资源管理**: 统一的线程池生命周期管理

🔧 **核心改进点**:
1. 移除CloudScanner的线程池自我关闭问题
2. 添加CountDownLatch超时机制(60秒)
3. 支持预计算哈希值传递
4. 完善异常处理和中断恢复
5. 增强结果验证和错误信息
end note

note over hashExecutor, scanExecutor
📈 **性能和稳定性提升总结**:

🚀 **性能提升**:
- **哈希计算优化**: 避免重复计算SHA256，性能提升显著
- **内存使用优化**: 预计算哈希值复用，减少内存分配
- **执行效率**: 减少不必要的计算开销

🛡️ **稳定性增强**:
- **超时保护**: 防止永久阻塞，提高应用响应性
- **异常恢复**: 完善的中断处理和异常恢复机制
- **资源管理**: 统一的线程池生命周期管理

🔄 **架构优化**:
- **职责分离**: 线程池管理职责清晰
- **状态一致**: 避免竞态条件和状态不一致
- **错误处理**: 分层的错误处理和恢复机制

📊 **修复前后对比**:
❌ **修复前**: 永久阻塞风险 + 重复计算 + 资源泄露
✅ **修复后**: 超时保护 + 哈希复用 + 正确清理

🎯 **最终效果**:
单文件云扫描现在具备了生产环境所需的
稳定性、性能和可靠性保障
end note

@enduml
