package com.antiy.demo.utils

import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.IOException

/**
 * Author: wangbiao
 * Date: 2024/9/14 15:43
 * Description:
 */
class FileCopyManager {

    /**
     * 从源目录拷贝前100个文件到目标目录
     *
     * @param sourceDir 源文件目录
     * @param destinationDir 目标文件目录
     */
    fun copyFirst100Files(sourceDir: File, destinationDir: File) {
        // 检查源目录是否存在且是一个目录
        if (!sourceDir.exists() || !sourceDir.isDirectory) {
            throw IllegalArgumentException("源目录无效或不存在")
        }

        // 如果目标目录不存在，创建目标目录
        if (!destinationDir.exists()) {
            destinationDir.mkdirs()
        }

        // 获取源目录下的文件列表
        val files = sourceDir.listFiles()

        if (files == null || files.isEmpty()) {
            println("源目录中没有文件")
            return
        }

        // 按照文件名排序，取前100个文件
        files.sort() // 默认按文件名排序
        val fileCount = files.size.coerceAtMost(100) // 取100个或更少的文件
        println("取$fileCount")
        // 遍历文件，拷贝到目标目录
        for (i in 0 until fileCount) {
            val sourceFile = files[i]
            val destinationFile = File(destinationDir, sourceFile.name)

            try {
                // 拷贝文件
                copyFile(sourceFile, destinationFile)
                println("文件已拷贝: ${sourceFile.name}")
            } catch (e: IOException) {
                println("文件拷贝失败: ${sourceFile.name}")
                e.printStackTrace()
            }
        }

        println("已完成前100个文件的拷贝")
    }

    /**
     * 文件拷贝方法，使用字节流复制文件
     */
    private fun copyFile(sourceFile: File, destinationFile: File) {
        FileInputStream(sourceFile).use { inputStream ->
            FileOutputStream(destinationFile).use { outputStream ->
                val buffer = ByteArray(1024)
                var length: Int
                while (inputStream.read(buffer).also { length = it } > 0) {
                    outputStream.write(buffer, 0, length)
                }
            }
        }
    }
}
