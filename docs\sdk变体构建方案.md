# Android SDK 多变体构建方案

## 1. 背景与需求

我们有一个基于Java开发的Android SDK（AVL SDK），需要通过Jenkins实现多版本构建，具体要求如下：

- **默认行为**：产出带PC检测能力的标准版SDK（.aar）
- **动态变体**：通过Jenkins参数生成不带PC检测的版本
- **扩展性**：未来可能新增变体（如无广告版、高通芯片专用版等）
- **现有变体**：已有changan、greatwall、base等变体

## 2. 当前架构分析

### 2.1 模块结构

当前项目由以下几个主要模块组成：

- **avl_sdk**：核心SDK模块，包含主要的扫描逻辑和引擎初始化
- **avl_pc**：PC检测引擎模块，提供PC文件的病毒检测能力
- **avl_monitor**：监控模块
- **avl_cloud**：云端检测模块
- **app**：示例应用

### 2.2 当前变体实现

目前项目已经实现了基于flavor的变体：

```gradle
// 定义产品变体
flavorDimensions "client"
productFlavors {
    // 全量版本
    base {
        dimension "client"
    }
    // 长城版本
    greatwall {
        dimension "client"
    }
    // 长安版本
    changan {
        dimension "client"
    }
}
```

### 2.3 PC引擎集成方式

当前PC引擎的集成方式：

1. **依赖关系**：avl_sdk通过fat-aar插件将avl_pc模块嵌入到最终的AAR中
2. **条件初始化**：在AVLEngine.init()方法中，根据flavor决定是否初始化PC引擎

```java
if(!BuildConfig.FLAVOR.equals("changan")){
    // 长城版本需要初始化PC引擎
    boolean pcInitResult = initPcEngine();
    resultInit.isSuccess = pcInitResult;
    resultInit.reason = pcInitResult ? "init success" : "pc engine init auth fail";

    prepareUpdateCheckerExe(context);
}
```

3. **扫描流程**：在FileScanner中，根据文件类型决定使用PC引擎还是移动引擎扫描

## 3. 架构设计方案

### 3.1 变体代码隔离策略

针对PC检测能力的变体，我们可以采用以下两种方案：

#### 方案一：接口抽象

1. **定义引擎接口**：创建IEngine接口，统一不同引擎的行为
2. **实现不同引擎**：分别实现MobileEngine和PCEngine
3. **工厂模式**：使用EngineFactory根据配置创建合适的引擎实例
4. **依赖注入**：在初始化时根据构建变体注入相应的引擎实现

##### 详细实现

**引擎接口设计**：

```java
/**
 * 杀毒引擎接口
 * 定义了所有杀毒引擎必须实现的基本功能
 */
public interface IEngine {
    /**
     * 初始化引擎
     * @param context 应用上下文
     * @return 初始化结果，true表示成功，false表示失败
     */
    boolean initialize(Context context);
    
    /**
     * 扫描文件
     * @param filePath 文件路径
     * @return 扫描结果
     */
    ResultScan scan(String filePath);
    
    /**
     * 获取引擎版本
     * @return 引擎版本号
     */
    String getVersion();
    
    /**
     * 获取病毒库版本
     * @return 病毒库版本号
     */
    String getVdbVersion();
    
    /**
     * 释放引擎资源
     */
    void release();
}
```

**移动引擎实现**：

```java
/**
 * 移动引擎实现
 * 负责Android平台特有文件（如APK、DEX等）的扫描
 */
public class MobileEngineImpl implements IEngine {
    private Context context;
    
    public MobileEngineImpl(Context context) {
        this.context = context;
    }
    
    @Override
    public boolean initialize(Context context) {
        // 调用现有的AVLCoreEngine初始化逻辑
        return AVLCoreEngine.getInstance().init(...) == 0;
    }
    
    @Override
    public ResultScan scan(String filePath) {
        // 调用现有的移动引擎扫描逻辑
        return MobileEngine.scan(filePath);
    }
    
    // 其他方法实现...
}
```

**PC引擎实现**：

```java
/**
 * PC引擎实现
 * 负责PC平台文件的扫描
 */
public class PCEngineImpl implements IEngine {
    private Context context;
    
    public PCEngineImpl(Context context) {
        this.context = context;
    }
    
    @Override
    public boolean initialize(Context context) {
        // 调用现有的PC引擎初始化逻辑
        AVLEnginePC.getInstance().prepareData(context);
        return AVLEnginePC.getInstance().init() == 0;
    }
    
    @Override
    public ResultScan scan(String filePath) {
        // 调用现有的PC引擎扫描逻辑
        return PcEngine.scan(filePath);
    }
    
    // 其他方法实现...
}
```

**空引擎实现**（用于不需要PC引擎的变体）：

```java
/**
 * 空引擎实现
 * 用于不需要PC引擎功能的变体
 */
public class NullPCEngineImpl implements IEngine {
    
    @Override
    public boolean initialize(Context context) {
        // 不需要实际初始化，直接返回成功
        return true;
    }
    
    @Override
    public ResultScan scan(String filePath) {
        // 返回一个空的扫描结果，表示该引擎不支持扫描
        ResultScan result = new ResultScan();
        result.isMalicious = false;
        result.errMsg = "PC Engine not available in this version";
        return result;
    }
    
    // 其他方法实现...
}
```

**引擎工厂**：

```java
/**
 * 引擎工厂类
 * 负责根据构建变体和配置创建合适的引擎实例
 */
public class EngineFactory {
    
    /**
     * 创建移动引擎
     * @param context 应用上下文
     * @return 移动引擎实例
     */
    public static IEngine createMobileEngine(Context context) {
        return new MobileEngineImpl(context);
    }
    
    /**
     * 创建PC引擎
     * @param context 应用上下文
     * @return PC引擎实例，如果当前变体不支持PC引擎，则返回空实现
     */
    public static IEngine createPCEngine(Context context) {
        // 根据BuildConfig判断当前变体是否支持PC引擎
        if (BuildConfig.FLAVOR_engine.equals("withPc")) {
            return new PCEngineImpl(context);
        } else {
            // 返回空实现，确保接口一致性
            return new NullPCEngineImpl();
        }
    }
}
```

**引擎管理器**：

```java
/**
 * 引擎管理器
 * 负责协调不同引擎的使用
 */
public class EngineManager {
    private static EngineManager instance;
    
    private IEngine mobileEngine;
    private IEngine pcEngine;
    private Context context;
    
    private EngineManager(Context context) {
        this.context = context.getApplicationContext();
        this.mobileEngine = EngineFactory.createMobileEngine(context);
        this.pcEngine = EngineFactory.createPCEngine(context);
    }
    
    public static synchronized EngineManager getInstance(Context context) {
        if (instance == null) {
            instance = new EngineManager(context);
        }
        return instance;
    }
    
    /**
     * 初始化所有引擎
     * @return 初始化结果
     */
    public boolean initialize() {
        boolean mobileResult = mobileEngine.initialize(context);
        boolean pcResult = pcEngine.initialize(context);
        
        // 移动引擎必须初始化成功，PC引擎可选
        return mobileResult;
    }
    
    /**
     * 扫描文件
     * @param filePath 文件路径
     * @return 扫描结果
     */
    public ResultScan scan(String filePath) {
        File file = new File(filePath);
        
        // 根据文件类型选择合适的引擎
        if (FileChecker.getInstance().isApkOrJarOrDex(file)) {
            // 使用移动引擎扫描
            return mobileEngine.scan(filePath);
        } else {
            // 使用PC引擎扫描
            return pcEngine.scan(filePath);
        }
    }
    
    /**
     * 释放所有引擎资源
     */
    public void release() {
        mobileEngine.release();
        pcEngine.release();
    }
    
    // 其他方法...
}
```

##### 优点

- 代码结构清晰，符合面向接口编程原则
- 便于扩展新的引擎类型
- 运行时行为一致性好
- 测试友好，可以轻松创建模拟实现用于测试

##### 缺点

- **不减少包体积**：即使使用接口抽象，在withoutPc变体中，PC引擎相关的资源文件（如avlsdk_pc.zip）和本地库（如libavlsdk_pc.so）仍会被打包到AAR中
- 只是在运行时通过逻辑控制不使用PC引擎，但相关代码和资源仍然存在

#### 方案二：编译时排除

编译时排除策略通过Gradle的sourceSet和变体配置，在编译阶段就完全排除不需要的代码和资源，从而减小最终包的体积。

##### 详细实现

**1. 创建源代码目录结构**：

```
avl_sdk/
  └── src/
      ├── main/                    # 共享代码
      │   └── java/
      │       └── com/antiy/avlsdk/
      │           └── common/      # 通用代码
      ├── withPc/                  # 带PC引擎的变体特有代码
      │   └── java/
      │       └── com/antiy/avlsdk/
      │           └── engine/      # PC引擎相关代码
      └── withoutPc/               # 不带PC引擎的变体特有代码
          └── java/
              └── com/antiy/avlsdk/
                  └── engine/      # 无PC引擎的替代实现
```

**2. Gradle配置**：

```gradle
android {
    // 定义产品变体维度
    flavorDimensions "client", "engine"
    
    productFlavors {
        // 客户端维度
        base {
            dimension "client"
        }
        greatwall {
            dimension "client"
        }
        changan {
            dimension "client"
        }
        
        // 引擎维度
        withPc {
            dimension "engine"
        }
        withoutPc {
            dimension "engine"
        }
    }
    
    // 配置源集
    sourceSets {
        // 带PC引擎的变体
        withPc {
            java.srcDirs = ['src/withPc/java']
            resources.srcDirs = ['src/withPc/resources']
            assets.srcDirs = ['src/withPc/assets']
            jniLibs.srcDirs = ['src/withPc/jniLibs']
        }
        
        // 不带PC引擎的变体
        withoutPc {
            java.srcDirs = ['src/withoutPc/java']
            resources.srcDirs = ['src/withoutPc/resources']
            assets.srcDirs = ['src/withoutPc/assets']
            jniLibs.srcDirs = ['src/withoutPc/jniLibs']
        }
    }
}

dependencies {
    // 基础依赖
    implementation "androidx.core:core-ktx:1.1.0"
    // ...
    
    // PC引擎依赖，只在withPc变体中包含
    withPcImplementation project(path: ':avl_pc')
    
    // 打包AAR时的依赖处理
    def isAarBuildTask = gradle.startParameter.taskNames.any { taskName ->
        taskName.contains('buildAar')
    }
    
    if (isAarBuildTask) {
        // 只在withPc变体中嵌入PC引擎
        withPcEmbed project(path: ':avl_pc', configuration: 'default')
    }
}
```

**3. 实现不同变体的代码**：

在`src/withPc/java`目录中：

```java
// PCEngineWrapper.java
package com.antiy.avlsdk.engine;

import com.antiy.avlsdk.pc.AVLEnginePC;

public class PCEngineWrapper {
    public static boolean isAvailable() {
        return true; // PC引擎可用
    }
    
    public static void initialize(Context context) {
        AVLEnginePC.getInstance().prepareData(context);
        AVLEnginePC.getInstance().init();
    }
    
    public static String scan(String filePath) {
        return AVLEnginePC.getInstance().scan(filePath);
    }
    
    public static void release() {
        AVLEnginePC.getInstance().unloadEngine();
    }
}
```

在`src/withoutPc/java`目录中：

```java
// PCEngineWrapper.java
package com.antiy.avlsdk.engine;

public class PCEngineWrapper {
    public static boolean isAvailable() {
        return false; // PC引擎不可用
    }
    
    public static void initialize(Context context) {
        // 空实现
    }
    
    public static String scan(String filePath) {
        return ""; // 返回空结果
    }
    
    public static void release() {
        // 空实现
    }
}
```

**4. 在共享代码中使用包装类**：

```java
// AVLEngine.java
public static ResultInit init(Context context, String id, Logger logger, NetworkTunnel tunnel) {
    // ...
    
    // 初始化移动引擎
    int mobileResult = initMobileEngine(id);
    boolean value = mobileResult == InitValue.ALREADY_INITIALIZED.getCode() || mobileResult == InitValue.SUCCESS.getCode();
    resultInit.isSuccess = value;
    resultInit.reason = value ? "mobile engine success" : "mobile engin init auth fail";
    if (!value) return resultInit;
    
    // 使用包装类检查PC引擎是否可用
    if (PCEngineWrapper.isAvailable()) {
        PCEngineWrapper.initialize(context);
        prepareUpdateCheckerExe(context);
    }
    
    // ...
}
```

##### 优点

- **显著减少包体积**：不带PC引擎的变体完全不包含PC引擎相关的代码、资源和本地库
- **编译时决定**：在编译阶段就决定包含哪些代码，而不是运行时通过逻辑控制
- **实现简单**：不需要复杂的接口设计，只需要简单的包装类

##### 缺点

- 代码可能需要重复（在不同的源集中）
- 不如接口抽象方案灵活，扩展性较差
- 需要维护多个源代码目录

### 3.1.3 方案比较与推荐

| 特性 | 接口抽象策略 | 编译时排除策略 |
|------|------------|---------------|
| 代码结构 | 清晰，符合OOP原则 | 简单，但可能有重复 |
| 包体积 | 不减少，所有代码和资源都会包含 | 显著减少，完全排除不需要的代码和资源 |
| 扩展性 | 强，易于添加新引擎类型 | 弱，添加新变体需要创建新的源集 |
| 实现复杂度 | 中等，需要设计接口和实现类 | 低，主要是Gradle配置 |
| 测试友好度 | 高，易于模拟测试 | 中等 |

**推荐方案**：

考虑到包体积对移动应用的重要性，以及您的需求主要是区分带PC引擎和不带PC引擎的版本，推荐采用**编译时排除策略**。

这种方案可以显著减少不带PC引擎变体的包体积，同时实现方式相对简单。虽然扩展性不如接口抽象方案，但对于当前的需求来说已经足够，而且未来如果需要更复杂的变体管理，也可以在这个基础上逐步引入接口抽象的设计。

### 3.2 具体实现方案

#### 3.2.1 引擎接口设计

```java
public interface IEngine {
    /**
     * 初始化引擎
     * @return 初始化结果
     */
    boolean initialize(Context context);
    
    /**
     * 扫描文件
     * @param filePath 文件路径
     * @return 扫描结果
     */
    ResultScan scan(String filePath);
    
    /**
     * 获取引擎版本
     * @return 引擎版本
     */
    String getVersion();
    
    /**
     * 释放引擎资源
     */
    void release();
}
```

#### 3.2.2 Gradle变体维度设计

在现有flavor基础上，增加一个新的维度"engine"：

```gradle
// 定义产品变体维度
flavorDimensions "client", "engine"

productFlavors {
    // 客户端维度
    base {
        dimension "client"
    }
    greatwall {
        dimension "client"
    }
    changan {
        dimension "client"
    }
    
    // 引擎维度
    withPc {
        dimension "engine"
    }
    withoutPc {
        dimension "engine"
    }
}
```

#### 3.2.3 条件依赖配置

```gradle
dependencies {
    // 基础依赖...
    
    // PC引擎依赖
    withPcImplementation project(path: ':avl_pc')
    
    // 打包AAR时的依赖处理
    def isAarBuildTask = gradle.startParameter.taskNames.any { taskName ->
        taskName.contains('buildAar')
    }
    
    if (isAarBuildTask) {
        withPcEmbed project(path: ':avl_pc', configuration: 'default')
    }
}
```

#### 3.2.4 引擎工厂实现

```java
public class EngineFactory {
    public static IEngine createEngine(Context context, String engineType) {
        if ("pc".equals(engineType) && BuildConfig.FLAVOR_engine.equals("withPc")) {
            return new PCEngineImpl(context);
        } else {
            return new MobileEngineImpl(context);
        }
    }
}
```

## 4. Jenkins配置方案

### 4.1 参数化构建配置

在Jenkins Job中配置以下参数：

1. **ENGINE_TYPE**：选择引擎类型
   - 选项：`withPc`（默认）, `withoutPc`
   
2. **CLIENT_TYPE**：选择客户端类型
   - 选项：`base`（默认）, `greatwall`, `changan`

3. **BUILD_TYPE**：构建类型
   - 选项：`release`（默认）, `debug`

### 4.2 构建脚本

```groovy
pipeline {
    agent any
    
    parameters {
        choice(name: 'ENGINE_TYPE', choices: ['withPc', 'withoutPc'], description: '选择引擎类型')
        choice(name: 'CLIENT_TYPE', choices: ['base', 'greatwall', 'changan'], description: '选择客户端类型')
        choice(name: 'BUILD_TYPE', choices: ['release', 'debug'], description: '构建类型')
    }
    
    stages {
        stage('Prepare') {
            steps {
                // 清理工作区
                sh 'chmod +x ./gradlew'
                sh './gradlew clean'
            }
        }
        
        stage('Build') {
            steps {
                // 根据参数构建特定变体
                sh "./gradlew :avl_sdk:assemble${params.CLIENT_TYPE.capitalize()}${params.ENGINE_TYPE.capitalize()}${params.BUILD_TYPE.capitalize()}"
            }
        }
        
        stage('Archive') {
            steps {
                // 归档构建产物
                archiveArtifacts artifacts: 'avl_sdk/build/outputs/aar/*.aar', fingerprint: true
            }
        }
    }
}
```

### 4.3 Gradle任务配置

在avl_sdk模块的build.gradle中添加自定义任务：

```gradle
// 构建特定变体的AAR
android.libraryVariants.all { variant ->
    def variantName = variant.name
    def flavorName = variant.flavorName
    def buildType = variant.buildType.name
    
    // 创建构建任务
    tasks.register("buildAar${variantName.capitalize()}") {
        dependsOn "assemble${variantName.capitalize()}"
        finalizedBy 'copyAarToTestDir'
        
        group = 'AVL SDK'
        description = "构建 ${flavorName} ${buildType} AAR"
        
        doLast {
            println """
                ===================================
                ${flavorName} ${buildType} AAR 构建完成！
                输出目录: ${project.buildDir}/outputs/aar
                复制目录: ${project.projectDir}/../tests
                ===================================
            """
        }
    }
}
```

## 5. 实现步骤

### 5.1 代码重构步骤

1. **创建引擎接口**：实现IEngine接口及其实现类
2. **修改AVLEngine**：重构AVLEngine，使用工厂模式创建引擎
3. **调整扫描逻辑**：修改FileScanner，根据引擎类型选择扫描方式
4. **配置Gradle变体**：在build.gradle中添加engine维度和相应flavor

### 5.2 Jenkins配置步骤

1. **创建参数化构建**：配置ENGINE_TYPE、CLIENT_TYPE等参数
2. **编写构建脚本**：创建Jenkinsfile或配置Jenkins Job
3. **配置归档策略**：设置构建产物的归档路径和规则

## 6. 未来扩展性考虑

本方案设计时考虑了未来可能的扩展需求：

1. **新增变体**：可以通过添加新的flavor轻松支持无广告版、高通芯片专用版等变体
2. **新增引擎**：通过实现IEngine接口可以轻松集成新的引擎类型
3. **动态配置**：可以通过配置文件或远程配置动态控制引擎行为

## 7. 总结

本方案通过接口抽象和多维度变体设计，实现了SDK的灵活构建，可以根据需求生成带PC检测或不带PC检测的版本。同时，通过Jenkins参数化构建，简化了构建流程，提高了开发效率。方案具有良好的扩展性，可以满足未来新增变体的需求。
