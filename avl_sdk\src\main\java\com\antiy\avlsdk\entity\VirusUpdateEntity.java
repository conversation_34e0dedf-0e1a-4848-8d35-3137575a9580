package com.antiy.avlsdk.entity;

/**
 * Author: wangbiao
 * Date: 2024/10/8 16:40
 * Description: 更新实体
 */
public class VirusUpdateEntity {
    // 病毒库下载地址
    private String download_path;
    private String hash;
    // 1表示需要更新，2表示无需更新
    private String status;
    // 病毒库更新版本
    private String version;
    //
    //0是空包，1是增量，2是全量 示例：1_0
    private String istotal;

    public String getDownloadUrl() {
        return download_path;
    }

    public void setDownloadUrl(String downloadUrl) {
        this.download_path = downloadUrl;
    }

    public String getHash() {
        return hash;
    }

    public void setHash(String hash) {
        this.hash = hash;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public boolean isUpdated() {
        return status.equals("1");
    }

    public String getIstotal() {
        return istotal;
    }

    public void setIstotal(String istotal) {
        this.istotal = istotal;
    }

    @Override
    public String toString() {
        return "VirusUpdateEntity{" +
                "download_path='" + download_path + '\'' +
                ", hash='" + hash + '\'' +
                ", status='" + status + '\'' +
                ", version='" + version + '\'' +
                ", istotal='" + istotal + '\'' +
                '}';
    }
}
