<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="16dp">

    <TextView
        android:id="@+id/tvDialogTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="设置缓存条目数限制"
        android:textColor="@color/black"
        android:textSize="18sp"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvDescription"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="设置缓存结果的最大条目数"
        android:textColor="@color/gray"
        android:textSize="14sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvDialogTitle" />

    <LinearLayout
        android:id="@+id/layoutInput"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        app:layout_constraintTop_toBottomOf="@id/tvDescription">

        <EditText
            android:id="@+id/editCacheCount"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:background="@drawable/bg_edittext_rounded"
            android:hint="请输入缓存条目数"
            android:inputType="number"
            android:paddingHorizontal="12dp"
            android:textSize="14sp"
            android:maxLength="8" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnSave"
            android:layout_width="wrap_content"
            android:layout_height="48dp"
            android:text="保存"
            android:layout_marginStart="12dp"/>
    </LinearLayout>

    <TextView
        android:id="@+id/tvRange"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="范围: 100 - 100000 条"
        android:textColor="@color/gray"
        android:textSize="12sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layoutInput" />

    <Button
        android:id="@+id/btnCancel"
        style="@style/Widget.MaterialComponents.Button.TextButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:text="取消"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvRange" />

</androidx.constraintlayout.widget.ConstraintLayout>