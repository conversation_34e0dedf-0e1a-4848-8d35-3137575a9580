package com.antiy.avlsdk.entity;

/**
 * 行为特征枚举
 * 用于标识不同的病毒行为特征，每个枚举值包含对应的字符串代码
 */
public enum BehaviorCode {
    PRV("prv"),   // 窃取隐私信息
    RMT("rmt"),   // 接收远程指令
    PAY("pay"),   // 恶意扣费
    SPR("spr"),   // 自动扩散恶意代码
    EXP("exp"),   // 消耗流量或发送短信
    SYS("sys"),   // 破坏系统
    FRA("fra"),   // 伪装或透明欺诈
    ROG("rog"),   // 恶意占用系统资源或弹出广告
    ADS("ads"),   // 推送广告
    SMS("sms"),   // 频繁发送短信
    SPY("spy"),   // 间谍或窃取大量隐私信息
    BKD("bkd"),   // 后门风险
    RTT("rtt"),   // 私自提权
    LCK("lck");   // 锁屏勒索

    private final String value;

    BehaviorCode(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static BehaviorCode fromString(String text) {
        if (text != null) {
            for (BehaviorCode code : BehaviorCode.values()) {
                if (code.value.equalsIgnoreCase(text)) {
                    return code;
                }
            }
        }
        return null;
    }
}

