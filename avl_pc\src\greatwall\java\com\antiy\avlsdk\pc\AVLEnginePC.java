package com.antiy.avlsdk.pc;

import static com.antiy.avlsdk.pc.PCConstant.VENDOR_DIR;

import android.content.Context;
import android.util.Log;
import android.util.Pair;

import java.io.File;
import java.io.IOException;

/**
 * VirtualApp Native Project
 */
public class AVLEnginePC {

    private static final String TAG = AVLEnginePC.class.getSimpleName();
    private final String TARGET_DIR = "avlsdk_pc";

    private final String VIDS_DIR = "/data/vids";

    private static final String ZIP_SUFFIX = ".zip";

    private static volatile AVLEnginePC mInstance;
    private static String mAVLPath;

    public String getAVLPCPath() {
        return mAVLPath;
    }

    static {
        try {
            System.loadLibrary("avlsdk_pc");
        } catch (Throwable e) {
            Log.d(TAG, e.getMessage());
        }
    }

    private AVLEnginePC() {
    }

    public static AVLEnginePC getInstance() {
        if (mInstance == null) {
            synchronized (AVLEnginePC.class) {
                if (mInstance == null) {
                    mInstance = new AVLEnginePC();
                }
            }
        }

        return mInstance;
    }

    /**
     * 准备 PC 引擎运行所需的数据文件
     * 基于 version.conf 文件的智能解压机制
     *
     * @param context Android 上下文对象
     *                <p>
     *                功能说明：
     *                1. 读取 zip 包内 version.conf 文件的时间戳
     *                2. 与目标解压目录的最后修改时间对比
     *                3. 如果 version.conf 时间戳更大，则完全清理目标目录后重新解压
     *                4. 否则跳过解压，使用现有文件
     *                <p>
     *                注意事项：
     *                - 该方法为耗时操作，建议在子线程中调用
     *                - 使用智能解压机制，避免不必要的重复解压
     *                - 解压失败时会打印异常堆栈，但不会抛出异常
     */
    public void prepareData(Context context) {
        // 准备 PC 引擎数据
        File avlsdkPc = new File(VIDS_DIR + File.separator + TARGET_DIR);
        mAVLPath = avlsdkPc.getAbsolutePath();

        Log.i(TAG, "开始准备 PC 引擎数据");
        Log.i(TAG, "目标目录：" + mAVLPath);
        Log.i(TAG, "avlsdkPc exist: " + avlsdkPc.exists());
        Log.i(TAG, "avlsdkPc HasFiles: " + AssetsUtil.isFolderHasFiles(mAVLPath));
        Log.i(TAG, "FLAVOR: " + BuildConfig.FLAVOR);

        try {
            // 长城版本：使用智能解压机制从 vendor 解压
            Log.i(TAG, "长城版本：使用智能解压机制从 vendor 解压");
            SmartExtractionUtil.smartExtractFromVendor(
                VENDOR_DIR + TARGET_DIR + ZIP_SUFFIX,
                VIDS_DIR
            );
        } catch (IOException e) {
            Log.e(TAG, "PC 引擎数据准备失败", e);
            e.printStackTrace();
        }
    }

    public int init() {
        return loadEngine(mAVLPath);
    }

    //ret:
    //0: success, -1: 失败（后面再完善错误码）
    public native int loadEngine(String sdk_dir);

    public native void unloadEngine();

    public native String scan(String filePath);

    public native Pair<String, byte[]> scanWithMd5(String filePath);

    public native void setScanEnabled(boolean enabled);

    public native String getDbInfo();

    public native String getCurVersion();
}
