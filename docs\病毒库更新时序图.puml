@startuml
title 病毒库更新流程

actor User
participant "AVLEngine" as Engine
participant "VirusDatabaseUpdater" as Updater
participant "NetworkTunnel" as Network
participant "UpdateCoordinator" as Coordinator
participant "UpdateStrategyFactory" as Factory
participant "PCFullUpdateStrategy" as PCStrategy
participant "AVLEnginePC" as PCSDK
participant "AVLCoreEngine" as AndroidSDK

User -> Engine: checkUpdate()
activate Engine

Engine -> Updater: checkUpdate()
activate Updater

Updater -> Network: download(confUrl, callback)
activate Network
Network --> Updater: onFinish(code, fileName, localPath)
deactivate Network

Updater -> Updater: 解析配置文件为VirusUpdateEntity
Updater -> Updater: 比较版本

alt 有新版本
    Updater -> Updater: downloadAndUpdate(virusUpdate)
    activate Updater

    Updater -> Network: download(downloadUrl, callback)
    activate Network
    Network --> Updater: onFinish(code, fileName, localPath)
    deactivate Network

    Updater -> Updater: 计算下载文件哈希值

    alt 哈希值匹配
        Updater -> Coordinator: handleUpdate(virusUpdate, localFilePath, targetDir)
        activate Coordinator

        Coordinator -> Coordinator: parseUpdateTypes(isTotal, version)
        Coordinator -> Coordinator: extractPackages(targetDir, localFilePath)
        Coordinator -> Coordinator: generatePath(entity, pair, totalZipPath)

        alt 需要更新PC
            Coordinator -> Factory: createStrategy(type, PlatformType.PC)
            Factory --> Coordinator: PCStrategy实例

            Coordinator -> PCStrategy: performUpdate(packagePath, pair)
            activate PCStrategy

            PCStrategy -> PCSDK: unloadEngine()
            PCStrategy -> PCStrategy: renameAvlPcDir()
            PCStrategy -> PCStrategy: extractZipPackage(packagePath, pcEngineDirectory)
            PCStrategy -> PCStrategy: 复制license文件
            PCStrategy -> PCStrategy: pcVirusUpdateCheck()

            alt 检查成功
                PCStrategy -> PCSDK: init()
                PCSDK --> PCStrategy: 初始化结果
            else 检查失败
                PCStrategy -> PCStrategy: resetPcEngine()
            end

            PCStrategy --> Coordinator: 更新结果
            deactivate PCStrategy
        end

        alt 需要更新Android
            Coordinator -> Factory: createStrategy(type, PlatformType.ANDROID)
            Factory --> Coordinator: AndroidStrategy实例

            Coordinator -> AndroidSDK: installPackage(packagePath, 1, packageName, installType)
            AndroidSDK --> Coordinator: 安装结果
        end

        alt 更新成功
            Coordinator -> PCStrategy: cleanupTempFiles()
        end

        Coordinator --> Updater: ResultUpdate对象
        deactivate Coordinator
    else 哈希值不匹配
        Updater -> Updater: 返回更新失败
    end

    deactivate Updater
else 无新版本
    Updater -> Updater: 返回无更新
end

Updater --> Engine: ResultUpdate对象
deactivate Updater

Engine --> User: ResultUpdate对象
deactivate Engine

@enduml