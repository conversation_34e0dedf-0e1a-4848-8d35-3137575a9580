package com.antiy.demo.activity

import android.content.Context
import android.content.Intent
import android.os.Build
import android.view.LayoutInflater
import android.view.WindowManager
import androidx.core.content.ContextCompat
import androidx.core.view.WindowCompat
import com.antiy.avlsdk.AVLEngine
import com.antiy.demo.base.BaseActivity
import com.antiy.demo.databinding.ActivityPerformanceSettingsBinding

class PerformanceSettingsActivity : BaseActivity<ActivityPerformanceSettingsBinding>() {
    
    companion object {
        private const val PREFS_NAME = "performance_settings"
        private const val KEY_CPU_LIMIT_ENABLED = "cpu_limit_enabled"
        private const val KEY_CPU_LIMIT_VALUE = "cpu_limit_value"
        private const val KEY_BATTERY_SAVE_ENABLED = "battery_save_enabled"
        private const val DEFAULT_CPU_LIMIT = 50 // 默认CPU限制为50%
        
        // 启动Activity的静态方法
        fun start(context: Context) {
            context.startActivity(Intent(context, PerformanceSettingsActivity::class.java))
        }
    }
    
    override fun getViewBinding(inflater: LayoutInflater): ActivityPerformanceSettingsBinding {
        return ActivityPerformanceSettingsBinding.inflate(inflater)
    }

    override fun initView() {
        super.initView()
        setupStatusBar()
        setupToolbar()
        setupCpuLimit()
    }

    private fun setupStatusBar() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            WindowCompat.getInsetsController(window, window.decorView)?.apply {
                isAppearanceLightStatusBars = true
            }
            
            window.statusBarColor = ContextCompat.getColor(this, android.R.color.transparent)
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
            WindowCompat.setDecorFitsSystemWindows(window, false)
        }
    }

    private fun setupToolbar() {
        binding.btnBack.setOnClickListener {
            finish()
        }
    }

    private fun setupCpuLimit() {
        // 从SharedPreferences加载设置
        val sharedPrefs = getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        
        // 设置CPU限制开关的初始状态
        binding.switchCpuLimit.isChecked = sharedPrefs.getBoolean(KEY_CPU_LIMIT_ENABLED, false)
        
        // 设置电池模式开关的初始状态
        binding.switchBatterySave.isChecked = sharedPrefs.getBoolean(KEY_BATTERY_SAVE_ENABLED, false)
        
        // 设置CPU限制滑块的初始值
        val cpuLimit = sharedPrefs.getInt(KEY_CPU_LIMIT_VALUE, DEFAULT_CPU_LIMIT)
        binding.sliderCpuLimit.value = cpuLimit.toFloat()
        updateCpuLimitText(cpuLimit)
        
        // 设置CPU限制开关的监听器
        binding.switchCpuLimit.setOnCheckedChangeListener { _, isChecked ->
        }
        
        // 设置电池模式开关的监听器
        binding.switchBatterySave.setOnCheckedChangeListener { _, isChecked ->
        }
        
        // 设置CPU限制滑块的监听器
        binding.sliderCpuLimit.addOnChangeListener { _, value, _ ->
            AVLEngine.Logger.info("CPU Value change: $value")
            val limitValue = value.toInt()
            updateCpuLimitText(limitValue)
            
            // 保存CPU限制值
            sharedPrefs.edit().putInt(KEY_CPU_LIMIT_VALUE, limitValue).apply()
            
            // 更新杀毒引擎配置
            AVLEngine.getInstance().updateConfig { config ->
                config.updateSilentThreshold(limitValue)
            }
        }
        
        // 初始设置滑块的启用状态
        binding.sliderCpuLimit.isEnabled = binding.switchCpuLimit.isChecked
    }
    
    private fun updateCpuLimitText(value: Int) {
        binding.tvCpuLimitValue.text = "CPU使用率上限: $value%"
    }
} 