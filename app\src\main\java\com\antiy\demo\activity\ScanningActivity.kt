package com.antiy.demo.activity

import ScanResultItem
import android.content.Context
import android.content.Intent
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.widget.Toast
import androidx.recyclerview.widget.DefaultItemAnimator
import androidx.recyclerview.widget.LinearLayoutManager
import com.antiy.avlsdk.AVLEngine
import com.antiy.avlsdk.callback.ScanListener
import com.antiy.avlsdk.entity.ResultScan
import com.antiy.avlsdk.entity.RiskLevel
import com.antiy.avlsdk.entity.ScanErrorType
import com.antiy.demo.adapter.ScanResultAdapter
import com.antiy.demo.base.BaseActivity
import com.antiy.demo.databinding.ActivityScanningBinding
import com.antiy.demo.model.ScanThreatData
import java.io.File
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.concurrent.thread


class ScanningActivity : BaseActivity<ActivityScanningBinding>() {
    
    companion object {
        const val EXTRA_SCAN_TYPE = "scan_type"
        const val EXTRA_APP_PATHS = "app_paths"  // 应用路径列表的Extra键
        const val EXTRA_APP_NAMES = "app_names"  // 应用名称列表的Extra键
        const val EXTRA_PACKAGE_NAMES = "package_names"  // 包名列表的Extra键
        const val SCAN_TYPE_QUICK = 0
        const val SCAN_TYPE_FULL = 1
        const val SCAN_TYPE_CUSTOM = 2
        
        private const val TAG = "ScanningActivity"
    }

    private val scanResultAdapter = ScanResultAdapter()
    private var totalFiles = 0
    private var currentIndex = 0
    private var startTime = System.currentTimeMillis()
    private var scannedFiles = 0
    private var threats = 0
    
    // 扫描控制变量
    private var scanThread: Thread? = null
    private val isPaused = AtomicBoolean(false)
    private val isStopped = AtomicBoolean(false)
    private var appPathsToScan: ArrayList<String>? = null
    private var appNamesToScan: ArrayList<String>? = null
    private var packageNamesToScan: ArrayList<String>? = null
    private var currentScanType = SCAN_TYPE_QUICK
    
    // 保存应用路径到应用名称的映射关系
    private val appPathToNameMap = mutableMapOf<String, String>()
    
    private val scanResults = mutableListOf<ScanThreatData>()
    
    override fun getViewBinding(inflater: LayoutInflater): ActivityScanningBinding {
        return ActivityScanningBinding.inflate(inflater)
    }

    override fun initView() {
        super.initView()
        setupRecyclerView()
        setupButtons()
        updateProgress()
        thread { scanDir() }
    }

    private fun scanDir() {
        currentScanType = intent.getIntExtra(EXTRA_SCAN_TYPE, SCAN_TYPE_QUICK)
        
        // 检查是否有应用路径列表（用于快速扫描）
        val appPaths = intent.getStringArrayListExtra(EXTRA_APP_PATHS)
        val appNames = intent.getStringArrayListExtra(EXTRA_APP_NAMES)
        val packageNames = intent.getStringArrayListExtra(EXTRA_PACKAGE_NAMES)
        
        appPathsToScan = appPaths
        appNamesToScan = appNames
        packageNamesToScan = packageNames
        
        // 创建应用路径到名称的映射
        if (appPaths != null && appNames != null && appPaths.size == appNames.size) {
            for (i in appPaths.indices) {
                appPathToNameMap[appPaths[i]] = appNames[i]
            }
        }
        
        if (currentScanType == SCAN_TYPE_QUICK && !appPaths.isNullOrEmpty()) {
            // 使用应用路径列表进行扫描
            scanAppList(appPaths)
            return
        }
        
        // 如果没有应用路径列表或不是快速扫描，则使用原有逻辑
        val path = when (currentScanType) {
            SCAN_TYPE_QUICK -> "/sdcard/samples/apk/100"
            SCAN_TYPE_FULL -> "/sdcard/Download"
            SCAN_TYPE_CUSTOM -> {
                // 从SharedPreferences获取自定义路径
                val customPath = getSharedPreferences("antivirus_settings", Context.MODE_PRIVATE)
                    .getString("custom_scan_path", null)
                
                if (customPath.isNullOrEmpty()) {
                    // 如果没有设置自定义路径，使用默认路径
                    "/storage/emulated/0"
                } else {
                    // 使用用户设置的自定义路径
                    customPath
                }
            }
            else -> "/storage/emulated/0"
        }

        // 更新UI显示当前扫描路径
        binding.tvCurrentFile.text = "准备扫描: $path"

        AVLEngine.getInstance().scanDir(path, createScanListener())
    }
    
    /**
     * 扫描应用列表
     */
    private fun scanAppList(appPaths: ArrayList<String>) {
        Log.d(TAG, "开始扫描应用列表，共 ${appPaths.size} 个应用")
        
        // 更新UI显示
        binding.tvCurrentFile.text = "准备扫描已安装应用，共 ${appPaths.size} 个"
        totalFiles = appPaths.size
        
        // 创建扫描监听器
        val scanListener = createScanListener()
        
        // 通知扫描开始
        scanListener.scanStart()
        scanListener.scanCount(appPaths.size)
        
        // 重置控制变量
        isPaused.set(false)
        isStopped.set(false)
        
        // 使用线程在后台扫描应用
        scanThread = Thread {
            try {
                // 逐个扫描应用
                for (index in appPaths.indices) {
                    // 检查是否需要停止
                    if (isStopped.get()) {
                        Log.d(TAG, "扫描已停止")
                        scanListener.scanStop()
                        break
                    }
                    
                    // 检查是否需要暂停
                    while (isPaused.get() && !isStopped.get()) {
                        try {
                            Thread.sleep(100) // 暂停时小睡一会儿，减少CPU使用
                        } catch (e: InterruptedException) {
                            Thread.currentThread().interrupt()
                            break
                        }
                    }
                    
                    // 如果在暂停后被停止，则退出循环
                    if (isStopped.get()) {
                        Log.d(TAG, "扫描已停止")
                        scanListener.scanStop()
                        break
                    }
                    
                    val appPath = appPaths[index]
                    
                    // 获取应用名称（如果有）
                    val appName = appPathToNameMap[appPath] ?: appPath.substringAfterLast("/")
                    
                    // 通知开始扫描当前文件，使用应用名称
                    scanListener.scanFileStart(index, "正在扫描：$appName")
                    
                    // 使用AVLEngine扫描单个文件
                    val result = AVLEngine.getInstance().scanFile(appPath)
                    
                    // 通知文件扫描完成
                    scanListener.scanFileFinish(index, appPath, result)
                }
                
                // 如果没有被停止，则通知扫描完成
                if (!isStopped.get()) {
                    Log.d(TAG, "扫描已完成")
                    scanListener.scanFinish()
                }
            } catch (e: Exception) {
                Log.e(TAG, "扫描应用列表时出错", e)
                // 通知扫描停止
                scanListener.scanStop()
            }
        }
        
        // 启动扫描线程
        scanThread?.start()
    }
    
    /**
     * 暂停扫描
     */
    fun pauseScan() {
        if (currentScanType == SCAN_TYPE_QUICK && appPathsToScan != null) {
            // 对于快速扫描（应用列表扫描），设置暂停标志
            isPaused.set(true)
            Log.d(TAG, "应用列表扫描已暂停")
            
            runOnUiThread {
                binding.tvScanStatus.text = "已暂停"
                binding.btnPause.text = "继续"
            }
        } else {
            // 对于其他类型的扫描，使用SDK的暂停方法
            AVLEngine.getInstance().scanPause()
        }
    }
    
    /**
     * 恢复扫描
     */
    fun resumeScan() {
        if (currentScanType == SCAN_TYPE_QUICK && appPathsToScan != null) {
            // 对于快速扫描（应用列表扫描），清除暂停标志
            isPaused.set(false)
            Log.d(TAG, "应用列表扫描已恢复")
            
            runOnUiThread {
                binding.tvScanStatus.text = "正在扫描"
                binding.btnPause.text = "暂停"
            }
        } else {
            // 对于其他类型的扫描，使用SDK的恢复方法
            AVLEngine.getInstance().scanResume()
        }
    }
    
    /**
     * 停止扫描
     */
    fun stopScan() {
        if (currentScanType == SCAN_TYPE_QUICK && appPathsToScan != null) {
            // 对于快速扫描（应用列表扫描），设置停止标志
            isStopped.set(true)
            // 如果当前是暂停状态，也需要清除暂停标志，以便线程可以继续执行并检测到停止标志
            isPaused.set(false)
            
            // 中断扫描线程
            scanThread?.interrupt()
            Log.d(TAG, "应用列表扫描已停止")
        } else {
            // 对于其他类型的扫描，使用SDK的停止方法
            AVLEngine.getInstance().scanStop()
        }
    }
    
    /**
     * 创建扫描监听器
     */
    private fun createScanListener(): ScanListener {
        return object : ScanListener {
            override fun scanStart() {
                // 扫描开始时重置计数
                currentIndex = 0
                startTime = System.currentTimeMillis()
                runOnUiThread {
                    binding.tvProgress.text = "0%"
                    binding.progressCircular.progress = 0
                    
                    // 确保报告按钮不可见
                    binding.btnReport.visibility = View.GONE
                }
            }

            override fun scanStop() {
                runOnUiThread {
                    binding.tvScanStatus.text = "已停止"
                }
            }

            override fun scanFinish() {
                runOnUiThread {
                    binding.tvScanStatus.text = "扫描完成"
                    binding.progressCircular.progress = 100
                    binding.tvProgress.text = "100%"
                    
                    // 禁用暂停按钮
                    binding.btnPause.isEnabled = false
                    
                    // 显示报告按钮
                    binding.btnReport.visibility = View.VISIBLE
                    binding.btnReport.isEnabled = true
                }
            }

            override fun scanCount(count: Int) {
                totalFiles = count
            }

            override fun scanFileStart(index: Int, path: String?) {
                currentIndex = index
                runOnUiThread {
                    path?.let { binding.tvCurrentFile.text = it }
                    AVLEngine.Logger.info("开始扫描第$index 个文件，路径:$path")
                }
            }

            override fun scanFileFinish(index: Int, path: String?, result: ResultScan?) {
                runOnUiThread {
                    // 更新当前文件的扫描结果
                    path?.let {
                        AVLEngine.Logger.info("第$index 个文件扫描完成")
                        updateCurrentFile(it, File(it).length(), result!!)
                    }
                    // 更新进度
                    updateProgress()
                }
            }

            override fun scanError(errorMsg: ScanErrorType?) {
                runOnUiThread {
                    Toast.makeText(this@ScanningActivity, "当前有任务在扫描，请先停止上一个任务", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

    private fun setupRecyclerView() {
        binding.rvScanResults.apply {
            adapter = scanResultAdapter
            layoutManager = LinearLayoutManager(this@ScanningActivity).also { 
                // 设置从底部开始布局
                it.stackFromEnd = true
                // 设置反转布局（最新的在底部）
                it.reverseLayout = true 
            }
            // 添加item动画
            itemAnimator = DefaultItemAnimator()
        }
    }

    private fun updateProgress() {
        if (totalFiles > 0) {
            // 计算进度百分比
            val progress = ((currentIndex).toFloat() / totalFiles.toFloat() * 100).toInt()
            binding.apply {
                progressCircular.progress = progress
                tvProgress.text = "$progress%"
                
                // 更新预计剩余时间
                val elapsedTime = System.currentTimeMillis() - startTime
                if (progress > 0) {
                    val estimatedTotalTime = (elapsedTime.toFloat() / progress.toFloat() * 100).toLong()
                    val remainingTime = estimatedTotalTime - elapsedTime
                    val remainingMinutes = remainingTime / 60000
                    tvTimeRemaining.text = "预计剩余时间: ${remainingMinutes}分钟"
                }
            }
        }
    }

    private fun updateCurrentFile(path: String, size: Long, result: ResultScan) {
        // 如果是快速扫描模式且有应用名称映射，使用应用名称；否则使用文件名
        val displayName = if (currentScanType == SCAN_TYPE_QUICK) {
            appPathToNameMap[path] ?: path.substringAfterLast("/")
        } else {
            path.substringAfterLast("/")
        }
        
        // 创建扫描结果项
        val scanResultItem = ScanResultItem(
            fileName = displayName,
            filePath = path,
            fileSize = size,
            result = result
        )
        
        // 如果是威胁，添加到威胁列表
        if (result.isMalicious) {
            val info = AVLEngine.getInstance().getVirusInfo(result.virusName)
            val threatData = ScanThreatData(
                fileName = displayName,
                filePath = path,
                fileSize = size,
                threatType = result.virusName?: "未知",
                description = info?.suggestion?.description ?: "可能存在安全风险",
                riskLevel = info.riskLevel
            )

            scanResults.add(threatData)
        }
        
        scanResultAdapter.addItem(scanResultItem)
        
        // 更新统计数据
        scannedFiles++
        if (result.isMalicious) threats++
        
        binding.apply {
            tvScannedFiles.text = "已扫描文件\n$scannedFiles"
            tvThreats.text = "发现威胁\n$threats"
            
            // 更新扫描耗时
            val duration = System.currentTimeMillis() - startTime
            val minutes = duration / 60000
            val seconds = (duration % 60000) / 1000
            tvTime.text = "耗时\n${minutes}:${String.format("%02d", seconds)}"

            // 更新当前文件信息
            // 如果是快速扫描，显示"正在扫描：应用名称"
            if (currentScanType == SCAN_TYPE_QUICK) {
                tvCurrentFile.text = "正在扫描：$displayName"
            } else {
                // 否则显示路径
                tvCurrentFile.text = path
            }

            // 滚动到最新添加的项
            rvScanResults.scrollToPosition(0)
        }
    }

    private fun setupButtons() {
        var isPausedState = false
        
        binding.btnPause.setOnClickListener {
            if (isPausedState) {
                // 继续扫描
                resumeScan()
            } else {
                // 暂停扫描
                pauseScan()
            }
            isPausedState = !isPausedState
        }

        binding.btnStop.setOnClickListener {
            // 停止扫描
            stopScan()
            finish()
        }
        
        // 设置报告按钮
        binding.btnReport.apply {
            // 初始状态下不可见
            visibility = View.GONE
            isEnabled = false
            
            // 点击事件
            setOnClickListener {
                navigateToScanResult()
            }
        }
    }
    
    override fun onDestroy() {
        super.onDestroy()
        // 确保在Activity销毁时停止扫描
        stopScan()
    }
    
    /**
     * 跳转到扫描结果页面
     */
    private fun navigateToScanResult() {
        // 计算各风险等级的威胁数量
        val highRiskCount = scanResults.count { it.riskLevel == RiskLevel.HIGH }
        val mediumRiskCount = scanResults.count { it.riskLevel == RiskLevel.MEDIUM }
        val lowRiskCount = scanResults.count { it.riskLevel == RiskLevel.LOW }
        
        val intent = Intent(this, ScanResultActivity::class.java).apply {
            putExtra(ScanResultActivity.EXTRA_HIGH_RISK_COUNT, highRiskCount)
            putExtra(ScanResultActivity.EXTRA_MEDIUM_RISK_COUNT, mediumRiskCount)
            putExtra(ScanResultActivity.EXTRA_LOW_RISK_COUNT, lowRiskCount)
            putParcelableArrayListExtra(ScanResultActivity.EXTRA_SCAN_THREATS, ArrayList(scanResults))
        }
        startActivity(intent)
        finish()
    }
} 