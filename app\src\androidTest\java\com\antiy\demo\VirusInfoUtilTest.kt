package com.antiy.demo

import androidx.test.ext.junit.runners.AndroidJUnit4
import com.antiy.avlsdk.entity.ActionSuggestion
import com.antiy.avlsdk.entity.BehaviorCode
import com.antiy.avlsdk.entity.RiskLevel
import com.antiy.avlsdk.entity.VirusCategory
import com.antiy.avlsdk.utils.VirusInfoUtil
import org.junit.Assert.*
import org.junit.Test
import org.junit.runner.RunWith

@RunWith(AndroidJUnit4::class)
class VirusInfoUtilTest {

    @Test
    fun testParseVirusName() {
        // 测试色情软件
        val pornWareResult = VirusInfoUtil.parseVirusName(
            "PornWare/Android.GSexplayer.hm[exp,rog]"
        )
        assertEquals(VirusCategory.PORN_WARE, pornWareResult.category)
        assertEquals(2, pornWareResult.behaviors.size)
        assertTrue(pornWareResult.behaviors.contains(BehaviorCode.EXP))
        assertTrue(pornWareResult.behaviors.contains(BehaviorCode.ROG))
        assertEquals(RiskLevel.MEDIUM, pornWareResult.riskLevel)
        assertEquals(ActionSuggestion.CLEAN_NOW, pornWareResult.suggestion)

        // 测试木马程序
        val trojanResult = VirusInfoUtil.parseVirusName(
            "Trojan/Android.Spy.hm[prv,rmt,spy]"
        )
        assertEquals(VirusCategory.TROJAN, trojanResult.category)
        assertEquals(3, trojanResult.behaviors.size)
        assertTrue(trojanResult.behaviors.contains(BehaviorCode.PRV))
        assertTrue(trojanResult.behaviors.contains(BehaviorCode.RMT))
        assertTrue(trojanResult.behaviors.contains(BehaviorCode.SPY))
        assertEquals(RiskLevel.HIGH, trojanResult.riskLevel)
        assertEquals(ActionSuggestion.CLEAN_NOW, trojanResult.suggestion)

        // 测试广告软件
        val adWareResult = VirusInfoUtil.parseVirusName(
            "AdWare/Android.AdDisplay.hm[ads,rog]"
        )
        assertEquals(VirusCategory.AD_WARE, adWareResult.category)
        assertEquals(2, adWareResult.behaviors.size)
        assertTrue(adWareResult.behaviors.contains(BehaviorCode.ADS))
        assertTrue(adWareResult.behaviors.contains(BehaviorCode.ROG))
        assertEquals(RiskLevel.LOW, adWareResult.riskLevel)
        assertEquals(ActionSuggestion.USE_CAUTION, adWareResult.suggestion)

        // 测试无行为特征的病毒
        val simpleVirusResult = VirusInfoUtil.parseVirusName(
            "RiskWare/Android.Risk.hm"
        )
        assertEquals(VirusCategory.RISK_WARE, simpleVirusResult.category)
        assertTrue(simpleVirusResult.behaviors.isEmpty())
        assertEquals(RiskLevel.LOW, simpleVirusResult.riskLevel)
        assertEquals(ActionSuggestion.USE_CAUTION, simpleVirusResult.suggestion)

        // 测试未知类型
        val unknownResult = VirusInfoUtil.parseVirusName(
            "Unknown/Android.Unknown.hm[prv]"
        )
        assertNull(unknownResult.category)
        assertEquals(1, unknownResult.behaviors.size)
        assertTrue(unknownResult.behaviors.contains(BehaviorCode.PRV))
        assertEquals(RiskLevel.LOW, unknownResult.riskLevel)
        assertEquals(ActionSuggestion.USE_CAUTION, unknownResult.suggestion)

        // 测试空输入
        val emptyResult = VirusInfoUtil.parseVirusName("")
        assertNull(emptyResult.category)
        assertTrue(emptyResult.behaviors.isEmpty())
        assertEquals(RiskLevel.LOW, emptyResult.riskLevel)
        assertEquals(ActionSuggestion.USE_CAUTION, emptyResult.suggestion)

        // 测试null输入
        val nullResult = VirusInfoUtil.parseVirusName(null)
        assertNull(nullResult.category)
        assertTrue(nullResult.behaviors.isEmpty())
        assertEquals(RiskLevel.LOW, nullResult.riskLevel)
        assertEquals(ActionSuggestion.USE_CAUTION, nullResult.suggestion)
    }
} 