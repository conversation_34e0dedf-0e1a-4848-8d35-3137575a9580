<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PPT导出中心 - CCCJ SDK 开发工作报告</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- PptxGenJS库 -->
    <script src="https://cdn.jsdelivr.net/npm/pptxgenjs@3.12.0/dist/pptxgen.bundle.js"></script>
    <!-- PPT导出模块 -->
    <script src="ppt-exporter.js"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .card-hover {
            transition: all 0.3s ease;
        }
        
        .card-hover:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }
        
        .export-card {
            background: linear-gradient(145deg, #ffffff, #f8fafc);
            border: 1px solid #e2e8f0;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .fade-in {
            animation: fadeIn 0.8s ease-out forwards;
        }
        
        .loading {
            display: none;
        }
        
        .loading.active {
            display: inline-block;
        }
    </style>
</head>
<body class="gradient-bg min-h-screen p-6">
    <div class="max-w-6xl mx-auto">
        <!-- 页面标题 -->
        <div class="text-center mb-12 fade-in">
            <h1 class="text-5xl font-bold text-white mb-4">
                <i class="fas fa-file-export mr-4"></i>
                PPT导出中心
            </h1>
            <p class="text-xl text-white/80 mb-2">CCCJ SDK 开发工作报告</p>
            <p class="text-lg text-white/60">将精美的网页演示文稿导出为PowerPoint格式</p>
        </div>

        <!-- 导出选项卡片 -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
            <!-- 完整演示文稿导出 -->
            <div class="export-card rounded-2xl p-8 card-hover fade-in" style="animation-delay: 0.2s;">
                <div class="text-center mb-6">
                    <div class="bg-green-100 p-4 rounded-full w-20 h-20 mx-auto mb-4 flex items-center justify-center">
                        <i class="fas fa-file-powerpoint text-green-600 text-3xl"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-800 mb-2">完整演示文稿</h3>
                    <p class="text-gray-600">导出包含所有页面的完整PPT文件</p>
                </div>
                
                <div class="space-y-3 mb-6">
                    <div class="flex items-center text-sm text-gray-600">
                        <i class="fas fa-check text-green-500 mr-2"></i>
                        包含第1页：项目概览与核心成果
                    </div>
                    <div class="flex items-center text-sm text-gray-600">
                        <i class="fas fa-check text-green-500 mr-2"></i>
                        包含第2页：技术改进详情与规划
                    </div>
                    <div class="flex items-center text-sm text-gray-600">
                        <i class="fas fa-check text-green-500 mr-2"></i>
                        保持原有设计风格和布局
                    </div>
                </div>
                
                <button onclick="exportFullPresentation()" 
                        class="w-full bg-green-600 hover:bg-green-700 text-white py-4 px-6 rounded-xl font-semibold transition-all duration-300 flex items-center justify-center">
                    <i class="fas fa-download mr-2"></i>
                    <span class="export-text">导出完整演示文稿</span>
                    <i class="fas fa-spinner fa-spin loading ml-2"></i>
                </button>
            </div>

            <!-- 第一页导出 -->
            <div class="export-card rounded-2xl p-8 card-hover fade-in" style="animation-delay: 0.4s;">
                <div class="text-center mb-6">
                    <div class="bg-blue-100 p-4 rounded-full w-20 h-20 mx-auto mb-4 flex items-center justify-center">
                        <i class="fas fa-chart-bar text-blue-600 text-3xl"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-800 mb-2">第一页</h3>
                    <p class="text-gray-600">项目概览与核心成果展示</p>
                </div>
                
                <div class="space-y-3 mb-6">
                    <div class="flex items-center text-sm text-gray-600">
                        <i class="fas fa-chart-line text-blue-500 mr-2"></i>
                        主要指标统计展示
                    </div>
                    <div class="flex items-center text-sm text-gray-600">
                        <i class="fas fa-rocket text-blue-500 mr-2"></i>
                        核心功能增强亮点
                    </div>
                    <div class="flex items-center text-sm text-gray-600">
                        <i class="fas fa-cogs text-blue-500 mr-2"></i>
                        技术改进进度展示
                    </div>
                </div>
                
                <button onclick="exportSingleSlide(1)" 
                        class="w-full bg-blue-600 hover:bg-blue-700 text-white py-4 px-6 rounded-xl font-semibold transition-all duration-300 flex items-center justify-center">
                    <i class="fas fa-download mr-2"></i>
                    <span class="export-text">导出第一页</span>
                    <i class="fas fa-spinner fa-spin loading ml-2"></i>
                </button>
            </div>

            <!-- 第二页导出 -->
            <div class="export-card rounded-2xl p-8 card-hover fade-in" style="animation-delay: 0.6s;">
                <div class="text-center mb-6">
                    <div class="bg-purple-100 p-4 rounded-full w-20 h-20 mx-auto mb-4 flex items-center justify-center">
                        <i class="fas fa-lightbulb text-purple-600 text-3xl"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-800 mb-2">第二页</h3>
                    <p class="text-gray-600">技术改进详情与发展规划</p>
                </div>
                
                <div class="space-y-3 mb-6">
                    <div class="flex items-center text-sm text-gray-600">
                        <i class="fas fa-timeline text-purple-500 mr-2"></i>
                        技术架构改进时间线
                    </div>
                    <div class="flex items-center text-sm text-gray-600">
                        <i class="fas fa-star text-purple-500 mr-2"></i>
                        技术突破亮点展示
                    </div>
                    <div class="flex items-center text-sm text-gray-600">
                        <i class="fas fa-trophy text-purple-500 mr-2"></i>
                        核心技术成果总结
                    </div>
                </div>
                
                <button onclick="exportSingleSlide(2)" 
                        class="w-full bg-purple-600 hover:bg-purple-700 text-white py-4 px-6 rounded-xl font-semibold transition-all duration-300 flex items-center justify-center">
                    <i class="fas fa-download mr-2"></i>
                    <span class="export-text">导出第二页</span>
                    <i class="fas fa-spinner fa-spin loading ml-2"></i>
                </button>
            </div>
        </div>

        <!-- 功能说明 -->
        <div class="bg-white/10 rounded-2xl p-8 mb-8 fade-in" style="animation-delay: 0.8s;">
            <h3 class="text-2xl font-bold text-white mb-6">
                <i class="fas fa-info-circle mr-3"></i>
                功能说明
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-4">
                    <h4 class="text-lg font-semibold text-white">导出特性</h4>
                    <ul class="space-y-2 text-white/80">
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-400 mr-2"></i>
                            生成标准PowerPoint格式(.pptx)
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-400 mr-2"></i>
                            保持原有设计风格和布局
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-400 mr-2"></i>
                            支持单页或完整演示文稿导出
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-400 mr-2"></i>
                            自动生成带时间戳的文件名
                        </li>
                    </ul>
                </div>
                <div class="space-y-4">
                    <h4 class="text-lg font-semibold text-white">使用说明</h4>
                    <ul class="space-y-2 text-white/80">
                        <li class="flex items-center">
                            <i class="fas fa-mouse-pointer text-blue-400 mr-2"></i>
                            点击对应按钮开始导出
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-clock text-blue-400 mr-2"></i>
                            等待几秒钟完成生成
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-download text-blue-400 mr-2"></i>
                            文件将自动下载到本地
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-folder-open text-blue-400 mr-2"></i>
                            可在下载文件夹中找到
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 返回导航 -->
        <div class="text-center fade-in" style="animation-delay: 1s;">
            <div class="flex justify-center gap-4">
                <button onclick="window.location.href='slide1.html'" 
                        class="bg-white/20 hover:bg-white/30 text-white px-6 py-3 rounded-xl transition-all duration-300 flex items-center">
                    <i class="fas fa-arrow-left mr-2"></i>
                    返回第一页
                </button>
                <button onclick="window.location.href='slide2.html'" 
                        class="bg-white/20 hover:bg-white/30 text-white px-6 py-3 rounded-xl transition-all duration-300 flex items-center">
                    <i class="fas fa-arrow-right mr-2"></i>
                    返回第二页
                </button>
            </div>
        </div>
    </div>

    <script>
        // 显示加载状态
        function showLoading(button) {
            const loading = button.querySelector('.loading');
            const text = button.querySelector('.export-text');
            loading.classList.add('active');
            text.textContent = '正在导出...';
            button.disabled = true;
        }

        // 隐藏加载状态
        function hideLoading(button, originalText) {
            const loading = button.querySelector('.loading');
            const text = button.querySelector('.export-text');
            loading.classList.remove('active');
            text.textContent = originalText;
            button.disabled = false;
        }

        // 导出完整演示文稿
        async function exportFullPresentation() {
            const button = event.target.closest('button');
            const originalText = '导出完整演示文稿';
            
            showLoading(button);
            
            try {
                await exportToPPT.exportFull();
            } catch (error) {
                console.error('导出失败:', error);
                alert('导出失败，请检查浏览器控制台获取详细信息');
            } finally {
                hideLoading(button, originalText);
            }
        }

        // 导出单页
        async function exportSingleSlide(slideNumber) {
            const button = event.target.closest('button');
            const originalText = `导出第${slideNumber === 1 ? '一' : '二'}页`;
            
            showLoading(button);
            
            try {
                await exportToPPT.exportCurrent(slideNumber);
            } catch (error) {
                console.error('导出失败:', error);
                alert('导出失败，请检查浏览器控制台获取详细信息');
            } finally {
                hideLoading(button, originalText);
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 检查PptxGenJS是否加载成功
            if (typeof PptxGenJS === 'undefined') {
                alert('PPT导出库加载失败，请检查网络连接后刷新页面');
            }
        });
    </script>
</body>
</html>
