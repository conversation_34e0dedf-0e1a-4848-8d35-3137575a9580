@startuml 黑白名单管理流程时序图
title 黑白名单管理功能完整流程

participant "用户" as User
participant "AntivirusSettingsActivity" as Settings
participant "BlackWhiteListManagementActivity" as Management
participant "BlackWhiteListAdapter" as Adapter
participant "DataManager" as DataManager
participant "DatabaseHelper" as DatabaseHelper
participant "AVLEngine" as Engine
participant "Config" as Config
participant "SQLite数据库" as Database

== 进入管理页面 ==

User -> Settings: 点击"管理白名单"或"管理黑名单"
activate Settings

Settings -> Management: start(context, listType)
activate Management

Management -> Management: 初始化UI组件
Management -> Management: setupRecyclerView()
Management -> Adapter: 创建适配器
activate Adapter

== 加载现有数据 ==

Management -> DataManager: getBlackWhiteListData()
activate DataManager

DataManager -> DatabaseHelper: getBlackWhiteListData()
activate DatabaseHelper

DatabaseHelper -> Database: SELECT * FROM black_white_list
Database --> DatabaseHelper: 返回所有记录
DatabaseHelper --> DataManager: HashMap<String, Boolean>
deactivate DatabaseHelper

DataManager --> Management: 黑白名单数据
deactivate DataManager

Management -> Management: updateListData(data)
Management -> Management: 根据listType过滤数据
Management -> Adapter: notifyDataSetChanged()
Management -> Management: updateStatistics()

== 添加新项目 ==

User -> Management: 点击FloatingActionButton
Management -> Management: showAddItemDialog()
Management -> User: 显示添加对话框

User -> Management: 输入Hash值和选择类型
User -> Management: 点击"添加"按钮

Management -> Management: 验证输入
alt 输入有效
    Management -> DataManager: getBlackWhiteListData()
    activate DataManager
    DataManager --> Management: 当前数据
    deactivate DataManager
    
    Management -> Management: 添加新项到数据中
    Management -> DataManager: saveBlackWhiteList(updatedData)
    activate DataManager
    
    DataManager -> DatabaseHelper: insertBlackWhiteData(hash, isBlack)
    activate DatabaseHelper
    DatabaseHelper -> Database: INSERT OR REPLACE INTO black_white_list
    Database --> DatabaseHelper: 操作完成
    deactivate DatabaseHelper
    
    DataManager --> Management: 保存完成
    deactivate DataManager
    
    Management -> Engine: updateConfig()
    activate Engine
    Engine -> Config: updateBlackWhiteList(updatedData)
    activate Config
    Config -> DataManager: updateCacheForBlackWhiteList(updatedData)
    activate DataManager
    DataManager -> DataManager: 同步更新缓存
    deactivate DataManager
    deactivate Config
    deactivate Engine
    
    Management -> Management: loadBlackWhiteListData()
    Management -> User: Toast("添加成功")
else 输入无效
    Management -> User: 显示错误提示
end

== 删除项目 ==

User -> Adapter: 点击删除按钮
Adapter -> Management: onDeleteClick(item)
Management -> Management: showDeleteConfirmDialog(item)
Management -> User: 显示确认对话框

User -> Management: 确认删除
Management -> DataManager: deleteBlackWhiteItem(hash)
activate DataManager

DataManager -> DatabaseHelper: deleteData(TABLE_BLACK_WHITE, HASH_COLUMN, hash)
activate DatabaseHelper
DatabaseHelper -> Database: DELETE FROM black_white_list WHERE hash = ?
Database --> DatabaseHelper: 删除完成
deactivate DatabaseHelper

DataManager --> Management: 删除完成
deactivate DataManager

Management -> DataManager: getBlackWhiteListData()
activate DataManager
DataManager --> Management: 更新后的数据
deactivate DataManager

Management -> Engine: updateConfig()
activate Engine
Engine -> Config: updateBlackWhiteList(updatedData)
activate Config
Config -> DataManager: updateCacheForBlackWhiteList(updatedData)
deactivate Config
deactivate Engine

Management -> Management: loadBlackWhiteListData()
Management -> User: Toast("删除成功")

== 查看详情 ==

User -> Adapter: 点击列表项
Adapter -> Management: onItemClick(item)
Management -> Management: showItemDetailDialog(item)
Management -> User: 显示详情对话框

== 返回设置页面 ==

User -> Management: 点击返回按钮
Management -> Settings: finish()
deactivate Management
deactivate Adapter
deactivate Settings

@enduml
