# 黑白名单管理功能说明

## 功能概述

黑白名单管理功能允许用户管理文件的扫描策略，通过添加文件Hash值到白名单或黑名单来控制扫描行为。

## 功能特性

### 1. 页面入口
- 在杀毒设置页面（AntivirusSettingsActivity）中点击"管理黑白名单"按钮
- 统一管理界面：显示和管理所有黑白名单项
- 页面标题：黑白名单管理

### 2. 数据展示
- 使用RecyclerView展示黑白名单条目
- 每个条目显示：
  - 简短Hash值（前8位...后8位）
  - 完整Hash值（作为副标题）
  - 类型标签（白名单/黑名单）
  - 删除按钮
- 支持点击查看详情并自动复制Hash值到剪贴板
- 显示统计信息（白名单和黑名单数量）
- 优化的界面布局和用户体验

### 3. 添加功能
- 点击右下角FloatingActionButton添加新项
- 输入文件Hash值（支持32位或64位十六进制字符串）
- 选择类型（白名单/黑名单）
- 根据页面模式自动设置或隐藏类型选择
- 输入验证：检查Hash值格式和长度

### 4. 删除功能
- 点击列表项的删除按钮
- 显示确认对话框
- 确认后从数据库删除并更新配置

### 5. 数据同步
- 所有操作都会同步更新到数据库
- 自动调用AVLEngine更新配置
- 支持缓存同步，避免缓存与黑白名单冲突

## 技术实现

### 1. 架构设计
```
AntivirusSettingsActivity
    ↓ (点击按钮)
BlackWhiteListManagementActivity
    ↓ (数据操作)
DataManager
    ↓ (数据库操作)
DatabaseHelper
```

### 2. 数据流
```
用户操作 → Activity → DataManager → DatabaseHelper → SQLite数据库
                ↓
            AVLEngine.updateConfig() → Config.updateBlackWhiteList()
                ↓
            缓存同步更新
```

### 3. 关键类说明

#### BlackWhiteListManagementActivity
- 主要的管理页面Activity
- 处理UI交互和数据操作
- 支持三种显示模式

#### BlackWhiteListAdapter
- RecyclerView适配器
- 处理列表项的显示和交互
- 支持数据更新和删除操作

#### BlackWhiteListItem
- 数据实体类
- 提供便捷的显示方法
- 封装Hash值和类型信息

#### DataManager (扩展)
- 新增deleteBlackWhiteItem()方法
- 支持单条记录删除
- 保持现有的批量操作方法

### 4. 数据库操作
- 使用现有的black_white_list表
- 字段：hash (TEXT PRIMARY KEY), is_black (TEXT)
- 支持INSERT OR REPLACE操作
- 支持单条删除操作

## 使用流程

### 添加黑白名单项
1. 进入杀毒设置页面
2. 点击"管理黑白名单"
3. 点击右下角"+"按钮
4. 输入文件Hash值
5. 选择类型（白名单/黑名单）
6. 确认添加

### 删除项目
1. 在列表中找到要删除的项目
2. 点击右侧删除按钮
3. 确认删除

### 查看详情和复制Hash值
1. 点击列表中的任意项目
2. 查看完整Hash值和类型信息
3. Hash值自动复制到剪贴板
4. 显示复制成功提示

## 错误处理

### 输入验证
- Hash值不能为空
- Hash值长度必须大于等于32位
- 自动去除首尾空格

### 异常处理
- 数据库操作异常捕获和提示
- 网络异常处理
- UI操作异常保护

### 用户反馈
- Toast消息提示操作结果
- 加载状态显示
- 空状态页面提示

## 性能优化

### 1. 异步操作
- 所有数据库操作在IO线程执行
- UI更新在主线程执行
- 使用Kotlin协程管理异步任务

### 2. 内存优化
- RecyclerView复用ViewHolder
- 及时释放数据库连接
- 避免内存泄漏

### 3. 用户体验
- 加载状态提示
- 操作反馈及时
- 界面响应流畅

## 扩展功能

### 可能的扩展
1. 批量导入/导出功能
2. Hash值扫描获取功能
3. 文件路径到Hash值转换
4. 搜索和过滤功能
5. 分类管理功能

### 配置选项
1. 自动同步设置
2. 缓存策略配置
3. 显示选项设置
