package com.antiy.avlsdk.pc;

import android.content.Context;
import android.util.Log;
import android.util.Pair;

import java.io.File;
import java.io.IOException;

/**
 * AVLEnginePC Base 版本实现
 * 使用应用私有目录，避免权限问题
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/9/7
 */
public class AVLEnginePC {

    private static final String TAG = AVLEnginePC.class.getSimpleName();
    private final String TARGET_DIR = "avlsdk_pc";
    private final String ZIP_SUFFIX = ".zip";

    // {{ AURA-X: Modify - 使用 Context 参数获取应用私有目录，避免模块依赖问题。Approval: 寸止。}}
    // 通过 Context 参数获取应用私有目录，避免权限问题
    private String getVidsDir(Context context) {
        return context.getFilesDir().getAbsolutePath();
    }

    private static volatile AVLEnginePC mInstance;
    private static String mAVLPath;

    public String getAVLPCPath() {
        return mAVLPath;
    }

    static {
        try {
            System.loadLibrary("avlsdk_pc");
        } catch (Throwable e) {
            Log.d(TAG, e.getMessage());
        }
    }

    private AVLEnginePC() {
    }

    public static AVLEnginePC getInstance() {
        if (mInstance == null) {
            synchronized (AVLEnginePC.class) {
                if (mInstance == null) {
                    mInstance = new AVLEnginePC();
                }
            }
        }

        return mInstance;
    }

    /**
     * 准备 PC 引擎运行所需的数据文件
     * Base 版本：从 assets 解压到应用私有目录
     *
     * @param context Android 上下文对象
     */
    public void prepareData(Context context) {
        String vidsDir = getVidsDir(context);
        File parentDir = new File(vidsDir);

        // 确保父目录存在
        if (!parentDir.exists()) {
            Log.e(TAG, "应用私有目录不存在，创建目录失败");
            return;
        }

        File avlsdkPc = new File(vidsDir + File.separator + TARGET_DIR);
        mAVLPath = avlsdkPc.getAbsolutePath();

        Log.e(TAG, "avlsdkPc exist: " + avlsdkPc.exists());
        Log.e(TAG, "avlsdkPc HasFiles: " + AssetsUtil.isFolderHasFiles(mAVLPath));
        Log.e(TAG, "FLAVOR " + BuildConfig.FLAVOR);
        
        if (avlsdkPc.exists() && AssetsUtil.isFolderHasFiles(mAVLPath)) {
            Log.e(TAG, "PC 引擎文件已存在，跳过解压");
            return;
        }
        
        Log.e(TAG, "开始从 assets 解压 PC 引擎文件");
        try {
            // Base 版本：始终从 assets 解压
            AssetsUtil.unzipFromAssets(context, vidsDir, TARGET_DIR + ZIP_SUFFIX);
            Log.e(TAG, "PC 引擎文件解压完成");
        } catch (IOException e) {
            Log.e(TAG, "PC 引擎文件解压失败", e);
            e.printStackTrace();
        }
    }

    public int init() {
        return loadEngine(mAVLPath);
    }

    //ret:
    //0: success, -1: 失败（后面再完善错误码）
    public native int loadEngine(String sdk_dir);

    public native void unloadEngine();

    public native String scan(String filePath);

    public native Pair<String, byte[]> scanWithMd5(String filePath);

    public native void setScanEnabled(boolean enabled);

    public native String getDbInfo();

    public native String getCurVersion();
}
