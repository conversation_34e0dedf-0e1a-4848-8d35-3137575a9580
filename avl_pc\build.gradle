apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'

android {
    namespace 'com.antiy.avlsdk.pc'
    compileSdk 34
    ndkVersion "27.0.12077973"
    defaultConfig {
        minSdk 26

        consumerProguardFiles "consumer-rules.pro"

        externalNativeBuild {
            ndkBuild {
                abiFilters "arm64-v8a"
            }
        }
        ndk {
            abiFilters "arm64-v8a"
        }
    }
    // 定义产品变体
    flavorDimensions "client"
    productFlavors {
        // 全量版本
        base {
            dimension "client"
            isDefault = true
        }
        // 长城版本
        greatwall {
            dimension "client"
        }
        // 长安版本
        changan {
            dimension "client"
        }
    }

    buildTypes {
        all {
            externalNativeBuild {
                cmake {
                    cFlags "-Wno-error=format-security -fpermissive -DLOG_TAG=\\\"AVLSDK_PC\\\" -fno-rtti -fno-exceptions"
                    cppFlags "-Wno-error=format-security -fpermissive -DLOG_TAG=\\\"AVLSDK_PC\\\" -fno-rtti -fno-exceptions"
                }
            }
        }
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    externalNativeBuild {
        cmake {
            path file("src/main/cpp/CMakeLists.txt")
            version "3.22.1"
        }
    }
    sourceSets {
        base {
            java.srcDirs = ['src/base/java']
            assets.srcDirs = ['src/base/assets']
        }
        greatwall {
            java.srcDirs = ['src/main/java', 'src/greatwall/java']
            assets.srcDirs = ['src/greatwall/assets']
        }
        changan {
            java.srcDirs = ['src/main/java', 'src/changan/java']
            assets.srcDirs = ['src/changan/assets']
        }
    }
}

