package com.antiy.avlsdk.pc;

import android.content.Context;
import android.content.res.AssetManager;
import android.util.Log;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;
import java.util.zip.ZipInputStream;

/**
 * 智能解压工具类
 * 
 * <h2>主要功能:</h2>
 * <ol>
 *     <li>基于 version.conf 文件的智能解压机制</li>
 *     <li>读取 zip 包内 version.conf 文件的时间戳</li>
 *     <li>与目标目录时间戳比较，决定是否需要解压</li>
 *     <li>提供目录完全清理功能</li>
 * </ol>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/12/24
 */
public class SmartExtractionUtil {
    private static final String TAG = "SmartExtractionUtil";
    private static final String VERSION_CONF_FILE = "version.conf";
    
    /**
     * 判断是否需要从 assets 解压
     * 
     * @param context Android 上下文
     * @param zipName assets 中的 zip 文件名
     * @param targetDir 目标解压目录
  * @return true 表示需要解压，false 表示跳过解压过解压
     */
    public static boolean shouldExtractFromAssets(Context context, String zipName, String targetDir) {
        try {
            // 检查目标目录是否存在且有文件
            File targetDirectory = new File(targetDir);
            if (!targetDirectory.exists() || !AssetsUtil.isFolderHasFiles(targetDir)) {
                Log.i(TAG, "Target directory does not exist or is empty, extraction needed");
                return true;
            }
            
            // 读取 assets 中 zip 包的 version.conf 时间戳
            long zipVersionTimestamp = readVersionFromAssetsZip(context, zipName);
            if (zipVersionTimestamp == -1) {
                Log.w(TAG, "Cannot read version.conf from zip package, fallback to force extraction");
                return true;
            }
            
            // 获取目标目录的最后修改时间
            long targetDirTimestamp = targetDirectory.lastModified();
            
            Log.i(TAG, "Zip version.conf timestamp: " + zipVersionTimestamp +
                      ", target directory timestamp: " + targetDirTimestamp);
            
            // 比较时间戳
            return zipVersionTimestamp > targetDirTimestamp;
            
        } catch (Exception e) {
            Log.e(TAG, "Smart extraction check failed, fallback to force extraction", e);
            return true;
        }
    }
    
    /**
     * 判断是否需要从 vendor 解压
     * 
     * @param sourceZipPath vendor 中的 zip 文件路径
     * @param targetDir 目标解压目录
     * @return true 表示需要解压，false 表示跳过解压
     */
    public static boolean shouldExtractFromVendor(String sourceZipPath, String targetDir) {
        try {
            // 检查目标目录是否存在且有文件
            File targetDirectory = new File(targetDir);
            if (!targetDirectory.exists() || !AssetsUtil.isFolderHasFiles(targetDir)) {
                Log.i(TAG, "Target directory does not exist or is empty, extraction needed");
                return true;
            }
            
            // 读取 vendor 中 zip 包的 version.conf 时间戳
            long zipVersionTimestamp = readVersionFromVendorZip(sourceZipPath);
            if (zipVersionTimestamp == -1) {
                Log.w(TAG, "Cannot read version.conf from zip package, fallback to force extraction");
                return true;
            }
            
            // 获取目标目录的最后修改时间
            long targetDirTimestamp = targetDirectory.lastModified();
            
            Log.i(TAG, "Zip version.conf timestamp: " + zipVersionTimestamp +
                      ", target directory timestamp: " + targetDirTimestamp);
            
            // 比较时间戳
            return zipVersionTimestamp > targetDirTimestamp;
            
        } catch (Exception e) {
            Log.e(TAG, "Smart extraction check failed, fallback to force extraction", e);
            return true;
        }
    }
    
    /**
     * 从 assets 中的 zip 包读取 version.conf 文件的时间戳
     *
     * 注意：由于 Android Assets 的限制，必须使用遍历方式：
     * 1. AssetManager 只提供 InputStream 访问，不支持随机访问
     * 2. ZipInputStream 只能按顺序读取条目，无法直接跳转到指定文件
     * 3. 无法使用 ZipFile.getEntry() 方法，因为 assets 文件没有实际文件路径
     *
     * @param context Android 上下文
     * @param zipName zip 文件名
     * @return 时间戳，读取失败返回 -1
     */
    private static long readVersionFromAssetsZip(Context context, String zipName) {
        try {
            AssetManager assetManager = context.getAssets();
            InputStream inputStream = assetManager.open(zipName);
            ZipInputStream zipInputStream = new ZipInputStream(inputStream);
            
            ZipEntry zipEntry;
            while ((zipEntry = zipInputStream.getNextEntry()) != null) {
                if (VERSION_CONF_FILE.equals(zipEntry.getName())) {
                    // 找到 version.conf 文件，读取内容
                    BufferedReader reader = new BufferedReader(new InputStreamReader(zipInputStream));
                    String timestampStr = reader.readLine();
                    if (timestampStr != null && !timestampStr.trim().isEmpty()) {
                        try {
                            long timestamp = Long.parseLong(timestampStr.trim());
                            Log.i(TAG, "Read version.conf timestamp from assets zip: " + timestamp);
                            return timestamp;
                        } catch (NumberFormatException e) {
                            Log.e(TAG, "Invalid version.conf timestamp format: " + timestampStr, e);
                        }
                    }
                    break;
                }
                zipInputStream.closeEntry();
            }
            
            zipInputStream.close();
            inputStream.close();
            
        } catch (IOException e) {
            Log.e(TAG, "Failed to read version.conf from assets zip package", e);
        }
        
        return -1;
    }
    
    /**
     * 从 vendor 中的 zip 包读取 version.conf 文件的时间戳
     * 优化版本：使用 ZipFile 直接访问指定文件，避免遍历整个 ZIP 包
     *
     * @param sourceZipPath zip 文件路径
     * @return 时间戳，读取失败返回 -1
     */
    private static long readVersionFromVendorZip(String sourceZipPath) {
        try {
            File sourceZipFile = new File(sourceZipPath);
            if (!sourceZipFile.exists()) {
                Log.e(TAG, "Vendor zip file does not exist: " + sourceZipPath);
                return -1;
            }

            // 使用 ZipFile 直接访问指定条目，避免遍历整个 ZIP 包
            try (ZipFile zipFile = new ZipFile(sourceZipFile)) {
                ZipEntry versionEntry = zipFile.getEntry(VERSION_CONF_FILE);

                if (versionEntry == null) {
                    Log.w(TAG, "File " + VERSION_CONF_FILE + " not found in zip package");
                    return -1;
                }

                // 直接读取 version.conf 文件内容
                try (InputStream inputStream = zipFile.getInputStream(versionEntry);
                     BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {

                    String timestampStr = reader.readLine();
                    if (timestampStr != null && !timestampStr.trim().isEmpty()) {
                        try {
                            long timestamp = Long.parseLong(timestampStr.trim());
                            Log.i(TAG, "Read version.conf timestamp directly from vendor zip: " + timestamp);
                            return timestamp;
                        } catch (NumberFormatException e) {
                            Log.e(TAG, "Invalid version.conf timestamp format: " + timestampStr, e);
                        }
                    } else {
                        Log.w(TAG, "version.conf file content is empty");
                    }
                }
            }

        } catch (IOException e) {
            Log.e(TAG, "Failed to read version.conf from vendor zip package", e);
        }

        return -1;
    }
    
    /**
     * 完全清理目标目录
     * 
     * @param targetDir 目标目录路径
     * @return true 表示清理成功，false 表示清理失败
     */
    public static boolean cleanTargetDirectory(String targetDir) {
        try {
            File directory = new File(targetDir);
            if (directory.exists()) {
                Log.i(TAG, "Start cleaning target directory: " + targetDir);
                deleteDirectory(directory);
                Log.i(TAG, "Target directory cleaning completed");
                return true;
            }
            return true; // 目录不存在，认为清理成功
        } catch (Exception e) {
            Log.e(TAG, "Failed to clean target directory: " + targetDir, e);
            return false;
        }
    }
    
    /**
     * 递归删除目录及其内容
     * 
     * @param directory 要删除的目录
     */
    private static void deleteDirectory(File directory) {
        if (directory.isDirectory()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    deleteDirectory(file);
                }
            }
        }
        directory.delete();
    }
    
    /**
     * 执行智能解压（从 assets）
     * 
     * @param context Android 上下文
     * @param zipName zip 文件名
     * @param targetDir 目标目录
     * @throws IOException 解压失败时抛出
     */
    public static void smartExtractFromAssets(Context context, String zipName, String targetDir) throws IOException {
        if (shouldExtractFromAssets(context, zipName, targetDir)) {
            Log.i(TAG, "Extraction needed, start cleaning target directory and extracting");
            cleanTargetDirectory(targetDir);
            AssetsUtil.unzipFromAssets(context, targetDir, zipName);
            Log.i(TAG, "Smart extraction completed");
        } else {
            Log.i(TAG, "Version not updated, skip extraction");
        }
    }
    
    /**
     * 执行智能解压（从 vendor）
     * 
     * @param sourceZipPath 源 zip 文件路径
     * @param targetDir 目标目录
     * @throws IOException 解压失败时抛出
     */
    public static void smartExtractFromVendor(String sourceZipPath, String targetDir) throws IOException {
        if (shouldExtractFromVendor(sourceZipPath, targetDir)) {
            Log.i(TAG, "Extraction needed, start cleaning target directory and extracting");
            cleanTargetDirectory(targetDir);
            VendorZipUtils.unzipFromVendor(sourceZipPath, targetDir);
            Log.i(TAG, "Smart extraction completed");
        } else {
            Log.i(TAG, "Version not updated, skip extraction");
        }
    }
}
