package com.antiy.avlsdk.auth;

import android.content.Context;
import android.content.res.AssetManager;
import android.text.TextUtils;
import android.util.Base64;

import com.antiy.avlsdk.AVLEngine;
import com.antiy.avlsdk.callback.Logger;
import com.antiy.avlsdk.storage.SecureStorage;
import com.antiy.avlsdk.utils.SdkConst;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;

/**
 * 授权信息管理类
 * 该类负责管理和存储应用的授权相关信息，包括UUID、客户端ID和授权令牌
 * 使用单例模式实现，确保全局只有一个实例
 * 支持从加密配置文件加载默认配置，并提供安全的数据存储机制
 *
 * <AUTHOR>
 * @version 1.0
 */
public class AuthInfoManager {
    private static final String AUTH_FILE_NAME = "auth_info";
    private static final String KEY_UUID = "uuid";
    private static final String KEY_CLIENT_ID = "client_id";
    private static final String KEY_TOKEN = "token";
    private static final String DEFAULT_CONFIG_FILE = "auth_config.bin";

    private static AuthInfoManager instance;
    private final SecureStorage secureStorage;
    private final Context context;
    private JSONObject defaultConfig;

    /**
     * 私有构造函数，初始化授权信息管理器
     * 
     * @param context 应用程序上下文，用于初始化存储组件和访问应用资源
     */
    private AuthInfoManager(Context context) {
        this.context = context.getApplicationContext();
        this.secureStorage = SecureStorage.getInstance(context);
        loadDefaultConfig();
    }

    /**
     * 获取AuthInfoManager的单例实例
     * 使用双重检查锁定模式确保线程安全
     *
     * @param context 应用程序上下文
     * @return AuthInfoManager的单例实例
     */
    public static AuthInfoManager getInstance(Context context) {
        if (instance == null) {
            synchronized (AuthInfoManager.class) {
                if (instance == null) {
                    instance = new AuthInfoManager(context);
                }
            }
        }
        return instance;
    }

    /**
     * 从加密的配置文件加载默认配置
     * 尝试从assets目录下读取并解密配置文件，如果失败则使用备用配置
     * 配置文件包含默认的UUID、客户端ID和授权令牌
     * 
     * @throws Exception 当配置文件读取或解密失败时抛出异常，但会被内部捕获并处理
     */
    private void loadDefaultConfig() {
        try {
            AssetManager assetManager = context.getAssets();
            InputStream inputStream = assetManager.open(DEFAULT_CONFIG_FILE);
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
            StringBuilder content = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                content.append(line);
            }
            reader.close();
            inputStream.close();

            // 解密配置内容
            byte[] encrypted = Base64.decode(content.toString(), Base64.NO_WRAP);
            byte[] decrypted = decryptConfig(encrypted);
            String decryptedContent = new String(decrypted, StandardCharsets.UTF_8);
            AVLEngine.Logger.info("decryptedContent:" + decryptedContent);
            defaultConfig = new JSONObject(decryptedContent);
            
        } catch (Exception e) {
            AVLEngine.Logger.error("Failed to load default config: " + e.getMessage());
            // 如果加载失败，使用备用默认值
            createFallbackConfig();
        }
    }

    /**
     * 解密配置文件内容
     * 使用应用签名作为密钥的一部分，通过SHA-256和异或运算进行解密
     * 
     * @param encrypted 待解密的字节数组
     * @return 解密后的字节数组，如果解密失败则返回空数组
     */
    private byte[] decryptConfig(byte[] encrypted) {
        try {
            // 使用应用签名作为解密密钥的一部分
            String appSignature = getAppSignature();
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] key = digest.digest(appSignature.getBytes(StandardCharsets.UTF_8));

            byte[] decrypted = new byte[encrypted.length];
            for (int i = 0; i < encrypted.length; i++) {
                decrypted[i] = (byte) (encrypted[i] ^ key[i % key.length]);
            }
            return decrypted;
        } catch (Exception e) {
            AVLEngine.Logger.error("Failed to decrypt config: " + e.getMessage());
            return new byte[0];
        }
    }

    /**
     * 获取应用签名
     * 当前使用预定义的默认签名密钥，后续可扩展为动态获取应用实际签名
     * 
     * @return 应用签名字符串
     */
    private String getAppSignature() {
        /*try {
            android.content.pm.PackageInfo packageInfo = context.getPackageManager()
                .getPackageInfo(context.getPackageName(), android.content.pm.PackageManager.GET_SIGNATURES);
            return Base64.encodeToString(
                packageInfo.signatures[0].toByteArray(), 
                Base64.NO_WRAP
            );
        } catch (Exception e) {
            return "fallback_signature";
        }*/
        return SdkConst.DEFAULT_SIGNATURE_KEY;
    }

    /**
     * 创建备用配置
     * 当无法从加密配置文件加载配置时，使用预定义的默认值创建配置
     * 包含默认的UUID、客户端ID和授权令牌
     */
    private void createFallbackConfig() {
        try {
            defaultConfig = new JSONObject();
            defaultConfig.put(KEY_UUID, SdkConst.DEFAULT_UUID);
            defaultConfig.put(KEY_CLIENT_ID, SdkConst.CLIENT_ID);
            defaultConfig.put(KEY_TOKEN, SdkConst.DEFAULT_TOKEN);
        } catch (JSONException e) {
            AVLEngine.Logger.error("Failed to create fallback config: " + e.getMessage());
        }
    }

    /**
     * 获取授权信息
     * 首先尝试从安全存储中读取授权信息，如果不存在或解析失败，则使用默认配置
     * 
     * @return 包含授权信息的JSONObject对象
     */
    public JSONObject getAuthInfo() {
        String authData = secureStorage.readData(AUTH_FILE_NAME);
        JSONObject authInfo = null;
        
        if (!TextUtils.isEmpty(authData)) {
            try {
                authInfo = new JSONObject(authData);
            } catch (JSONException e) {
                AVLEngine.Logger.error("Failed to parse auth info: " + e.getMessage());
            }
        }
        
        if (authInfo == null) {
            authInfo = defaultConfig;
            saveAuthInfo(authInfo);
        }
        
        return authInfo;
    }

    /**
     * 保存授权信息到安全存储
     * 
     * @param authInfo 要保存的授权信息JSONObject对象
     */
    public void saveAuthInfo(JSONObject authInfo) {
        secureStorage.saveData(AUTH_FILE_NAME, authInfo.toString());
    }

    /**
     * 获取设备UUID
     * 
     * @return 当前设备的UUID，如果未设置则返回默认UUID
     */
    public String getUuid() {
        JSONObject authInfo = getAuthInfo();
        return authInfo.optString(KEY_UUID, defaultConfig.optString(KEY_UUID));
    }

    /**
     * 获取客户端ID
     * 
     * @return 当前的客户端ID，如果未设置则返回默认客户端ID
     */
    public String getClientId() {
        JSONObject authInfo = getAuthInfo();
        return authInfo.optString(KEY_CLIENT_ID, defaultConfig.optString(KEY_CLIENT_ID));
    }

    /**
     * 获取授权令牌
     * 
     * @return 当前的授权令牌，如果未设置则返回默认令牌
     */
    public String getToken() {
        JSONObject authInfo = getAuthInfo();
        return authInfo.optString(KEY_TOKEN, defaultConfig.optString(KEY_TOKEN));
    }
}