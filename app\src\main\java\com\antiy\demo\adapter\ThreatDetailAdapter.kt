package com.antiy.demo.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.antiy.demo.R
import com.antiy.demo.model.ThreatDetail

class ThreatDetailAdapter(
    private val threats: List<ThreatDetail>,
    private val onDeleteClick: (ThreatDetail) -> Unit,
    private val onIgnoreClick: (ThreatDetail) -> Unit
) : RecyclerView.Adapter<ThreatDetailAdapter.ThreatViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ThreatViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_threat_detail, parent, false)
        return ThreatViewHolder(view)
    }

    override fun onBindViewHolder(holder: ThreatViewHolder, position: Int) {
        holder.bind(threats[position])
    }

    override fun getItemCount(): Int = threats.size

    inner class ThreatViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val ivThreatIcon: ImageView = itemView.findViewById(R.id.ivThreatIcon)
        private val tvThreatName: TextView = itemView.findViewById(R.id.tvThreatName)
        private val tvThreatLocation: TextView = itemView.findViewById(R.id.tvThreatLocation)
        private val tvRiskLevel: TextView = itemView.findViewById(R.id.tvRiskLevel)
        private val tvThreatDescription: TextView = itemView.findViewById(R.id.tvThreatDescription)
        private val btnDelete: Button = itemView.findViewById(R.id.btnDelete)
        private val btnIgnore: Button = itemView.findViewById(R.id.btnIgnore)

        fun bind(threat: ThreatDetail) {
            tvThreatName.text = threat.name
            tvThreatLocation.text = "位置：${threat.location}"
            tvThreatDescription.text = threat.description

            // 设置图标
            when (threat.type) {
                ThreatDetail.ThreatType.MALWARE -> {
                    ivThreatIcon.setImageResource(R.drawable.ic_virus_db)
                    ivThreatIcon.setBackgroundResource(R.drawable.circle_background_light_red)
                }
                ThreatDetail.ThreatType.SUSPICIOUS_BEHAVIOR -> {
                    ivThreatIcon.setImageResource(R.drawable.ic_warning)
                    ivThreatIcon.setBackgroundResource(R.drawable.circle_background_light_blue)
                }
                ThreatDetail.ThreatType.PRIVACY_RISK -> {
                    ivThreatIcon.setImageResource(R.drawable.ic_privacy)
                    ivThreatIcon.setBackgroundResource(R.drawable.circle_background_light_blue)
                }
            }

            // 设置风险等级
            when (threat.riskLevel) {
                ThreatDetail.RiskLevel.HIGH -> {
                    tvRiskLevel.text = "高风险"
                    tvRiskLevel.backgroundTintList = ContextCompat.getColorStateList(itemView.context, R.color.danger_red)
                }
                ThreatDetail.RiskLevel.MEDIUM -> {
                    tvRiskLevel.text = "中风险"
                    tvRiskLevel.backgroundTintList = ContextCompat.getColorStateList(itemView.context, R.color.warning_yellow)
                }
                ThreatDetail.RiskLevel.LOW -> {
                    tvRiskLevel.text = "低风险"
                    tvRiskLevel.backgroundTintList = ContextCompat.getColorStateList(itemView.context, R.color.processing_blue)
                }
            }

            // 设置按钮点击事件
            btnDelete.setOnClickListener { onDeleteClick(threat) }
            btnIgnore.setOnClickListener { onIgnoreClick(threat) }
        }
    }
} 