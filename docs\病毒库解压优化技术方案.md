# AVL SDK病毒库解压优化方案

## 一、背景分析

在SDK开发的持续迭代过程中，因为病毒库解压的解压逻辑设计于早期版本，其设计初衷是：


1. **性能优化**：避免每次启动都重复解压大文件（通常20MB）
2. **用户体验**：减少应用启动时间，提升响应速度
3. **资源节约**：避免不必要的CPU和I/O消耗


**原始设计逻辑**：伪代码

```java
// 早期的简单检查逻辑
if (targetDirectory.exists() && hasFiles(targetDirectory)) {
    // 认为数据已准备好，直接使用
    return;
}
// 否则执行解压操作
extractData();
```


## 二、具体场景

随着长城的定制化的需求，这种解压的业务设计已经出现弊端。在长城的业务场景中，AVL SDK 当前的数据解压机制存在关键缺陷：

当应用更新包含新的病毒库或引擎so和license时，系统无法识别并更新本地的so或者license，导致继续使用过期的病毒特征库 

**应用更新导致的数据滞后，需要手动更新**

```
时间线：
T1: 用户安装应用v1.0，解压引擎数据到本地
T2: 应用更新到v1.1，zip中包含新的病毒库和so、license
T3: 应用启动，检查发现本地目录存在且有文件
T4: 跳过解压，继续使用v1.0的旧病毒库
结果: so无法被更新，安全防护能力下降
```

## 三、解决方案设计

### 3.1 技术方案概述

实现**基于version.conf版本文件的解压机制**：通过在zip包内置version.conf文件记录版本时间戳，与本地数据时间对比，自动判断是否需要重新解压最新的病毒库和引擎数据。

**优化原因**：
原方案基于zip包文件时间戳存在风险，因为：
1. 计算机系统时间可能不准确
2. 文件传输过程中时间戳可能被修改
3. 不同系统间时间同步问题

**新方案设计**：
在zip包内置version.conf文件，内容为单行时间戳，确保版本信息的准确性和可控性。

**智能决策逻辑**：

```
读取zip包内version.conf文件的时间戳
如果 (version.conf时间戳 > 本地数据目录时间戳) {
    完全清理目标目录
    重新解压最新数据
} 否则 {
    使用现有数据，跳过解压
}
```

**技术架构**：

* 新增SmartExtractionUtil统一工具类
* 改造现有的prepareData()方法
* 新增version.conf文件读取逻辑
* 保持所有现有API接口不变
* 支持所有SDK变体（base、greatwall、changan）

### 体现价值

* **确保数据时效性**：通过内置版本文件准确识别并应用最新的病毒库更新
* **保持性能优化**：避免不必要的重复解压操作
* **提高可靠性**：避免系统时间不准确导致的版本判断错误

### 3.2 时序图设计

```mermaid
sequenceDiagram
    participant App as 应用程序
    participant AVLEngine as AVLEngine
    participant MobileEngine as 手机引擎
    participant PCEngine as PC引擎
    participant SmartUtil as SmartExtractionUtil
    participant AssetsUtil as AssetsUtil/VendorZipUtils
    participant FileSystem as 文件系统
    participant ZipFile as ZIP包
    App->>AVLEngine: init(context, uuid, logger, tunnel)
    AVLEngine->>MobileEngine: prepareData(context)
    Note over MobileEngine: Mobile引擎数据准备
    MobileEngine->>SmartUtil: shouldExtractFromAssets(context, zipName, targetDir)
    SmartUtil->>FileSystem: 检查目标目录状态
    SmartUtil->>ZipFile: 读取zip包内version.conf文件
    ZipFile-->>SmartUtil: 返回版本时间戳
    SmartUtil->>FileSystem: 获取目标目录最后修改时间
    SmartUtil->>SmartUtil: 比较version.conf时间戳与目录时间
    SmartUtil-->>MobileEngine: 返回是否需要解压
    alt 需要解压
        MobileEngine->>FileSystem: 完全清理目标目录
        MobileEngine->>AssetsUtil: unzipFromAssets(context, targetDir, zipName)
        AssetsUtil->>FileSystem: 解压文件到目标目录
        AssetsUtil-->>MobileEngine: 解压完成
        MobileEngine->>FileSystem: 更新目录时间戳
    else 跳过解压
        Note over MobileEngine: 使用现有文件，跳过解压
    end
    MobileEngine-->>AVLEngine: Mobile引擎准备完成
    AVLEngine->>PCEngine: prepareData(context)
    Note over PCEngine: PC引擎数据准备
    PCEngine->>SmartUtil: shouldExtractFromVendor/Assets(sourcePath, targetDir)
    SmartUtil->>ZipFile: 读取zip包内version.conf文件
    ZipFile-->>SmartUtil: 返回版本时间戳
    SmartUtil->>FileSystem: 检查目标目录状态
    SmartUtil->>SmartUtil: 比较version.conf时间戳与目录时间
    SmartUtil-->>PCEngine: 返回是否需要解压
    alt 需要解压
        PCEngine->>FileSystem: 完全清理目标目录
        PCEngine->>AssetsUtil: unzipFromAssets/Vendor(...)
        AssetsUtil->>FileSystem: 解压文件
        AssetsUtil-->>PCEngine: 解压完成
        PCEngine->>FileSystem: 更新目录时间戳
    else 跳过解压
        Note over PCEngine: 使用现有文件
    end
    PCEngine-->>AVLEngine: PC引擎准备完成
    AVLEngine-->>App: 初始化完成
```

### 3.3 **流程图设计**

```mermaid
flowchart TD
    A[开始 prepareData] --> B{目标目录是否存在?}
    B -->|否| C[创建目标目录]
    B -->|是| D{目录是否包含文件?}
    C --> E[执行解压操作]
    D -->|否| E
    D -->|是| F[读取zip包内version.conf文件]
    F --> G[获取version.conf中的时间戳]
    G --> H[获取目标目录最后修改时间]
    H --> I{version.conf时间戳 > 目标目录时间戳?}
    I -->|是| J[完全清理目标目录]
    I -->|否| K[跳过解压，使用现有文件]
    J --> E
    E --> L{解压是否成功?}
    L -->|是| M[更新目标目录时间戳]
    L -->|否| N[记录错误日志]
    M --> O[解压完成]
    N --> O
    K --> O
    O --> P[结束]
    style A fill:#e1f5fe
    style E fill:#fff3e0
    style K fill:#f3e5f5
    style O fill:#e8f5e8
    style P fill:#ffebee
```

## 四、version.conf文件格式规范

### 4.1 文件位置
- 文件名：`version.conf`
- 位置：zip包根目录下

### 4.2 文件内容格式
```
1703123456789
```
- 单行文本，包含13位时间戳（毫秒级Unix时间戳）
- 无其他内容，无注释，无空行

### 4.3 时间戳生成规则
- 使用当前构建时间的毫秒级Unix时间戳
- 确保每次打包时更新为最新时间
- 建议在CI/CD流程中自动生成

## 五、总结

本方案通过引入基于version.conf版本文件的解压机制，解决了原方案依赖系统时间可能不准确的问题，在保持性能优化的同时确保了数据版本判断的准确性，能够显著提升AVL SDK的安全防护能力和用户体验。

**核心优势**：
1. **版本控制准确性**：通过内置版本文件避免系统时间不准确的风险
2. **完整性保证**：更新时完全清理目标目录，确保数据完整性
3. **向后兼容**：保持现有API接口不变，升级平滑

## 六、风险与注意事项

### 6.1 实施风险
1. **version.conf文件缺失**：需要确保所有zip包都包含version.conf文件，否则需要降级处理
2. **时间戳格式错误**：需要严格按照13位毫秒级时间戳格式，避免解析错误
3. **构建流程依赖**：需要在打包流程中确保version.conf文件的正确生成和更新

### 6.2 部署注意事项
1. **长城vendor推送**：需要确认长城那边在vendor下推送zip文件时，确保version.conf文件包含正确的时间戳
2. **OTA更新**：需要长城OTA时更新/vendor/etc/idps/avlsdk/目录下的压缩包，并确保新包含有正确的version.conf
3. **兼容性处理**：对于不包含version.conf的旧版本zip包，需要有降级处理机制

### 6.3 测试验证
1. **时间戳比较逻辑**：需要充分测试各种时间戳比较场景
2. **目录清理逻辑**：确保完全清理目标目录的逻辑正确执行
3. **异常处理**：测试version.conf文件读取失败、格式错误等异常情况的处理