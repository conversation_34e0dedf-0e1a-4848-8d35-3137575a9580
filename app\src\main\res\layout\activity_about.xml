<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_gray"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- 蓝色顶部区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/primary"
            android:orientation="vertical"
            android:paddingTop="32dp">

            <!-- 顶部栏 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingHorizontal="16dp">

                <ImageButton
                    android:id="@+id/btn_back"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:contentDescription="返回"
                    android:src="@drawable/ic_arrow_back"
                    android:tint="@color/white" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:text="关于"
                    android:textColor="@color/white"
                    android:textSize="18sp"
                    android:textStyle="bold" />
            </LinearLayout>
        </LinearLayout>

        <!-- 中间内容区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingHorizontal="16dp"
            android:paddingBottom="24dp">

            <!-- App Logo and Version Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="vertical"
                android:paddingVertical="32dp">

                <FrameLayout
                    android:layout_width="80dp"
                    android:layout_height="80dp"
                    android:background="@drawable/bg_app_logo">

                    <ImageView
                        android:id="@+id/iv_app_logo"
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:layout_gravity="center"
                        android:src="@drawable/ic_shield_logo" />
                </FrameLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="安全卫士"
                    android:textColor="@color/black"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_app_version"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="版本 1.0"
                    android:textColor="@color/gray"
                    android:textSize="14sp" />
            </LinearLayout>

            <!-- Version Info Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="版本信息"
                        android:textColor="@color/black"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tv_engine_version"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="引擎版本：1.5.3"
                            android:textColor="@color/gray"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/tv_virus_db_version"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:text="病毒库：2024.03.15"
                            android:textColor="@color/gray"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/tv_last_update"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:text="最后更新：2024.03.15"
                            android:textColor="@color/gray"
                            android:textSize="14sp" />
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Developer Info Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="开发者信息"
                        android:textColor="@color/black"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tv_developer"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="开发者：安天信息科技有限公司"
                            android:textColor="@color/gray"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/tv_website"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:text="官方网站：www.antiy.com"
                            android:textColor="@color/gray"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/tv_email"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:text="联系邮箱：<EMAIL>"
                            android:textColor="@color/gray"
                            android:textSize="14sp" />
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Links Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="8dp"
                app:cardElevation="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tv_service_terms"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="?attr/selectableItemBackground"
                        android:drawableEnd="@drawable/ic_arrow_right"
                        android:padding="16dp"
                        android:text="服务条款"
                        android:textColor="@color/black"
                        android:textSize="16sp" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="0.5dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:background="@color/divider" />

                    <TextView
                        android:id="@+id/tv_privacy_policy"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="?attr/selectableItemBackground"
                        android:drawableEnd="@drawable/ic_arrow_right"
                        android:padding="16dp"
                        android:text="隐私政策"
                        android:textColor="@color/black"
                        android:textSize="16sp" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="0.5dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:background="@color/divider" />

                    <TextView
                        android:id="@+id/tv_open_source"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="?attr/selectableItemBackground"
                        android:drawableEnd="@drawable/ic_arrow_right"
                        android:padding="16dp"
                        android:text="开源许可"
                        android:textColor="@color/black"
                        android:textSize="16sp" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>
        </LinearLayout>
    </LinearLayout>
</androidx.core.widget.NestedScrollView> 