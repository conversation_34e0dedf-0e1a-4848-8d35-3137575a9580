package com.antiy.avlsdk.scan;

import android.content.Context;
import android.content.SharedPreferences;

import com.antiy.avlsdk.AVLEngine;
import com.antiy.avlsdk.config.ConfigKey;
import com.antiy.avlsdk.entity.ScanFileType;
import com.antiy.avlsdk.storage.DBConstants;
import com.antiy.avlsdk.utils.ArchiveIdentifier;
import com.antiy.avlsdk.utils.JsonUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

/**
 * Author: wangbiao
 * Date: 2024/9/9 09:42
 * Description: 文件工具类
 * 使用 try-with-resources 语句，
 * 这是 Java 7 引入的一种更优雅的方式，自动关闭实现了 AutoCloseable 接口的资源（例如 InputStream）
 */
public class FileChecker {
    // 修改为 ScanFileType 类型的列表
    private static List<ScanFileType> VALID_TYPES;

    static {
        SharedPreferences sharedPreferences = AVLEngine.getInstance().getContext()
                .getSharedPreferences(DBConstants.SHARE_PREFERENCES_CONFIG, Context.MODE_PRIVATE);
        String types = sharedPreferences.getString(ConfigKey.SCAN_TYPE.toString(), "");
        List<String> typeStrings = JsonUtils.jsonToList(types, String.class);
        VALID_TYPES = ScanFileType.fromStringList(typeStrings);
    }

    private FileChecker() {

    }

    private static class FileCheckerSingle {
        private static final FileChecker INSTANCE = new FileChecker();
    }

    public static FileChecker getInstance() {
        return FileCheckerSingle.INSTANCE;
    }

    /**
     * 判断文件是否检测类型
     * 1. 有扩展名时直接判断扩展名是否在支持列表中
     * 2. 无扩展名时通过文件头魔数判断
     *
     * @param file 文件
     * @return true是，false否
     */
    public boolean isValidType(File file) {
        if (file == null || !file.exists() || !file.isFile()) {
            AVLEngine.Logger.info("Invalid file: " + (file != null ? file.getPath() : "null"));
            return false;
        }
        return isValidByMagicNumber(file);
    }

    /**
     * 判断文件是apk、jar、dex其中一种，如果是走手机引擎查
     *
     * @param file 文件
     * @return true是，false否
     */
    public boolean isApkOrJarOrDex(File file) {
        return isApkFile(file) || isJarFile(file) || isDexFile(file);
    }

    /**
     * 判断是否是APK文件
     *
     * @param file 文件
     * @return true是，false否
     */
    public boolean isApkFile(File file) {
        if (file == null || !file.exists() || !file.isFile()) {
            return false;
        }
        ArchiveIdentifier.ArchiveType type = ArchiveIdentifier.identifyFileType(file);
        return type == ArchiveIdentifier.ArchiveType.APK;
    }

    /**
     * 判断是否是Dex文件
     *
     * @param file 文件
     * @return true是，false否
     */
    public boolean isDexFile(File file) {
        if (file == null || !file.exists() || !file.isFile()) {
            return false;
        }
        ArchiveIdentifier.ArchiveType type = ArchiveIdentifier.identifyFileType(file);
        return type == ArchiveIdentifier.ArchiveType.DEX;
    }

    /**
     * 判断是否是Jar文件
     *
     * @param file 文件
     * @return true是，false否
     */
    public boolean isJarFile(File file) {
        if (file == null || !file.exists() || !file.isFile()) {
            return false;
        }
        ArchiveIdentifier.ArchiveType type = ArchiveIdentifier.identifyFileType(file);
        return type == ArchiveIdentifier.ArchiveType.JAR;
    }


    /**
     * 备用方案：通过魔数检查文件类型（保留原实现作为fallback）
     */
    private boolean isValidByMagicNumber(File file) {
        try (FileInputStream fis = new FileInputStream(file)) {
            // 读取更多字节以支持更准确的检测
            byte[] header = new byte[12];
            long length = Math.min(12, file.length());
            int read = fis.read(header, 0, (int) length);
            
            if (read < 2) {
                return false;
            }

            // 检查是否为ZIP格式 (APK/JAR)
            if (read >= 4 && header[0] == 0x50 && header[1] == 0x4B && 
                header[2] == 0x03 && header[3] == 0x04) {
                ArchiveIdentifier.ArchiveType type = ArchiveIdentifier.identifyFileType(file);
                if (type == ArchiveIdentifier.ArchiveType.APK) {
                    AVLEngine.Logger.info("APK file detected: APK");
                    return VALID_TYPES.contains(ScanFileType.APK);
                } else if (type == ArchiveIdentifier.ArchiveType.JAR) {
                    AVLEngine.Logger.info("JAR file detected: JAR");
                    return VALID_TYPES.contains(ScanFileType.JAR);
                } else {
                    return false;
                }
            }

            // 检查是否为DEX (dex\n035\0 or dex\n036\0 etc.)
            if (read >= 4 && header[0] == 0x64 && header[1] == 0x65 && 
                header[2] == 0x78 && header[3] == 0x0A) {
                AVLEngine.Logger.info("DEX file detected: DEX");
                return VALID_TYPES.contains(ScanFileType.DEX);
            }

            // 检查是否为JPEG (更准确的检查)
            if (read >= 2 && (header[0] & 0xFF) == 0xFF && (header[1] & 0xFF) == 0xD8) {
                // 进一步验证JPEG格式
                if (read >= 3) {
                    int thirdByte = header[2] & 0xFF;
                    // JPEG文件第三个字节通常是0xFF，或者是0xE0-0xEF, 0xDB, 0xC0等
                    if (thirdByte == 0xFF || (thirdByte >= 0xE0 && thirdByte <= 0xEF) ||
                        thirdByte == 0xDB || thirdByte == 0xC0 || thirdByte == 0xC2) {
                        AVLEngine.Logger.info("JPEG file (detailed) detected: " + file.getName());
                        return VALID_TYPES.contains(ScanFileType.JPG);
                    }
                }
                AVLEngine.Logger.info("JPEG file (FF D8 FF) detected: " + file.getName());
                // 如果只有两个字节，也认为是JPEG
                return VALID_TYPES.contains(ScanFileType.JPG);
            }

            // 检查是否为PNG
            if (read >= 8 && (header[0] & 0xFF) == 0x89 && 
                (header[1] & 0xFF) == 0x50 && 
                (header[2] & 0xFF) == 0x4E && 
                (header[3] & 0xFF) == 0x47 && 
                (header[4] & 0xFF) == 0x0D && 
                (header[5] & 0xFF) == 0x0A && 
                (header[6] & 0xFF) == 0x1A && 
                (header[7] & 0xFF) == 0x0A) {
                AVLEngine.Logger.info("PNG file detected: " + file.getName());
                return VALID_TYPES.contains(ScanFileType.PNG);
            }

            // 检查是否为GIF
            if (read >= 6) {
                String magicString = new String(header, 0, 6);
                if (magicString.equals("GIF87a") || magicString.equals("GIF89a")) {
                    AVLEngine.Logger.info("GIF file detected: " + file.getName());
                    return VALID_TYPES.contains(ScanFileType.GIF);
                }
            }

            // 检查是否为MP3
            final byte[] ID3_MAGIC = {'I', 'D', '3'};
            if (read >= ID3_MAGIC.length && byteArrayStartsWith(header, ID3_MAGIC)) {
                AVLEngine.Logger.info("MP3 file (ID3 tag) detected: " + file.getName());
                return VALID_TYPES.contains(ScanFileType.MP3);
            }
            // MPEG 音频帧同步字检测 (更严格的验证)
            if (read >= 4 && (header[0] & 0xFF) == 0xFF && ((header[1] & 0xE0) == 0xE0)) {
                // 检查Layer信息 (不能为00，保留值)
                int layer = (header[1] >> 1) & 0x03;
                if (layer == 0) {
                    return false; // Layer 00是保留的
                }
                
                // 检查Bitrate index (不能为1111，保留值)
                int bitrateIndex = (header[2] >> 4) & 0x0F;
                if (bitrateIndex == 0x0F) {
                    return false; // Bitrate index 1111是保留的
                }
                
                // 检查采样率index (11通常是保留的)
                int samplingRate = (header[2] >> 2) & 0x03;
                if (samplingRate == 0x03) {
                    return false; // 采样率index 11是保留的
                }
                
                AVLEngine.Logger.info("MP3 file (validated MPEG frame) detected: " + file.getName());
                return VALID_TYPES.contains(ScanFileType.MP3);
            }

            // 检查是否为MP4 (改进版本，支持不同位置的ftyp)
            if (read >= 8) {
                // 检查第4-7字节的ftyp
                String ftyp = new String(header, 4, 4);
                if (ftyp.equals("ftyp")) {
                    AVLEngine.Logger.info("MP4 file (ftyp at offset 4) detected1: " + file.getName());
                    return VALID_TYPES.contains(ScanFileType.MP4);
                }
                // 有些MP4文件ftyp可能在不同位置，检查前12字节中是否包含ftyp
                if (read >= 12) {
                    String headerStr = new String(header, 0, 12);
                    if (headerStr.contains("ftyp")) {
                        AVLEngine.Logger.info("MP4 file (ftyp at offset 4) detected2: " + file.getName());
                        return VALID_TYPES.contains(ScanFileType.MP4);
                    }
                }
            }

            // 检查是否为ELF
            if (read >= 4 && header[0] == 0x7F && header[1] == 0x45 && 
                header[2] == 0x4C && header[3] == 0x46) {
                AVLEngine.Logger.info("ELF file detected: " + file.getName());
                return VALID_TYPES.contains(ScanFileType.ELF);
            }

            return false;

        } catch (IOException e) {
            AVLEngine.Logger.error("Failed to check file type: " + file.getPath() +
                                 ", Error: " + Arrays.toString(e.getStackTrace()));
            return false;
        }
    }

    /**
     * 辅助方法：检查字节数组是否以指定的魔数字节序列开头。
     * @param array 要检查的数组
     * @param magic 魔数字节序列
     * @return 如果 array 以 magic 开头，则为 true
     */
    private boolean byteArrayStartsWith(byte[] array, byte[] magic) {
        if (array == null || magic == null || array.length < magic.length) {
            return false;
        }
        for (int i = 0; i < magic.length; i++) {
            if (array[i] != magic[i]) {
                return false;
            }
        }
        return true;
    }
}
