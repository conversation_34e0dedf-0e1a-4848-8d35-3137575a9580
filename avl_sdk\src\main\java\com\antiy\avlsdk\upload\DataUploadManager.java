package com.antiy.avlsdk.upload;

import android.content.Context;
import android.os.Build;

import com.antiy.avlsdk.AVLEngine;
import com.antiy.avlsdk.callback.RequestCallback;
import com.antiy.avlsdk.entity.RequestMethod;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Locale;

/**
 * 数据上传管理类
 * 负责收集设备信息并上传到服务器
 * 
 */
public class DataUploadManager {
    
    private static final String UPLOAD_URL = "/api/device/upload";
    private static DataUploadManager instance;
    
    private DataUploadManager() {}
    
    public static synchronized DataUploadManager getInstance() {
        if (instance == null) {
            instance = new DataUploadManager();
        }
        return instance;
    }
    
    /**
     * 上传设备信息
     * @param context 上下文
     * @param vehicleId 车架号ID
     */
    public void uploadDeviceInfo(Context context, String vehicleId) {
        // 在子线程中执行上传操作
        new Thread(() -> {
            try {
                // 收集设备信息
                String brand = Build.BRAND;
                String model = Build.MODEL;
                String language = Locale.getDefault().toLanguageTag();
                String deviceId = DeviceInfoManager.getDeviceId(context);
                String vehicleIdentifier = DeviceInfoManager.getVehicleId(vehicleId);
                
                // 构建上传数据
                JSONObject data = new JSONObject();
                try {
                    data.put("brand", brand);
                    data.put("model", model);
                    data.put("language", language);
                    data.put("deviceId", deviceId != null ? deviceId : "");
                    data.put("vehicleId", vehicleIdentifier);
                } catch (JSONException e) {
                    AVLEngine.Logger.error("DataUploadManager: Failed to build upload data: " + e.getMessage());
                    return;
                }
                
                AVLEngine.Logger.info("DataUploadManager: Uploading device info: " + data.toString());
                
                // 执行上传逻辑
                uploadToServer(data);
                
            } catch (Exception e) {
                AVLEngine.Logger.error("DataUploadManager: Upload failed: " + e.getMessage());
            }
        }).start();
    }
    
    /**
     * 上传数据到服务器
     * @param data 要上传的数据
     */
    private void uploadToServer(JSONObject data) {
        if (AVLEngine.getInstance().getNetworkManager() == null) {
            AVLEngine.Logger.error("DataUploadManager: NetworkManager is null, cannot upload data");
            return;
        }
        
        AVLEngine.getInstance().getNetworkManager().request(
            UPLOAD_URL, RequestMethod.POST, data, new RequestCallback() {
                @Override
                public void onError(String msg) {
                    AVLEngine.Logger.error("DataUploadManager: Upload failed: " + msg);
                }
                
                @Override
                public void onFinish(int code, Object responseBody) {
                    if (code == 200) {
                        AVLEngine.Logger.info("DataUploadManager: Upload successful: " + responseBody);
                    } else {
                        AVLEngine.Logger.error("DataUploadManager: Upload failed with code: " + code + ", response: " + responseBody);
                    }
                }
            }
        );
    }
}
