package com.antiy.avlsdk.pc;

import android.content.Context;
import android.util.Log;

/**
 * 性能测试示例类
 * 展示如何使用SmartExtractionUtil中的性能测试方法
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 * @since 2024/12/24
 */
public class PerformanceTestExample {
    private static final String TAG = "PerformanceTestExample";
    
    /**
     * 执行单个文件的性能测试
     * 
     * @param context Android上下文（如果需要）
     */
    public static void testSingleFile(Context context) {
        Log.i(TAG, "开始单个文件性能测试");
        
        // 测试avlsdk_mobile.zip
        String mobileZipPath = "/vendor/avlsdk/avlsdk_mobile.zip";
        SmartExtractionUtil.performanceTestVersionReading(mobileZipPath);
        
        Log.i(TAG, "单个文件性能测试完成");
    }
    
    /**
     * 执行批量文件的性能测试
     * 
     * @param context Android上下文（如果需要）
     */
    public static void testMultipleFiles(Context context) {
        Log.i(TAG, "开始批量文件性能测试");
        
        // 准备测试文件路径
        String[] zipPaths = {
            "/vendor/avlsdk/avlsdk_mobile.zip",
            "/vendor/avlsdk/avlsdk_pc.zip"
        };
        
        // 执行批量测试
        SmartExtractionUtil.batchPerformanceTest(zipPaths);
        
        Log.i(TAG, "批量文件性能测试完成");
    }
    
    /**
     * 在SDK初始化时调用性能测试
     * 可以在AVLEngine.init()方法中调用此方法
     * 
     * @param context Android上下文
     */
    public static void runPerformanceTestDuringInit(Context context) {
        Log.i(TAG, "在SDK初始化过程中执行性能测试");
        
        // 只在Debug模式下执行性能测试
        if (BuildConfig.DEBUG) {
            testMultipleFiles(context);
        } else {
            Log.i(TAG, "Release模式，跳过性能测试");
        }
    }
    
    /**
     * 自定义性能测试配置
     * 展示如何根据需要调整测试参数
     */
    public static void customPerformanceTest() {
        Log.i(TAG, "开始自定义性能测试");
        
        // 可以在这里添加自定义的测试逻辑
        // 例如：测试不同大小的zip文件、不同位置的version.conf文件等
        
        String testZipPath = "/vendor/avlsdk/avlsdk_mobile.zip";
        
        // 执行多轮测试以获得更准确的结果
        for (int round = 1; round <= 3; round++) {
            Log.i(TAG, "执行第 " + round + " 轮测试");
            SmartExtractionUtil.performanceTestVersionReading(testZipPath);
            
            // 在测试轮次之间稍作停顿
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        
        Log.i(TAG, "自定义性能测试完成");
    }
}
