package com.antiy.demo.activity

import android.content.Context
import android.content.Intent
import android.os.Build
import android.view.LayoutInflater
import android.view.WindowManager
import androidx.core.content.ContextCompat
import androidx.core.view.WindowCompat
import com.antiy.avlsdk.AVLEngine
import com.antiy.demo.base.BaseActivity
import com.antiy.demo.databinding.ActivityCloudScanSettingsBinding

class CloudScanSettingsActivity : BaseActivity<ActivityCloudScanSettingsBinding>() {
    
    companion object {
        private const val PREFS_NAME = "cloud_scan_settings"
        private const val KEY_CLOUD_SCAN_ENABLED = "cloud_scan_enabled"
        private const val KEY_WIFI_ONLY = "wifi_only"
        private const val KEY_CLOUD_THRESHOLD = "cloud_threshold"
        private const val DEFAULT_CLOUD_THRESHOLD = 5000
        private const val KEY_CLOUD_SIZE_THRESHOLD = "cloud_size_threshold"
        private const val DEFAULT_CLOUD_SIZE_THRESHOLD = 500 // 单位MB
        
        // 启动Activity的静态方法
        fun start(context: Context) {
            context.startActivity(Intent(context, CloudScanSettingsActivity::class.java))
        }
    }
    
    override fun getViewBinding(inflater: LayoutInflater): ActivityCloudScanSettingsBinding {
        return ActivityCloudScanSettingsBinding.inflate(inflater)
    }

    override fun initView() {
        super.initView()
        setupStatusBar()
        setupToolbar()
        setupSwitches()
        setupCloudThreshold()
        setupCloudSizeThreshold()
    }

    private fun setupStatusBar() {
        // 确保状态栏正确显示
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            // 设置状态栏为亮色模式，图标为深色
            WindowCompat.getInsetsController(window, window.decorView)?.apply {
                isAppearanceLightStatusBars = true
            }
            
            // 设置状态栏为透明
            window.statusBarColor = ContextCompat.getColor(this, android.R.color.transparent)
            
            // 设置内容延伸到状态栏下方，但状态栏仍然可见
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
            
            // 使用CoordinatorLayout时，设置为false让内容延伸到系统栏下方
            WindowCompat.setDecorFitsSystemWindows(window, false)
        }
    }

    private fun setupToolbar() {
        binding.btnBack.setOnClickListener {
            finish()
        }
    }

    private fun setupSwitches() {
        // 从SharedPreferences加载设置
        val sharedPrefs = getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        
        // 设置开关的初始状态
        binding.switchCloud.isChecked = sharedPrefs.getBoolean(KEY_CLOUD_SCAN_ENABLED, true)
        binding.switchWifiOnly.isChecked = sharedPrefs.getBoolean(KEY_WIFI_ONLY, false)
        
        // 设置开关的监听器
        binding.switchCloud.setOnCheckedChangeListener { _, isChecked ->
            // 保存云查杀开关状态
            sharedPrefs.edit().putBoolean(KEY_CLOUD_SCAN_ENABLED, isChecked).apply()
            
            // 更新杀毒引擎配置
            AVLEngine.getInstance().updateConfig { config ->
//                config?.updateCloudScanEnabled(isChecked)
            }
            
            // 如果关闭了云查杀，WiFi选项应该被禁用
            binding.switchWifiOnly.isEnabled = isChecked
        }
        
        binding.switchWifiOnly.setOnCheckedChangeListener { _, isChecked ->
            // 保存仅WiFi下使用云查杀的设置
            sharedPrefs.edit().putBoolean(KEY_WIFI_ONLY, isChecked).apply()
            
            // 更新杀毒引擎配置
            AVLEngine.getInstance().updateConfig { config ->
//                config?.updateWifiOnlyCloudScan(isChecked)
            }
        }
        
        // 初始设置WiFi选项的启用状态
        binding.switchWifiOnly.isEnabled = binding.switchCloud.isChecked
    }

    private fun setupCloudThreshold() {
        val sharedPrefs = getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        val threshold = sharedPrefs.getInt(KEY_CLOUD_THRESHOLD, DEFAULT_CLOUD_THRESHOLD)
        binding.editCloudThreshold.setText(threshold.toString())

        binding.btnSaveCloudThreshold.setOnClickListener {
            val input = binding.editCloudThreshold.text.toString()
            val value = input.toIntOrNull()
            if (value == null || value < 0) {
                android.widget.Toast.makeText(this, "请输入有效的正整数", android.widget.Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }
            sharedPrefs.edit().putInt(KEY_CLOUD_THRESHOLD, value).apply()
            // 实时更新引擎配置
            AVLEngine.getInstance().updateConfig { config ->
                config?.updateCloudThreshold(value)
            }
            android.widget.Toast.makeText(this, "云查阈值已保存", android.widget.Toast.LENGTH_SHORT).show()
        }
    }

    private fun setupCloudSizeThreshold() {
        val sharedPrefs = getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        val sizeMb = sharedPrefs.getInt(KEY_CLOUD_SIZE_THRESHOLD, DEFAULT_CLOUD_SIZE_THRESHOLD)
        binding.editCloudSizeThreshold.setText(sizeMb.toString())

        binding.btnSaveCloudSizeThreshold.setOnClickListener {
            val input = binding.editCloudSizeThreshold.text.toString()
            val value = input.toIntOrNull()
            if (value == null || value <= 0) {
                android.widget.Toast.makeText(this, "请输入有效的正整数", android.widget.Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }
            sharedPrefs.edit().putInt(KEY_CLOUD_SIZE_THRESHOLD, value).apply()
            // 实时更新引擎配置（单位转为字节）
            AVLEngine.getInstance().updateConfig { config ->
                config?.updateCloudSizeThreshold(value * 1024L * 1024L)
            }
            android.widget.Toast.makeText(this, "云查文件大小阈值已保存", android.widget.Toast.LENGTH_SHORT).show()
        }
    }
} 