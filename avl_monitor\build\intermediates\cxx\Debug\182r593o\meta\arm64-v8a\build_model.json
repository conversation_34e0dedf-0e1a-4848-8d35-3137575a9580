{"abi": "ARM64_V8A", "info": {"abi": "ARM64_V8A", "bitness": 64, "deprecated": false, "default": true}, "cxxBuildFolder": "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\.cxx\\Debug\\182r593o\\arm64-v8a", "soFolder": "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\build\\intermediates\\cxx\\Debug\\182r593o\\obj\\arm64-v8a", "soRepublishFolder": "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\build\\intermediates\\cmake\\debug\\obj\\arm64-v8a", "abiPlatformVersion": 21, "cmake": {"effectiveConfiguration": {"inheritEnvironments": [], "variables": []}}, "variant": {"buildSystemArgumentList": [], "cFlagsList": [], "cppFlagsList": [], "variantName": "debug", "soFolder": "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\build\\intermediates\\cxx\\Debug\\182r593o\\obj", "soRepublishFolder": "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\build\\intermediates\\cmake\\debug\\obj", "cxxBuildFolder": "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\.cxx\\Debug\\182r593o", "intermediatesFolder": "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\build\\intermediates\\cxx\\Debug\\182r593o", "isDebuggableEnabled": true, "validAbiList": ["ARM64_V8A"], "buildTargetSet": [], "implicitBuildTargetSet": [], "cmakeSettingsConfiguration": "android-gradle-plugin-predetermined-name", "module": {"cxxFolder": "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\.cxx", "intermediatesBaseFolder": "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\build\\intermediates", "intermediatesFolder": "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\build\\intermediates\\cxx", "gradleModulePathName": ":avl_monitor", "moduleRootFolder": "D:\\Projects\\Android\\cccj-sdk\\avl_monitor", "moduleBuildFile": "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\build.gradle", "makeFile": "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\CMakeLists.txt", "buildSystem": "CMAKE", "ndkFolder": "D:\\Android\\Sdk\\ndk\\27.0.12077973", "ndkVersion": "27.0.12077973", "ndkSupportedAbiList": ["ARMEABI_V7A", "ARM64_V8A", "X86", "X86_64"], "ndkDefaultAbiList": ["ARMEABI_V7A", "ARM64_V8A", "X86", "X86_64"], "ndkDefaultStl": "LIBCXX_STATIC", "ndkMetaPlatforms": {"min": 21, "max": 35, "aliases": {"20": 19, "25": 24, "J": 16, "J-MR1": 17, "J-MR2": 18, "K": 19, "L": 21, "L-MR1": 22, "M": 23, "N": 24, "N-MR1": 24, "O": 26, "O-MR1": 27, "P": 28, "Q": 29, "R": 30, "S": 31, "Sv2": 32, "Tiramisu": 33, "UpsideDownCake": 34, "VanillaIceCream": 35}}, "ndkMetaAbiList": [{"abi": "ARMEABI_V7A", "bitness": 32, "deprecated": false, "default": true}, {"abi": "ARM64_V8A", "bitness": 64, "deprecated": false, "default": true}, {"abi": "X86", "bitness": 32, "deprecated": false, "default": true}, {"abi": "X86_64", "bitness": 64, "deprecated": false, "default": true}], "cmakeToolchainFile": "D:\\Android\\Sdk\\ndk\\27.0.12077973\\build\\cmake\\android.toolchain.cmake", "cmake": {"isValidCmakeAvailable": true, "cmakeExe": "D:\\Android\\Sdk\\cmake\\3.10.2.4988404\\bin\\cmake.exe", "minimumCmakeVersion": "3.10.2", "ninjaExe": "D:\\Android\\Sdk\\cmake\\3.10.2.4988404\\bin\\ninja.exe"}, "stlSharedObjectMap": {"LIBCXX_SHARED": {"ARMEABI_V7A": "D:\\Android\\Sdk\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\arm-linux-androideabi\\libc++_shared.so", "ARM64_V8A": "D:\\Android\\Sdk\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\aarch64-linux-android\\libc++_shared.so", "X86": "D:\\Android\\Sdk\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\i686-linux-android\\libc++_shared.so", "X86_64": "D:\\Android\\Sdk\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\x86_64-linux-android\\libc++_shared.so"}, "LIBCXX_STATIC": {}, "NONE": {}, "SYSTEM": {}}, "project": {"rootBuildGradleFolder": "D:\\Projects\\Android\\cccj-sdk", "sdkFolder": "D:\\Android\\Sdk", "isBuildOnlyTargetAbiEnabled": true, "ideBuildTargetAbi": "arm64-v8a,armeabi-v7a,armeabi", "isCmakeBuildCohabitationEnabled": false, "isPrefabEnabled": false}, "nativeBuildOutputLevel": "QUIET"}, "prefabClassPathFileCollection": [], "prefabPackageDirectoryListFileCollection": [], "stlType": "c++_static", "optimizationTag": "Debug"}, "buildSettings": {"environmentVariables": []}, "prefabFolder": "D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\.cxx\\Debug\\182r593o\\prefab\\arm64-v8a", "isActiveAbi": true, "fullConfigurationHash": "182r593o46h6s28426g3q4e332gy201b6r225b2n4f2o276p1x5y4t151q5b", "configurationArguments": ["-HD:\\Projects\\Android\\cccj-sdk\\avl_monitor", "-DCMAKE_SYSTEM_NAME=Android", "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON", "-DCMAKE_SYSTEM_VERSION=21", "-DANDROID_PLATFORM=android-21", "-DANDROID_ABI=arm64-v8a", "-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a", "-DANDROID_NDK=D:\\Android\\Sdk\\ndk\\27.0.12077973", "-DCMAKE_ANDROID_NDK=D:\\Android\\Sdk\\ndk\\27.0.12077973", "-DCMAKE_TOOLCHAIN_FILE=D:\\Android\\Sdk\\ndk\\27.0.12077973\\build\\cmake\\android.toolchain.cmake", "-DCMAKE_MAKE_PROGRAM=D:\\Android\\Sdk\\cmake\\3.10.2.4988404\\bin\\ninja.exe", "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\build\\intermediates\\cxx\\Debug\\182r593o\\obj\\arm64-v8a", "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\\Projects\\Android\\cccj-sdk\\avl_monitor\\build\\intermediates\\cxx\\Debug\\182r593o\\obj\\arm64-v8a", "-DCMAKE_BUILD_TYPE=Debug", "-BD:\\Projects\\Android\\cccj-sdk\\avl_monitor\\.cxx\\Debug\\182r593o\\arm64-v8a", "-<PERSON><PERSON><PERSON><PERSON>"]}