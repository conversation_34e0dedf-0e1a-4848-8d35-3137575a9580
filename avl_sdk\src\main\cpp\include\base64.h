/***********************************************************
 * Base64 library                                           *
 * <AUTHOR>                                  *
 * @date July 23, 2017                                      *
 * Purpose: encode and decode base64 format                 *
 * Copied from: https://github.com/elzoughby/Base64         *
 ***********************************************************/

#ifndef BASE46_H
#define BASE46_H

#include <memory.h>
#include <stdlib.h>

#ifdef __cplusplus
extern "C" {
#endif
/***********************************************
Encodes ASCCI string into base64 format string
@param plain String to be encoded
@param len String len, '\0' is allowed.
@return encoded base64 format string
***********************************************/
char* base64_encode(char* plain, int len);


/***********************************************
decodes base64 format string into ASCCI string
@param plain encoded base64 format string
@param len decoded string len, '\0' is counted.
@return ASCII string to be encoded
***********************************************/
char* base64_decode(const char* cipher, int* len);
#ifdef _cplusplus
};
#endif
#endif   // BASE46_H
