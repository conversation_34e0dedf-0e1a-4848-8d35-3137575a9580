@startuml
'https://plantuml.com/class-diagram
!pragma layout smetana

class FolderScanner implements Scanner{
    - fileList: List<File>
    - cloudScanner: CloudScanner
    + scan(folder: File): ScanResult
}

class FileScanner extends FolderScanner implements Scanner{
    - file: File
    - cacheManager: CacheManager
    - cloudScanner: CloudScanner
    - engineScanner: AbsEngine
    - fileChecker: FileChecker
    + scan(file: File)): ScanResult
}

class CacheManager {
    + hasCache(file: File): boolean
    + getCacheResult(file: File): ScanResult
    + storeScanResult(file: File, result: ScanResult): void
}

class CloudScanner implements Scanner{
    + scan(file: File): ScanResult
}

abstract class AbsEngine {
    + init()
    + scan(file: File): ScanResult
}

class MobileEngine extends AbsEngine {
    + init()
    + scan(file: File): ScanResult
}

class PcEngine extends AbsEngine {
    + init()
    + scan(file: File): ScanResult
}

class FileChecker {
    + isValidType(file: File): boolean
    + isAPKOrJAR(file: File): boolean
    + isFileSizeThreshold(file: File): boolean
}

class ScanResult {
    + isSafe: boolean
    + details: String
}

interface Scanner {
    + scan(file: File): ScanResult
}

FolderScanner --> FileScanner
FolderScanner --> CloudScanner

FileScanner --> CacheManager
CacheManager --> Database
CacheManager --> SharePreferences
FileScanner --> CloudScanner
FileScanner --> AbsEngine
FileScanner --> FileChecker
FileScanner --> ScanResult


@enduml