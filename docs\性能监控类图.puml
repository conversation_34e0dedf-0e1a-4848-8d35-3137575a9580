@startuml

title 杀毒引擎性能监控模块类图

package "Java/Kotlin层" {
  class MainActivity {
    -scanFileFinishTime: Long
    -scanFileStartTime: Long
    -startStabilityTime: Long
    -binding: ActivityMainBinding
    -startTime: Long
    -endTime: Long
    -isScan24Hours: Boolean
    -csvFilePath: String
    -mAdapter: LogAdapter
    +onCreate(savedInstanceState: Bundle?)
    +initAvlConfig()
    +initView()
    +initAVLEngine()
    +initListener()
    +scan(silent: Boolean)
    +createScanListener(): ScanListener
    +getDirectoryFileCount(directory: File): Int
    +againScan()
    +openFilePicker()
    +openDirPicker()
    +uriToFilePath(context: Context, uri: Uri): String?
    +initCsvFile()
    +writeToCsv(content: String)
    +writeScanResultToCsv(path: String, result: ResultScan?)
    -sendPipeMessage(msg: PipeMessage)
  }

  class AVLEngine {
    +scanDir(path: String, listener: ScanListener)
    +scanDirSilent(path: String, listener: ScanListener)
    -startCpuMonitor(): Process
    -getScanSilentListener(listener: ScanListener, process: Process): ScanListener
    -terminateProcess(process: Process)
  }

  enum PipeMessage {
    PAUSE("PAUSE")
    CONTINUE("CONTINUE")
    -message: String
    +getMessage(): String
  }

  class ScanListener {
    +scanStart()
    +scanStop()
    +scanFinish()
    +scanCount(count: Int)
    +scanFileStart(index: Int, path: String?)
    +scanFileFinish(index: Int, path: String?, result: ResultScan?)
  }
}

package "C层" {
  class "main.c" {
    -pid: int
    -g_disable_flag: bool
    -percent: int
    -interval: int
    -should_exit: bool
    -pthread_attr: pthread_attr_t
    -pthread_mutex: pthread_mutex_t
    -logging_thread: pthread_t
    -monitor_thread: pthread_t
    +main(argc: int, argv: char*[]): int
    +sig_handler(signo: int): void
    +monitor_func(arg: void*): void*
    +logging_func(arg: void*): void*
  }

  class "PID_STAT" {
    -utime: unsigned long long
    -stime: unsigned long long
    -tv: struct timeval
  }

  class "SYS_STAT" {
    -user: unsigned long long
    -nice: unsigned long long
    -system: unsigned long long
    -idle: unsigned long long
    -iowait: unsigned long long
    -irq: unsigned long long
    -softirq: unsigned long long
    -tv: struct timeval
  }

  class "工具函数" {
    +file_exists(path: const char*): bool
    +parse_pid_stat(pid_stat: PID_STAT*): bool
    +get_pid_delta(now: PID_STAT*, before: PID_STAT*): int
    +parse_sys_stat(sys_stat: SYS_STAT*): bool
    +get_cpu_delta(now: SYS_STAT*, before: SYS_STAT*): int
    +pid_time(pid_stat: PID_STAT): unsigned long long
    +sys_time(sys_stat: SYS_STAT): unsigned long long
  }
}

' 关系定义
MainActivity ..> PipeMessage : 使用
MainActivity ..> AVLEngine : 调用
MainActivity ..> ScanListener : 创建
AVLEngine --> ScanListener : 使用
"main.c" --> "PID_STAT" : 使用
"main.c" --> "SYS_STAT" : 使用
"main.c" --> "工具函数" : 调用

@enduml