// CloudScanner 使用示例
// CSV文件会在扫描完成后自动生成

import com.antiy.avlsdk.scan.CloudScanner;
import com.antiy.avlsdk.callback.ScanListener;
import com.antiy.avlsdk.entity.ResultScan;

import java.io.File;
import java.util.Arrays;
import java.util.List;

public class CloudScannerUsageExample {
    
    public void performCloudScanWithCsv() {
        // 准备要扫描的文件列表
        List<File> filesToScan = Arrays.asList(
            new File("/path/to/file1.apk"),
            new File("/path/to/file2.txt"),
            new File("/path/to/file3.exe")
        );
        
        // 创建扫描监听器
        ScanListener scanListener = new ScanListener() {
            @Override
            public void scanStart() {
                System.out.println("云扫描开始");
            }
            
            @Override
            public void scanStop() {
                System.out.println("云扫描停止");
            }
            
            @Override
            public void scanFinish() {
                System.out.println("云扫描完成 - CSV文件已自动生成");
                // CSV文件已在此时生成，格式：cloud_scan_results_YYYYMMDD_HHMMSS.csv
            }
            
            @Override
            public void scanCount(int count) {
                System.out.println("总共需要扫描 " + count + " 个文件");
            }
            
            @Override
            public void scanFileStart(int index, String filePath) {
                System.out.println("开始扫描文件 " + (index + 1) + ": " + filePath);
            }
            
            @Override
            public void scanFileFinish(int index, String filePath, ResultScan result) {
                System.out.println("完成扫描文件 " + (index + 1) + ": " + filePath);
                if (result.isMalicious) {
                    System.out.println("  检测到威胁: " + result.virusName);
                } else {
                    System.out.println("  文件安全");
                }
                // 此时该文件的数据已被收集到CSV记录中
            }
        };
        
        // 创建并启动云扫描器
        CloudScanner cloudScanner = new CloudScanner(filesToScan, scanListener, true);
        cloudScanner.startScan();
        
        // 扫描完成后，CSV文件将自动生成在应用私有目录：
        // /data/data/{package_name}/files/cloud_scan_results_YYYYMMDD_HHMMSS.csv
    }
}
