package com.antiy.avlsdk.license;

/**
 * License刷新回调接口
 * 用于通知license刷新过程中的各种事件
 * 实现观察者模式，与具体的业务逻辑解耦
 * 
 * <AUTHOR>
 * @version 1.0
 */
public interface RefreshCallback {
    
    /**
     * 刷新开始时调用
     * 在license刷新操作开始时触发
     */
    void onRefreshStart();
    
    /**
     * 刷新成功时调用
     * 在license刷新操作成功完成时触发
     */
    void onRefreshSuccess();
    
    /**
     * 刷新失败时调用
     * 在license刷新操作失败时触发
     * 
     * @param reason 失败原因描述
     */
    void onRefreshFailed(String reason);
    
    /**
     * 跳过刷新时调用
     * 当由于某种原因跳过刷新操作时触发
     * 
     * @param reason 跳过原因描述
     */
    void onRefreshSkipped(String reason);
    
    /**
     * 默认的空实现回调
     * 提供一个默认实现，避免客户端必须实现所有方法
     */
    class Empty implements RefreshCallback {
        @Override
        public void onRefreshStart() {
            // 默认空实现
        }
        
        @Override
        public void onRefreshSuccess() {
            // 默认空实现
        }
        
        @Override
        public void onRefreshFailed(String reason) {
            // 默认空实现
        }
        
        @Override
        public void onRefreshSkipped(String reason) {
            // 默认空实现
        }
    }
}
