package com.antiy.avlsdk.pc;

/**
ERR_SUCCESS	0	成功
ERR_UNKNOWN	-1	未知错误
ERR_INCORRECT_PARAM	-2	参数错误
ERR_NO_ENOUGH_MEM	-3	内存不足
ERR_BAD_LICENSE	-4	授权相关错误
ERR_BAD_AUTH_CODE	-5	授权码相关错误
ERR_BAD_ENGINE_VER	-6	引擎版本错误
ERR_BAD_DATABASE	-7	特征库错误
ERR_INIT_COMPONENT_FAILED	-8	初始化组件失败
ERR_MISS_IMPORTANT_MOD_FILE	-9	模块文件相关错误
ERR_MISS_IMPORTANT_DATA_FILE	-10	Data特征库文件相关错误
ERR_BAD_WORKING_PATH	-11	引擎路径相关错误
ERR_LOAD_CONFIG	-12	加载配置模板错误
ERR_SET_CONFIG	-13	配置设置错误
ERR_GET_CONFIG	-14	获取配置失败
ERR_SCAN_FAILED	-15	扫描失败

 */
public enum PCEngineInit {
    ERR_SUCCESS(0,"成功"),
    ERR_UNKNOWN(-1,"未知错误"),
    ERR_INCORRECT_PARAM(-2,"参数错误"),
    ERR_NO_ENOUGH_MEM(-3,"内存不足"),
    ERR_BAD_LICENSE(-4,"授权相关错误"),
    ERR_BAD_AUTH_CODE(-5,"授权码相关错误"),
    ERR_BAD_ENGINE_VER(-6,"引擎版本错误"),
    ERR_BAD_DATABASE(-7,"特征库错误"),
    ERR_INIT_COMPONENT_FAILED(-8,"初始化组件失败"),
    ERR_MISS_IMPORTANT_MOD_FILE(-9,"模块文件相关错误"),
    ERR_MISS_IMPORTANT_DATA_FILE(-10,"Data特征库文件相关错误"),
    ERR_BAD_WORKING_PATH(-11,"引擎路径相关错误"),
    ERR_LOAD_CONFIG(-12,"加载配置模板错误"),
    ERR_SET_CONFIG(-13,"配置设置错误"),
    ERR_GET_CONFIG(-14,"获取配置失败"),
    ERR_SCAN_FAILED(-15,"扫描失败");

    private final int code;
    private final String message;

    PCEngineInit(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }   
    // 通过code获取枚举
    public static PCEngineInit get(int code) {
        for (PCEngineInit error : PCEngineInit.values()) {
            if (error.getCode() == code) {
                return error;
            }
        }
        return null;
    }
}
