//
// Created by zos on 2024/9/14.
//

#ifndef DEMO_AVL_SDK_H
#define DEMO_AVL_SDK_H

#include <stdbool.h>

#define UUID_LEN          36   // c58a859c-7a1d-11ef-a2e1-644ed7b349a8 长度 36
#define MAX_CLIENT_ID_LEN 64   // 客户端id长度最大64

#define LOG_TAG "avl_sdk"

#define LOGI(fmt, ...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, fmt, __VA_ARGS__)
#define LOGD(fmt, ...) __android_log_print(ANDROID_LOG_DEBUG, LOG_TAG, fmt, __VA_ARGS__)
#define LOGE(fmt, ...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, fmt, __VA_ARGS__)

#ifdef AVLM_USE_AUTH
struct auth_args {
        char uuid[256];       // 设备唯一标识
        char clientId[256];   // SDK标识
        char license[256];    // 授权凭证
        int  licenseLen;      // 授权凭证长度
};
#endif

#ifdef AVLM_USE_AUTH
char* native_bytes_to_hex(unsigned char* bytes, int bytesLen, bool upperCase);
char* native_generate_key(const char* requestTime, int flag);
char* native_encrypt(const char* key, const char* clearText);
char* native_generate_auth_request(const char* requestTime, int flag);

char* native_decrypt(const char* key, const char* encryptedText, int* msgLen);
int   native_set_auth_params(const char* uuid, const char* clientId);
int   native_set_auth_token(const char* license, int licenseLen);
char* native_get_auth_info();
#endif

char*       native_get_running_arch();
int         native_init(const char* libPath, const char* dbPath);
int         native_release();
char*       native_scan(const char* path);
char*       native_get_cert_hash(const char* path);
char*       native_get_cert_full_hash(const char* path);
int         native_install_package(const char* package_path, int package_type, const char* to_version, const char* sig_package_type);
const char* native_get_engine_version();
const char* native_get_sig_version();

#endif   // DEMO_AVL_SDK_H
