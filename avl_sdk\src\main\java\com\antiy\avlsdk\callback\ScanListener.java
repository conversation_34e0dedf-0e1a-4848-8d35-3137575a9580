package com.antiy.avlsdk.callback;

import com.antiy.avlsdk.entity.ResultScan;
import com.antiy.avlsdk.entity.ScanErrorType;

/**
 * 2 * Copyright (C), 2020-2024
 * 3 * FileName: ScanListener
 * 4 * Author: wangbiao
 * 5 * Date: 2024/9/2 10:42
 * 6 * Description: 扫描监听器
 * 10
 */
public interface ScanListener {
    /** 扫描开始通知回调*/
    void scanStart();

    /** 扫描被终止通知回调*/
    void scanStop();

    /** 扫描结束通知回调*/
    void scanFinish();

    /** 需要扫描多少文件*/
    void scanCount(int count);

    /**
     * 单个文件扫描开始回调
     * @param index 第几个文件
     * @param path 要扫描的文件路径
     */
    void scanFileStart(int index, String path);

    /**
     * 单个文件扫描结束回调
     * @param index 第几个文件
     * @param path 扫描的文件的路径
     * @param result 扫描结果
     */
    void scanFileFinish(int index, String path, ResultScan result);

    /**
     * 扫描错误通知回调
     * @param errorType 错误类型
     * */
    void scanError(ScanErrorType errorType);
}
