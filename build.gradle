// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    repositories {
        mavenCentral()
        gradlePluginPortal()
        google()
    }
    dependencies {
        classpath "com.android.tools.build:gradle:7.0.4"
        classpath 'com.github.kezong:fat-aar:1.3.8'
        classpath 'org.jetbrains.kotlin:kotlin-gradle-plugin:1.8.0'
        classpath "org.jetbrains.dokka:dokka-gradle-plugin:1.9.10"  // 新增 Dokka
    }
}


allprojects {
    repositories {
        mavenCentral()
        gradlePluginPortal()
        google()
    }
}
task clean(type: Delete) {
    delete rootProject.buildDir
}