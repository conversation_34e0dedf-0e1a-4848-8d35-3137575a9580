package com.antiy.demo.fragment

import android.content.Intent
import android.util.Log
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.CompoundButton
import android.widget.Toast
import com.antiy.avlsdk.storage.DBConstants.TABLE_CACHE
import com.antiy.avlsdk.storage.DataManager
import com.antiy.demo.base.BaseFragment
import com.antiy.demo.databinding.FragmentSettingsBinding
import com.antiy.demo.activity.AntivirusSettingsActivity
import com.antiy.demo.activity.AboutActivity
import com.antiy.demo.activity.HelpFeedbackActivity
import com.antiy.demo.activity.CloudScanSettingsActivity
import com.antiy.demo.activity.PerformanceSettingsActivity

class SettingsFragment : BaseFragment<FragmentSettingsBinding>() {
    override fun getViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): FragmentSettingsBinding {
        return FragmentSettingsBinding.inflate(inflater, container, false)
    }

    override fun initView() {
        // 初始化深色模式开关
        binding.switchDarkMode.setOnCheckedChangeListener { _: CompoundButton, isChecked: Boolean ->
            // 处理深色模式切换
            // 这里可以添加实际的深色模式切换逻辑
        }
    }

    override fun initListener() {
        // 用户信息点击事件
        binding.layoutUserProfile.setOnClickListener {
            // 跳转到用户信息页面
        }

        // 通知设置点击事件
        binding.layoutNotification.setOnClickListener {
            // 跳转到通知设置页面
        }

        // 语言设置点击事件
        binding.layoutLanguage.setOnClickListener {
            // 跳转到语言设置页面
        }

        // 杀毒引擎设置点击事件
        binding.layoutAntivirusEngine.setOnClickListener {
            // 跳转到杀毒引擎设置页面
            startActivity(Intent(requireContext(), AntivirusSettingsActivity::class.java))
        }

        // 云查杀设置点击事件
        binding.layoutCloudScan.setOnClickListener {
            // 跳转到云查杀设置页面
            CloudScanSettingsActivity.start(requireContext())
        }

        // 性能设置点击事件
        binding.layoutPerformance.setOnClickListener {
            // 跳转到性能设置页面
            PerformanceSettingsActivity.start(requireContext())
        }

        // 关于点击事件
        binding.layoutAbout.setOnClickListener {
            AboutActivity.start(requireContext())
        }

        // 帮助与反馈点击事件
        binding.layoutHelp.setOnClickListener {
            HelpFeedbackActivity.start(requireContext())
        }

        // 退出登录点击事件
        binding.layoutLogout.setOnClickListener {
            // 处理退出登录逻辑
        }

        binding.clearCacheLl.setOnClickListener {
            Log.e(SettingsFragment::class.java.simpleName, "清除缓存")
            DataManager.getInstance().clearAllData(TABLE_CACHE)
            Toast.makeText(requireContext(), "清除成功", Toast.LENGTH_SHORT).show()
        }

    }
}