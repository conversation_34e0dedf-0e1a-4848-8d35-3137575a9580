@startuml 激活流程 - 活动图
' 让响应消息的文本在箭头下方
skinparam ResponseMessageBelowArrow true

' 设置导出图形的字体和字号
skinparam DefaultFontName "Microsoft YaHei"
skinparam DefaultFontSize 12

' 顺序图中的actor样式
skinparam actorStyle hollow

' 大概讲清楚激活过程，包括如何获取授权信息、如何加载引擎授权

start

fork
    :SDK初始化|
    note left
    {{
        participant 引擎 as so
        participant SDK as sdk
        actor Dev<PERSON>per as dev
        participant L运维 as ops
        participant 更新渠道 as channel
        participant 安全运维 as sec_ops
        dev -> ops : 申请引擎so
        dev <-- ops: 引擎so & 文档
        dev -> channel : 申请病毒库全量包
        dev <-- channel : 全量包
        dev -> sec_ops : 申请license文件
        dev <-- sec_ops : license文件
        dev -> sdk : 部署引擎/病毒库/license
        sdk -> so : init(sig_lib, license)
        note over so
            这里init函数参数为虚构
            实际license存放在sig目录
            直接init即可
        end note
        sdk <-- so : 初始化完成,引擎可用
        dev <-- sdk : 继续后续开发测试
    }}
    end note
fork again
    :服务端初始化|
    note right
    {{
        actor L研发 as ops
        actor Developer as dev
        participant 服务端工具 as server_tool
        dev -> server_tool : 收集服务器信息
        dev <-- server_tool : 服务器信息
        dev -> ops : 服务器信息
        dev <-- ops : 服务器激活conf文件
        dev -> server_tool : 应用conf文件
        dev <-- server_tool : 生成license文件
    }}
    end note
end fork

:SDK生成授权请求|
note left
{{
    participant "SDK jni库" as so
    participant SDK as sdk
    sdk <--    :预定义ClientI
    sdk -> sdk    :生成UUID并保存
    sdk -> sdk    :生成请求时间戳并保存
    sdk -> sdk    :生成随机数并保存
    sdk -> so : generate_auth_request(uuid, client_id, requestTime, flag)
    sdk <-- so : 加密请求内容
    sdk --> : 申请授权凭证
}}
end note

:从服务端请求授权凭证|
note right
{{
    participant SDK as sdk
    participant 服务端 as server
    participant 服务端工具 as server_tool

    note over sdk,server
    end note
    sdk -> server : 发送请求http_request:\n{\n\tuuid: 上面保存,\n\tclient_id: 上面保存,\n\trequestTime: 上面保存,\n\tflag: 上面保存,\n\trequest_body:\n\t\t上一个过程生成的加密请求内容\n}

    note over server
    end note
    server -> server_tool : 从数据库中读取start/end等配置
    server <-- server_tool : 加密后的凭证
    sdk <-- server : http_response: 加密后的凭证\n凭证解密所需信息:\n\tresponseTime,随机数等
}}
end note

:SDK使用授权凭证初始化|
note left
{{
    participant SDK as sdk
    participant "SDK jni库" as so
    participant 引擎库 as antiy_so

    sdk -> so : 解密凭证(responseTime, flag,...)
    so -> antiy_so : 带授权凭证的初始化方法
    so <-- antiy_so : 初始化成功
    sdk <-- so : 开始使用

}}
end note

stop
@enduml

@startuml 激活流程-时序图

' 让响应消息的文本在箭头下方
skinparam ResponseMessageBelowArrow true

' 设置导出图形的字体和字号
skinparam DefaultFontName "Microsoft YaHei"
skinparam DefaultFontSize 12

participant 引擎so as so
participant JNI as jni
participant SDK as sdk
participant 服务端工具 as server_tool
participant 授权平台服务 as server
participant 项目组 as T
participant 方向L as L
participant 安全运维 as antiy_ops

group #1f1 获取so
    T -> L : 申请so
    note over L
        处理人: 方向L胡正
    end note
    T <-- L : so库文件
end

group #ff1 获取病毒库
    T -> L : 申请病毒库全量包
    T <-- L : 全量包<total.zip>
end

group #1ff 申请so所用license
    T -> antiy_ops :提交数据
    note over antiy_ops
        处理人: 安全运维组周媛媛
    end note
    T <-- antiy_ops: 授权License & 配套SDK
end

group #afa 激活服务端
    T -> server_tool : 收集硬件数据
    T <-- server_tool : 硬件数据
    T -> L : 提交硬件信息
    T <-- L : 返回授权conf文件
    T -> server : 上传conf文件
    server -> server_tool : 生成license文件
    server_tool <-- server_tool : 使用license文件
end

== 使用SDK ==

group 生成请求密文
    sdk --> sdk : 生成UUID并保存
    sdk -> jni : 加密(uuid, client_id, requestTime, flag)
    note right
    uuid: 使用上面保存的uuid
    client_id: 分配给当前sdk的唯一id
    requestTime: 当前请求时间
    flag: 随机数
    end note
    sdk <-- jni : base64编码的密文
end
group 请求授权凭证
    sdk -> server : 发送激活请求
    note over sdk,server
        header {
            uuid: 当前保存的UUID
            requestTime: 上面加密时使用的requestTime
            flag: 随机数
        }
        body {
            "content": base64编码的密文
        }
    end note
    sdk <-- server: 响应授权数据

    sdk -> sdk : 解析响应头和body
    note over sdk,server
        header {
            Responsetime: 响应时间
            Flag: 响应随机数
        }
        body {
            data: base64编码的密文
        }
    end note
    sdk -> sdk : 保存Resposnetime,Flag,data
end

group 解密license,应用license
    sdk -> jni : 解密数据(uuid, responseTime, flag, base64编码的密文)
    jni -> so : 解密后的license数据
end

@enduml
