package com.antiy.demo

import android.content.Context
import android.content.SharedPreferences
import android.content.pm.PackageManager
import android.util.Base64
import android.util.Log
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.platform.app.InstrumentationRegistry
import com.antiy.avlsdk.AVLEngine
import com.antiy.avlsdk.scan.CacheManager
import com.antiy.avlsdk.storage.DBConstants
import com.antiy.avlsdk.storage.DataManager
import com.antiy.avlsdk.storage.EncryptorHelper
import com.antiy.avlsdk.utils.SdkConst
import org.json.JSONObject
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import java.io.File
import java.nio.charset.StandardCharsets
import java.security.MessageDigest

@RunWith(AndroidJUnit4::class)
class StorageTest {
    private lateinit var context: Context
    private lateinit var sharedPreferences: SharedPreferences
    private lateinit var dataManager: DataManager

    @Before
    fun setUp() {
        context = InstrumentationRegistry.getInstrumentation().targetContext
        sharedPreferences = context.getSharedPreferences(DBConstants.SHARE_PREFERENCES_CONFIG, Context.MODE_PRIVATE)
        // 清除之前的配置
        sharedPreferences.edit().clear().apply()
        
        // 初始化引擎
        val uuid = sharedPreferences.getString(SdkConst.AUTH_UUID, "")
        AVLEngine.init(context, uuid, ALog(context), NetworkManager())
        dataManager = DataManager.getInstance()
        
        // 清除所有测试数据
        dataManager.clearAllData(DBConstants.TABLE_BLACK_WHITE)
        dataManager.clearAllData(DBConstants.TABLE_CACHE)
    }

    @Test
    fun testDefaultToken() {
        // 准备默认配置
        val defaultConfig = JSONObject().apply {
            put("uuid", "00000000-0000-0000-0000-000000000000")
            put("client_id", "zhanch")
            put("token", "fOz/DjxF2WBlH/Q1lgTDz/oPQr613zoDp+8y0uDV4mJja57ANpeSyKXziKe9L8quzf+Rrf+PdbG++kXe/5ZTd3tAaGiX1u7t6XynLd5mdZyq1eeD+ZGk+VRZrQCUWjxVyer1e3+yXeqy9rvUpfXOxKYxjRIOPvnrMF19tcLgL3jHCYHC42ytzaMSDIZFCAkEgnZ7iB6TjdDFTocP56E+OrK/LAMJD1iDnHSxOn2PylE9tfCBRHJmXe3pvfnqN9YoBQAAAA==")
        }

        // 获取应用签名作为加密密钥
        val packageInfo = context.packageManager.getPackageInfo(
            context.packageName,
            PackageManager.GET_SIGNATURES
        )
        val signature = Base64.encodeToString(
            packageInfo.signatures[0].toByteArray(),
            Base64.NO_WRAP
        )

        // 使用SHA-256生成加密密钥
        val digest = MessageDigest.getInstance("SHA-256")
        val key = digest.digest(signature.toByteArray(StandardCharsets.UTF_8))

        // 加密配置
        val configBytes = defaultConfig.toString().toByteArray(StandardCharsets.UTF_8)
        val encrypted = ByteArray(configBytes.size)
        for (i in configBytes.indices) {
            encrypted[i] = (configBytes[i].toInt() xor key[i % key.size].toInt()).toByte()
        }

        // Base64编码
        val encoded = Base64.encodeToString(encrypted, Base64.NO_WRAP)

        // 保存到文件
        val assetsDir = File(context.getExternalFilesDir(null), "src/main/assets")
        if (!assetsDir.exists()) {
            assetsDir.mkdirs()
        }

        val configFile = File(assetsDir, "auth_config.bin")
        configFile.writeText(encoded)

        // 验证文件是否创建成功
        assertTrue("配置文件应该被创建", configFile.exists())
        assertTrue("配置文件不应该为空", configFile.length() > 0)

        // 输出文件路径，方便后续使用
        println("配置文件已生成：${configFile.absolutePath}")

        // 测试解密（可选）
        val decrypted = ByteArray(encrypted.size)
        for (i in encrypted.indices) {
            decrypted[i] = (encrypted[i].toInt() xor key[i % key.size].toInt()).toByte()
        }
        val decryptedConfig = String(decrypted, StandardCharsets.UTF_8)
        val decryptedJson = JSONObject(decryptedConfig)

        // 验证解密后的内容是否正确
        assertEquals("UUID应该匹配", 
            defaultConfig.getString("uuid"), 
            decryptedJson.getString("uuid")
        )
        assertEquals("Client ID应该匹配", 
            defaultConfig.getString("client_id"), 
            decryptedJson.getString("client_id")
        )
        assertEquals("Token应该匹配", 
            defaultConfig.getString("token"), 
            decryptedJson.getString("token")
        )
    }

    /**
     * 测试配置参数存储功能
     * 验证:
     * 1. 正常值的存储和读取
     * 2. 无效值的处理(返回默认值)
     */
    @Test
    fun testPreferencesStorage() {
        // 测试阈值存储
        dataManager.saveCloudCheckThreshold(800)
        assertEquals(800, dataManager.getCloudCheckThreshold())

        dataManager.saveCloudSizeThreshold(50 * 1024 * 1024)
        assertEquals(50 * 1024 * 1024, dataManager.getCloudSizeThreshold())

        dataManager.saveSilentPerformanceThreshold(90)
        assertEquals(90, dataManager.getSilentPerformanceThreshold())

        // 测试无效值处理
        dataManager.saveCloudCheckThreshold(-1)
        assertEquals("无效值应返回默认值500", 500, dataManager.getCloudCheckThreshold())

        dataManager.saveSilentPerformanceThreshold(-1)
        assertEquals("无效值应返回默认值100", 100, dataManager.getSilentPerformanceThreshold())
    }

    /**
     * 测试黑白名单存储功能
     * 验证:
     * 1. 批量存储和读取
     * 2. 单个数据查询
     * 3. 清除功能
     */
    @Test
    fun testBlackWhiteListStorage() {
        // 测试黑白名单存储
        val testMap = HashMap<String, Boolean>().apply {
            put("test_hash1", true)
            put("test_hash2", false)
            put("test_hash3", true)
        }
        
        dataManager.saveBlackWhiteList(testMap)
        val savedMap = dataManager.getBlackWhiteListData()
        
        assertEquals(3, savedMap.size)
        assertTrue(savedMap["test_hash1"] == true)
        assertTrue(savedMap["test_hash2"] == false)
        
        // 测试单个查询
        val entity = dataManager.getBlackWhiteData("test_hash1")
        assertNotNull(entity)
        assertTrue(entity.isBlack)
        
        // 测试清除功能
        dataManager.clearBlackWhiteList()
        val emptyMap = dataManager.getBlackWhiteListData()
        assertTrue(emptyMap.isEmpty())
    }

    /**
     * 测试缓存存储基本功能
     * 验证:
     * 1. 缓存数据的存储和读取
     * 2. 缓存过期清理机制
     * 3. 全量查询功能
     */
    @Test
    fun testCacheStorage() {
        val currentTime = System.currentTimeMillis()
        
        // 测试缓存存储
        dataManager.saveCacheData("cache_hash1", "virus1", currentTime)
        dataManager.saveCacheData("cache_hash2", "virus2", currentTime)
        
        // 测试单个缓存查询
        val cache1 = dataManager.getCacheData("cache_hash1")
        assertNotNull(cache1)
        assertEquals("virus1", cache1.virusName)
        assertEquals(currentTime, cache1.timestamp)
        
        // 测试所有缓存查询
        val allCaches = dataManager.getAllCacheData()
        assertEquals(2, allCaches.size)
        
        // 测试缓存维护
        dataManager.saveHistoryTimeout(1000) // 设置1秒超时
        Thread.sleep(1500) // 等待超过超时时间
        dataManager.maintainDatabase() // 执行维护
        val remainingCaches = dataManager.getAllCacheData()
        assertTrue(remainingCaches.isEmpty())
    }

    /**
     * 测试缓存容量限制功能
     * 验证:
     * 1. 超出容量限制时的处理
     * 2. 保留最新数据的机制
     */
    @Test
    fun testCacheSizeLimit() {
        // 测试缓存大小限制
        dataManager.saveHistorySize(2) // 设置缓存限制为2条
        val currentTime = System.currentTimeMillis()
        
        // 存入3条数据
        dataManager.saveCacheData("cache_hash1", "virus1", currentTime)
        dataManager.saveCacheData("cache_hash2", "virus2", currentTime + 1000)
        dataManager.saveCacheData("cache_hash3", "virus3", currentTime + 2000)
        
        // 执行维护
        dataManager.maintainDatabase()
        
        // 验证只保留最新的2条数据
        val caches = dataManager.getAllCacheData()
        assertEquals(2, caches.size)
        assertTrue(caches.any { it.hash == "cache_hash2" })
        assertTrue(caches.any { it.hash == "cache_hash3" })
    }

    /**
     * 测试数据库基本操作
     * 验证:
     * 1. 单条数据的增删改查
     * 2. 批量数据清除功能
     */
    @Test
    fun testDatabaseOperations() {
        // 测试数据库基本操作
        val currentTime = System.currentTimeMillis()
        dataManager.saveCacheData("test_hash", "test_virus", currentTime)
        
        // 测试删除单条数据
        dataManager.deleteData(DBConstants.TABLE_CACHE, DBConstants.CACHE_HASH, "test_hash")
        val deletedCache = dataManager.getCacheData("test_hash")
        assertNull(deletedCache)
        
        // 测试清除所有数据
        dataManager.saveCacheData("test_hash1", "virus1", currentTime)
        dataManager.saveCacheData("test_hash2", "virus2", currentTime)
        dataManager.clearAllData(DBConstants.TABLE_CACHE)
        val allCaches = dataManager.getAllCacheData()
        assertTrue(allCaches.isEmpty())
    }

    /**
     * 测试加密存储功能
     * 验证:
     * 1. 数据加密存储
     * 2. 加密数据的检索
     * 3. 原始数据和加密数据的对应关系
     */
    @Test
    fun testEncryption() {
        // 测试加密存储功能
        val testHash = "test_file_hash"
        val encryptedHash = EncryptorHelper.md5(testHash)
        
        // 保存加密后的数据
        dataManager.saveCacheData(encryptedHash, "test_virus", System.currentTimeMillis())
        
        // 验证使用加密hash能够正确检索
        val cache = dataManager.getCacheData(encryptedHash)
        assertNotNull("应能通过加密hash找到缓存", cache)
        assertEquals("test_virus", cache.virusName)
        
        // 验证使用原始hash无法检索
        val nullCache = dataManager.getCacheData(testHash)
        assertNull("不应通过原始hash找到缓存", nullCache)
    }

    /**
     * 测试黑白名单边界情况
     * 验证:
     * 1. 特殊字符处理
     * 2. 空值处理
     * 3. 中文字符处理
     */
    @Test
    fun testBlackWhiteListEdgeCases() {
        // 测试特殊字符
        val specialCharsMap = HashMap<String, Boolean>().apply {
            put("!@#$%^&*()", true)
            put("中文测试", false)
            put(" ", true)
            put("", false)  // 空字符串
        }
        
        dataManager.saveBlackWhiteList(specialCharsMap)
        val savedMap = dataManager.getBlackWhiteListData()
        
        // 验证特殊字符处理
        assertTrue(savedMap.containsKey("!@#$%^&*()"))
        assertTrue(savedMap.containsKey("中文测试"))
        assertTrue(savedMap.containsKey(" "))
        assertTrue(savedMap.containsKey(""))
        
        // 测试单个查询
        val entity = dataManager.getBlackWhiteData("中文测试")
        assertNotNull(entity)
        assertFalse(entity.isBlack)
    }

    /**
     * 测试并发访问情况
     * 验证:
     * 1. 多线程并发写入
     * 2. 数据一致性
     * 3. 线程安全性
     */
    @Test
    fun testConcurrentAccess() {
        // 测试并发访问
        val threads = mutableListOf<Thread>()
        val startTime = System.currentTimeMillis()
        
        // 创建10个线程同时写入数据
        repeat(10) { index ->
            threads.add(Thread {
                repeat(100) { i ->
                    val hash = "hash_${index}_$i"
                    dataManager.saveCacheData(hash, "virus_$i", startTime + i)
                }
            }.apply { start() })
        }
        
        // 等待所有线程完成
        threads.forEach { it.join() }
        
        // 验证数据完整性
        val allCaches = dataManager.getAllCacheData()
        assertTrue("应至少有1000条数据", allCaches.size >= 1000)
        
        // 验证没有重复数据
        val uniqueHashes = allCaches.map { it.hash }.toSet()
        assertEquals("不应有重复数据", allCaches.size, uniqueHashes.size)
    }

    /**
     * 测试大数据集��理能力
     * 验证:
     * 1. 大量数据的写入性能
     * 2. 容量限制功能
     * 3. 数据清理机制
     */
    @Test
    fun testLargeDatasets() {
        // 测试大数据集处理
        val startTime = System.currentTimeMillis()
        
        // 插入大量数据
        repeat(5000) { i ->
            dataManager.saveCacheData("hash_$i", "virus_$i", startTime + i)
        }
        
        // 设置缓存大小限制为3000
        dataManager.saveHistorySize(3000)
        
        // 执行维护
        dataManager.maintainDatabase()
        
        // 验证数据被正确限制
        val remainingCaches = dataManager.getAllCacheData()
        assertEquals("应该只保留3000条数据", 3000, remainingCaches.size)
        
        // 验证保留的是最新的数据
        val oldestTimestamp = remainingCaches.minByOrNull { it.timestamp }?.timestamp ?: 0
        assertTrue("应该保留最新的数据", oldestTimestamp > startTime + 1999)
    }

    /**
     * 测试数据库恢复能力
     * 验证:
     * 1. 数据库重置后的恢复
     * 2. 重新初始化后的正常操作
     */
    @Test
    fun testDatabaseRecovery() {
        // 测试数据库恢复能力
        val currentTime = System.currentTimeMillis()
        
        // 先保存一些数据
        dataManager.saveCacheData("test_hash1", "virus1", currentTime)
        dataManager.saveCacheData("test_hash2", "virus2", currentTime)
        
        // 模拟数据库损坏（通过清除数据）
        dataManager.clearAllData(DBConstants.TABLE_CACHE)
        
        // 重新初始化数据管理器
        val newDataManager = DataManager.getInstance()
        
        // 验证可以继续正常操作
        newDataManager.saveCacheData("test_hash3", "virus3", currentTime)
        val cache = newDataManager.getCacheData("test_hash3")
        assertNotNull("应该能够在重新初始化后正常操作", cache)
        assertEquals("virus3", cache.virusName)
    }

    /**
     * 测试无效输入处理
     * 验证:
     * 1. null值处理
     * 2. 超长字符串处理
     * 3. 无效时间戳处理
     */
    @Test
    fun testInvalidInputs() {
        // 测试无效输入处理
        
        // 测试null值
        dataManager.saveCacheData("null_test", null, System.currentTimeMillis())
        val nullCache = dataManager.getCacheData("null_test")
        assertNotNull("应该能处理null病毒名", nullCache)
        
        // 测试超长字符串
        val longHash = "a".repeat(1000)
        val longVirusName = "v".repeat(1000)
        dataManager.saveCacheData(longHash, longVirusName, System.currentTimeMillis())
        val longCache = dataManager.getCacheData(longHash)
        assertNotNull("应该能处理超长字符串", longCache)
        
        // 测试特殊时间戳
        dataManager.saveCacheData("time_test", "virus", -1)
        val timeCache = dataManager.getCacheData("time_test")
        assertNotNull("应该能处理无效时间戳", timeCache)
    }

    @Test
    fun testCacheStore() {
        CacheManager.storeScanResult("/sdcard/samples/apk/100/100/09F1941601DCB705EFAB10D5FAAB1C64","RiskWare/Android.repackage.a[fra,rog,crt]")
        val cache = CacheManager.getCacheResult("/sdcard/samples/apk/100/100/09F1941601DCB705EFAB10D5FAAB1C64")
        Log.e("testCacheStore", cache.virusName)
        Log.e("testCacheStore", cache.hash)
        Log.e("testCacheStore", cache.timestamp.toString())
        assertNotNull(cache)
        assertEquals(cache.virusName,"RiskWare/Android.repackage.a[fra,rog,crt]")

    }
} 