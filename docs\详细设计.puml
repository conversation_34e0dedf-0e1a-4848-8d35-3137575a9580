@startuml 接口关系时序
title 接口关系时序

skinparam responseMessageBelowArrow true

participant VIDS as vids
participant AVLSDK as sdk

== 加载: 以下需顺序执行 ==

group 初始化
vids -> sdk : init(context, id, logger, tunnel)
vids <-- sdk : 初始化结果
end

group 加载配置  [多次]
vids -> sdk : loadConfig(key, value)
vids <-- sdk : 配置加载是否成功
end

== 使用: 以下两类操作互斥 ==

group 更新 [可选]
vids -> sdk : checkUpdate()
vids <-- sdk : 更新结果
end

group 扫描

group 单文件扫描
vids -> sdk : scanFile(path)
vids <-- sdk : 扫描结果

end

group 目录扫描 [与静默扫描互斥]
vids -> sdk : scanDir(path, callback)
vids <-- sdk: 通过callback通知

note over vids,sdk
扫描过程中可以暂停,恢复和终止扫描
endnote
end

group 静默扫描扫描 [与目录扫描互斥]
vids -> sdk : scanDirSilent(path, callback)
vids <-- sdk: 通过callback通知

note over vids,sdk
扫描过程中可以暂停,恢复和终止扫描
endnote
end

end

@enduml