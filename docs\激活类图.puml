@startuml
title AVL SDK 核心类图

class AVLEngine {
  -static AVLEngine instance
  -Context mContext
  -Config config
  -IScanner scanner
  +static ResultInit init()
  +ResultUpdate checkUpdate()
  +void loadConfig()
  +void scanFile()
  +void scanDir()
  +void release()
}

class AVLCoreEngine {
  -static AVLCoreEngine instance
  +void prepareData()
  +void setupAuthParams()
  +String generateAuthRequest()
  +String decryptAuthToken()
  +int init()
  +void release()
}

class AuthInfoManager {
  -static AuthInfoManager instance
  -SecureStorage secureStorage
  -JSONObject defaultConfig
  +void loadDefaultConfig()
  +JSONObject getAuthInfo()
  +void saveAuthInfo()
  +String getUuid()
  +String getClientId()
  +String getToken()
}

class LicenseManager {
  -AuthInfoManager authInfoManager
  +void requestAuthToken()
  -String generateAuthRequest()
  -void saveAuthInfo()
}

class AVLEnginePC {
  -static AVLEnginePC instance
  +void prepareData()
  +int init()
  +void unloadEngine()
  +String getDbInfo()
}

class SecureStorage {
  -static SecureStorage instance
  +void saveData()
  +String readData()
}

enum InitValue {
  SUCCESS
  UNKNOWN_ERROR
  DB_VERIFY_FAILED
  TOKEN_DECRYPT_FAILED
  AUTH_FAILED
  ALREADY_INITIALIZED
}

class ResultInit {
  +boolean isSuccess
  +String reason
}

AVLEngine --> AVLCoreEngine
AVLEngine --> AVLEnginePC
AVLEngine --> AuthInfoManager
AVLEngine --> LicenseManager
AVLEngine --> ResultInit
AuthInfoManager --> SecureStorage
LicenseManager --> AuthInfoManager
ResultInit --> InitValue

@enduml