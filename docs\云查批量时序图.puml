@startuml CloudScanner云扫描流程时序图(修复后)

!theme plain
skinparam backgroundColor #FFFFFF
skinparam sequenceArrowThickness 2
skinparam roundcorner 20
skinparam maxmessagesize 60
skinparam sequenceParticipant underline

' 定义颜色用于标记修复点
skinparam note {
  BackgroundColor #E8F5E8
  BorderColor #4CAF50
}

actor Client as "调用方"
participant AVLEng<PERSON> as "AVLEngine"
participant ScannerFactory as "ScannerFactory"
participant CloudScanner as "CloudScanner"
participant hashExecutor as "hashExecutor\n线程池"
participant scanExecutor as "scanExecutor\n线程池"
participant taskQueue as "taskQueue\n任务队列"
participant CloudScanService as "CloudScanService"
participant ScanListener as "ScanListener\n回调"
participant FileScanner as "FileScanner\n本地扫描"

== 扫描初始化阶段 ==

Client -> AVLEngine: scanDir(path, listener)
activate AVLEngine

AVLEngine -> AVLEngine: validateScanRequest()
AVLEngine -> AVLEngine: prepareFileList(path)
AVLEngine -> AVLEngine: shouldUseCloudScanner(files)
note right: 判断条件:\n1. 文件数量 > 阈值\n2. 网络可用

AVLEngine -> ScannerFactory: createScanner(CLOUD, files, listener)
activate ScannerFactory
ScannerFactory -> CloudScanner: new CloudScanner(files, callback, true)
activate CloudScanner
CloudScanner -> CloudScanner: 初始化hashExecutor线程池
CloudScanner -> CloudScanner: 初始化scanExecutor线程池
CloudScanner -> CloudScanner: 初始化taskQueue队列
CloudScanner -> CloudScanner: 初始化preCalculatedHashes映射
note right #FFE0B2: 🔧 新增功能:\n支持预计算哈希值\n避免重复计算
ScannerFactory --> AVLEngine: CloudScanner实例
deactivate ScannerFactory

AVLEngine -> CloudScanner: startScan()

== 任务提交阶段 ==

CloudScanner -> CloudScanner: 检查引擎初始化状态
loop 遍历每个文件
    CloudScanner -> CloudScanner: 检查hashExecutor状态
    note right #E8F5E8: ✅ 修复点1:\n添加线程池状态检查\n防止在已关闭的线程池中提交任务

    alt hashExecutor未关闭
        CloudScanner -> hashExecutor: execute(lambda)
        activate hashExecutor
        note right: 为每个文件提交\n哈希计算任务

        hashExecutor -> hashExecutor: 检查isStopped状态
        hashExecutor -> taskQueue: put(new ScanTask(index, file))
        activate taskQueue
        note right: 创建ScanTask并\n放入队列等待处理
        taskQueue --> hashExecutor: 任务入队成功
        deactivate taskQueue
        deactivate hashExecutor
    else hashExecutor已关闭
        CloudScanner -> CloudScanner: 记录错误并跳出循环
        note right #FFCDD2: 避免RejectedExecutionException
    end
end

CloudScanner -> CloudScanner: 检查scanExecutor状态
note right #E8F5E8: ✅ 修复点2:\n启动前检查线程池状态

alt scanExecutor未关闭
    CloudScanner -> scanExecutor: execute(this::processScan)
    activate scanExecutor
    note right: 启动扫描处理线程
else scanExecutor已关闭
    CloudScanner -> CloudScanner: 记录错误
end

== 批量处理阶段 ==

scanExecutor -> scanExecutor: processScan()
loop 处理扫描任务
    scanExecutor -> scanExecutor: handlePause()
    scanExecutor -> taskQueue: poll()
    activate taskQueue
    taskQueue --> scanExecutor: ScanTask
    deactivate taskQueue

    scanExecutor -> scanExecutor: 添加到batch

    alt batch达到BATCH_SIZE或队列为空
        scanExecutor -> scanExecutor: processCloudScanBatch(batch)

        == 哈希计算阶段 ==
        scanExecutor -> scanExecutor: calculateBatchHashes(batch)
        loop 计算每个文件的哈希
            alt 有预计算哈希值
                scanExecutor -> scanExecutor: 使用preCalculatedHashes
                note right #E8F5E8: ✅ 性能优化:\n使用预计算哈希值\n避免重复计算
            else 无预计算哈希值
                scanExecutor -> scanExecutor: EncryptorHelper.calcPathSHA256()
                note right: 现场计算哈希值
            end
        end

        == 云服务调用阶段 ==
        scanExecutor -> CloudScanService: batchScanAsync(hashes, callback)
        activate CloudScanService
        CloudScanService -> CloudScanService: 创建网络请求
        CloudScanService -> CloudScanService: 发送POST请求到云端

        alt 云扫描成功
            CloudScanService --> scanExecutor: onSuccess(results)
            deactivate CloudScanService

            == 结果处理阶段 ==
            scanExecutor -> scanExecutor: processCloudScanResults(batch, results)
            loop 处理每个扫描结果
                scanExecutor -> scanExecutor: processIndividualScanResult(task, cloudScan)
                scanExecutor -> ScanListener: scanFileStart(index, path)
                activate ScanListener
                ScanListener --> scanExecutor:
                deactivate ScanListener

                scanExecutor -> scanExecutor: createScanResult(cloudScan)
                scanExecutor -> ScanListener: scanFileFinish(index, path, result)
                activate ScanListener
                ScanListener --> scanExecutor:
                deactivate ScanListener

                scanExecutor -> scanExecutor: CacheManager.storeScanResult()
                scanExecutor -> scanExecutor: incrementAndCheckFinish()
            end

        else 云扫描失败
            CloudScanService --> scanExecutor: onError(error)
            deactivate CloudScanService

            == 错误处理和降级阶段 ==
            scanExecutor -> scanExecutor: handleScanError(batch, error)

            alt isFromCloudCheck = true
                scanExecutor -> scanExecutor: processLocalScanBatch(batch)
                loop 本地扫描降级
                    scanExecutor -> ScanListener: scanFileStart(index, path)
                    activate ScanListener
                    ScanListener --> scanExecutor:
                    deactivate ScanListener

                    scanExecutor -> FileScanner: scan(filePath, true)
                    activate FileScanner
                    FileScanner --> scanExecutor: ResultScan
                    deactivate FileScanner

                    scanExecutor -> ScanListener: scanFileFinish(index, path, result)
                    activate ScanListener
                    ScanListener --> scanExecutor:
                    deactivate ScanListener

                    scanExecutor -> scanExecutor: currentIndex.incrementAndGet()
                end
            else 直接处理错误
                scanExecutor -> scanExecutor: processBatchError(batch)
                loop 创建失败结果
                    scanExecutor -> ScanListener: scanFileFinish(index, path, failResult)
                    activate ScanListener
                    ScanListener --> scanExecutor:
                    deactivate ScanListener
                end
            end
        end

        scanExecutor -> scanExecutor: batch.clear()
    end

    scanExecutor -> scanExecutor: checkAllTasksFinished()
    alt 所有任务完成
        scanExecutor -> scanExecutor: notifyScanFinish()
        note right #E8F5E8: ✅ 修复点3:\n正常完成时的资源清理
        scanExecutor -> ScanListener: scanFinish()
        activate ScanListener
        ScanListener --> scanExecutor:
        deactivate ScanListener
        scanExecutor -> scanExecutor: shutdownExecutors()
        note right #E8F5E8: ✅ 新增:\n优雅关闭线程池\n确保资源正确释放
        break
    end
end

== 资源清理阶段 ==

scanExecutor -> scanExecutor: finally块执行
alt isStopped = true
    scanExecutor -> ScanListener: scanStop()
    activate ScanListener
    ScanListener --> scanExecutor:
    deactivate ScanListener
end

note over scanExecutor #E8F5E8
✅ 修复点4: 移除自我关闭逻辑
原来的问题代码已移除:
- hashExecutor.shutdownNow()
- scanExecutor.shutdownNow()

现在由以下方法统一管理:
- notifyScanFinish() → shutdownExecutors()
- stopScan() → forceShutdownExecutors()
end note

deactivate scanExecutor

== 手动停止场景的资源清理 ==

alt 用户调用stopScan()
    Client -> CloudScanner: stopScan()
    activate CloudScanner
    CloudScanner -> CloudScanner: super.stopScan()
    CloudScanner -> CloudScanner: forceShutdownExecutors()
    note right #E8F5E8: ✅ 修复点5:\n手动停止时强制关闭线程池\n使用shutdownNow()立即停止
    CloudScanner -> hashExecutor: shutdownNow()
    CloudScanner -> scanExecutor: shutdownNow()
    deactivate CloudScanner
end

deactivate CloudScanner
deactivate AVLEngine

== 修复效果总结 ==

note over Client, FileScanner
🎯 **修复前后对比**:

❌ **修复前的问题**:
1. 线程池自我关闭 → RejectedExecutionException
2. 正常完成时资源泄露 → 内存泄露
3. 重复哈希计算 → 性能浪费
4. 缺少状态检查 → 不稳定

✅ **修复后的改进**:
1. 统一资源管理 → shutdownExecutors() / forceShutdownExecutors()
2. 生命周期清晰 → 正常完成和手动停止都正确清理
3. 性能优化 → 预计算哈希值避免重复计算
4. 安全检查 → 任务提交前检查线程池状态
5. 优雅关闭 → shutdown() vs shutdownNow()

🔧 **核心修复点**:
- 移除processScan()的finally块中的线程池关闭
- 在notifyScanFinish()中添加shutdownExecutors()
- 在stopScan()中使用forceShutdownExecutors()
- 添加线程池状态检查和预计算哈希支持

📈 **改进效果**:
- 消除竞态条件，提高稳定性
- 防止资源泄露，优化内存使用
- 减少重复计算，提升性能
- 增强错误处理，提高健壮性
end note

@enduml