package com.antiy.avlsdk.update;

/**
 * Author: wang<PERSON><PERSON>
 * Date: 2024/10/11 17:09
 * Description:
 */
public class UpdateStrategyFactory {
    public static UpdateStrategy createStrategy(UpdateTypeEnum type, PlatformType platform) {
        switch (platform) {
            case ANDROID:
                return type == UpdateTypeEnum.FULL ? new AndroidFullUpdateStrategy() : new AndroidIncrementUpdateStrategy();
            case PC:
                return type == UpdateTypeEnum.FULL ? new PCFullUpdateStrategy() : new PCIncrementUpdateStrategy();
            default:
                throw new IllegalArgumentException("Unsupported platform type");
        }
    }
}
