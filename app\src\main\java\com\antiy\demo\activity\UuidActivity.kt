package com.antiy.demo.activity

import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import com.antiy.avlsdk.AVLCoreEngine
import com.antiy.avlsdk.AVLEngine
import com.antiy.avlsdk.callback.RequestCallback
import com.antiy.avlsdk.entity.AuthResponseEntity
import com.antiy.avlsdk.entity.RequestMethod
import com.antiy.avlsdk.storage.EncryptorHelper
import com.antiy.avlsdk.utils.DateUtils
import com.antiy.avlsdk.utils.JsonUtils
import com.antiy.avlsdk.utils.SdkConst
import com.antiy.demo.LogAdapter
import com.antiy.demo.databinding.ActivityUuidBinding
import org.json.JSONException
import org.json.JSONObject
import java.util.Locale
import java.util.Random
import kotlin.concurrent.thread

class UuidActivity : AppCompatActivity() {

    private lateinit var binding: ActivityUuidBinding
    private val handler by lazy {
        Handler(Looper.getMainLooper())
    }
    private val mAdapter by lazy {
        LogAdapter(arrayListOf())
    }
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityUuidBinding.inflate(layoutInflater)
        setContentView(binding.root)

        initView()

    }

    private fun initView() {
        binding.btnSend.setOnClickListener {
            thread { requestAuthToken() }
        }
        binding.rv.layoutManager = LinearLayoutManager(this)
        binding.rv.adapter = mAdapter
    }


    private fun requestAuthToken() {
        try {
            val flag: Int = generateAuthFlag()
            val uuid = /*UUID.randomUUID().toString()*/SdkConst.DEFAULT_UUID
            AVLEngine.Logger.error("uuid：$uuid")
            handler.post {
                mAdapter.addData("未加密的uuid：" + uuid)
            }

            val clientId = "zhanch"
            val requestTime: String = requestTime()
            val content: String = generateAuthRequest(uuid, clientId, requestTime, flag)
            val jsonObject = JSONObject()
            jsonObject.put("flag", flag.toString() + "")
            jsonObject.put("uuid", EncryptorHelper.md5(uuid).uppercase(Locale.getDefault()))
            jsonObject.put("requestTime", requestTime)
            jsonObject.put("content", content)
            handler.post {
                mAdapter.addData("请求参数：$jsonObject")
            }

            AVLEngine.getInstance().networkManager.request(SdkConst.AUTH_URL,
                RequestMethod.POST,
                jsonObject,
                object : RequestCallback {
                    override fun onError(msg: String) {
                        AVLEngine.Logger.error("auth request fail")
                    }

                    override fun onFinish(code: Int, responseBody: Any) {
                        handler.post {
                            mAdapter.addData("返回结果:$responseBody")
                            binding.rv.scrollToPosition(mAdapter.dataSet.size - 1)

                            val `object` = JSONObject(responseBody.toString())
                            val resultCode = `object`.getInt("code")
                            if (resultCode == 0) {
                                val data = `object`.getJSONObject("data")
                                val responseEntity = JsonUtils.toObject(
                                    data.toString(),
                                    AuthResponseEntity::class.java
                                )
                                val authToken = AVLCoreEngine.getInstance().decryptAuthToken(
                                    responseEntity.content,
                                    responseEntity.responseTime, responseEntity.flag
                                )
                                AVLEngine.Logger.info("decrypt auth token:$authToken")
                            }
                        }
                    }
                }
            )
        } catch (e: JSONException) {
            e.printStackTrace()
        }
    }

    private fun generateAuthFlag(): Int {
        val random = Random()
        val number = random.nextInt(32)
        return number
    }
    private fun requestTime(): String {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val time = DateUtils.getCurrentDate()
            return time
        }
        return ""
    }

    private fun generateAuthRequest(
        uuid: String,
        clientId: String,
        requestTime: String,
        flag: Int
    ): String {
        AVLCoreEngine.getInstance().setupAuthParams(uuid, clientId)
        val content = AVLCoreEngine.getInstance().generateAuthRequest(requestTime, flag)
        return content
    }
}