<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Base.Theme.Demo" parent="Theme.Material3.DayNight.NoActionBar">
        <!-- Customize your light theme here. -->
        <!-- <item name="colorPrimary">@color/my_light_primary</item> -->
    </style>

    <style name="Theme.Demo" parent="Base.Theme.Demo" />

    <style name="Theme.AntiVirus" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryVariant">@color/primary_variant</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/primary</item>
        <item name="colorSecondaryVariant">@color/primary_variant</item>
        <item name="colorOnSecondary">@color/white</item>
        
        <!-- Status bar and Navigation bar -->
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">true</item>

        <!-- Bottom Navigation customization -->
        <item name="bottomNavigationStyle">@style/Widget.App.BottomNavigationView</item>
    </style>

    <!-- 自定义底部导航栏样式 -->
    <style name="Widget.App.BottomNavigationView" parent="Widget.MaterialComponents.BottomNavigationView">
        <item name="itemIconTint">@color/bottom_nav_colors</item>
        <item name="itemTextColor">@color/bottom_nav_colors</item>
    </style>

    <style name="Theme.About" parent="Theme.AppCompat.Light.DarkActionBar">
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryDark">@color/primary_variant</item>
        <item name="android:statusBarColor">@color/primary_variant</item>
    </style>
</resources>