package com.antiy.avlsdk.cloud;

import android.util.Log;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.FileInputStream;
import java.security.KeyManagementException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.util.ArrayList;
import java.util.List;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;

/**
 * VirtualApp Native Project
 */
public class AVLEngineCloud {

    private static final String TAG = AVLEngineCloud.class.getSimpleName();
    private static boolean sFlag = false;

    private static AVLEngineCloud mInstance;
    private static String mAVLPath;

    public String getmAVLPath() {
        return mAVLPath;
    }

    static {
        try {
            System.loadLibrary("avlsdk_cloud");
        } catch (Throwable e) {
            Log.d(TAG, e.getMessage());
        }
    }

    private AVLEngineCloud() {
    }

    public static AVLEngineCloud getInstance() {
        if (mInstance == null) {
            synchronized (AVLEngineCloud.class) {
                if (mInstance == null) {
                    mInstance = new AVLEngineCloud();
                }
            }
        }

        return mInstance;
    }

    public static String getMD5Checksum(String filePath) {
        try {
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            try (FileInputStream fis = new FileInputStream(filePath)) {
                byte[] dataBytes = new byte[1024];
                int nread;
                while ((nread = fis.read(dataBytes)) != -1) {
                    md5.update(dataBytes, 0, nread);
                }
                byte[] mdbytes = md5.digest();
                StringBuilder hexString = new StringBuilder();
                for (byte mdbyte : mdbytes) {
                    hexString.append(Integer.toString((mdbyte & 0xff) + 0x100, 16).substring(1));
                }
                return hexString.toString();
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static String getMD5(String input) {
        try {
            // Create MD5 Hash
            MessageDigest digest = MessageDigest.getInstance("MD5");
            digest.update(input.getBytes());
            byte messageDigest[] = digest.digest();

            // Create Hex String
            StringBuilder hexString = new StringBuilder();
            for (byte aMessageDigest : messageDigest) {
                String h = Integer.toHexString(0xFF & aMessageDigest);
                while (h.length() < 2) h = "0" + h;
                hexString.append(h);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return "";
    }

    public static native String getApkInfo(String filePath);

    public static SSLContext createIgnoreVerifySSL() throws NoSuchAlgorithmException, KeyManagementException {
        SSLContext sc = SSLContext.getInstance("TLS");
        // 实现一个X509TrustManager接口，用于绕过验证，不用修改里面的方法
        X509TrustManager trustManager = new X509TrustManager() {
            @Override
            public void checkClientTrusted(
                    java.security.cert.X509Certificate[] paramArrayOfX509Certificate,
                    String paramString) throws CertificateException {
            }

            @Override
            public void checkServerTrusted(
                    java.security.cert.X509Certificate[] paramArrayOfX509Certificate,
                    String paramString) throws CertificateException {
            }

            @Override
            public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                return null;
            }
        };

        sc.init(null, new TrustManager[]{trustManager}, null);
        return sc;
    }

    public List<String> getScanCloudResult(String response, int[] indexs) {
        List<String> indexRet = new ArrayList<>();
        try {
            JSONObject jsonObject = new JSONObject(response);
            JSONObject dataObject = (JSONObject) jsonObject.get("data");
            JSONObject result = dataObject.getJSONObject("result").getJSONObject("cert_hash");

            for (int i : indexs) {
                if (result.has("" + i)) {
                    indexRet.add(i, result.getString("" + i));
                } else {
                    indexRet.add(i, "未查到相关应用结果");
                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }

        return indexRet;
    }
}

