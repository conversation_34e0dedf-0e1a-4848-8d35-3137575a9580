package com.antiy.avlsdk.utils;

import android.annotation.SuppressLint;
import android.app.ActivityManager;
import android.content.Context;

import com.antiy.avlsdk.AVLEngine;

import java.io.BufferedReader;
import java.io.FileReader;
import java.util.ArrayList;
import java.util.List;

public class PerformanceMonitor {
    private long startTime;
    private long endTime;
    private List<Long> memoryUsages = new ArrayList<>();
    private List<Double> cpuUsages = new ArrayList<>();

    public void start() {
        startTime = System.currentTimeMillis();
        memoryUsages.clear();
        cpuUsages.clear();
    }

    public void stop() {
        endTime = System.currentTimeMillis();
    }

    public void updateMetrics() {
        memoryUsages.add(getMemoryUsage());
        cpuUsages.add(getCpuUsage());
    }

    private long getMemoryUsage() {
        ActivityManager.MemoryInfo memoryInfo = new ActivityManager.MemoryInfo();
        ActivityManager activityManager = (ActivityManager) AVLEngine.getInstance().getContext().getSystemService(Context.ACTIVITY_SERVICE);
        activityManager.getMemoryInfo(memoryInfo);

        Runtime runtime = Runtime.getRuntime();
        long usedMemory = runtime.totalMemory() - runtime.freeMemory();

        return usedMemory;
    }

    private double getCpuUsage() {
        try {
            String[] cpuInfos = null;
            BufferedReader reader = new BufferedReader(new FileReader("/proc/stat"));
            String load = reader.readLine();
            reader.close();
            cpuInfos = load.split(" ");
            long idleCpuTime1 = Long.parseLong(cpuInfos[5]);
            long totalCpuTime1 = 0;
            for (int i = 2; i < cpuInfos.length; i++) {
                totalCpuTime1 += Long.parseLong(cpuInfos[i]);
            }

            Thread.sleep(360);

            reader = new BufferedReader(new FileReader("/proc/stat"));
            load = reader.readLine();
            reader.close();
            cpuInfos = load.split(" ");
            long idleCpuTime2 = Long.parseLong(cpuInfos[5]);
            long totalCpuTime2 = 0;
            for (int i = 2; i < cpuInfos.length; i++) {
                totalCpuTime2 += Long.parseLong(cpuInfos[i]);
            }

            long idleCpuTime = idleCpuTime2 - idleCpuTime1;
            long totalCpuTime = totalCpuTime2 - totalCpuTime1;

            return 100.0 * (1.0 - (idleCpuTime * 1.0 / totalCpuTime));
        } catch (Exception e) {
            AVLEngine.Logger.error("Error getting CPU usage: " + e.getMessage());
            return 0;
        }
    }

    @SuppressLint("DefaultLocale")
    public String getReport() {
        long duration = endTime - startTime;
        long peakMemory = memoryUsages.stream().mapToLong(Long::longValue).max().orElse(0);
        double peakMemoryMB = peakMemory / (1024.0 * 1024.0);
        double avgCpu = cpuUsages.stream().mapToDouble(Double::doubleValue).average().orElse(0);

        return String.format("扫描时间: %d ms\n平均 CPU 使用率: %.2f%%\n峰值内存使用: %.2f MB",
                duration, avgCpu, peakMemoryMB);
    }
}
