@startuml
title AVL SDK 配置管理流程

actor 应用层 as App
participant AVLEng<PERSON> as Engine
participant "Config.Builder" as Builder
participant Config
participant ConfigUpdater
participant DataManager
participant DatabaseHelper
participant SharedPreferences

== 配置创建与加载 ==

App -> Builder: new Config.Builder()
activate Builder

App -> Builder: setBlackWhiteList(map)
App -> Builder: setScanType(types)
App -> Builder: setCloudCheckThreshold(500)
App -> Builder: setCloudSizeThreshold(100MB)
App -> Builder: setSilentPerformanceThreshold(100)
App -> Builder: setHistoryTimeout(7天)
App -> Builder: setHistorySize(1000)

Builder -> Config: build()
activate Config
Config --> Builder: config对象
deactivate Config
Builder --> App: config对象
deactivate Builder

App -> Engine: loadConfig(config)
activate Engine
Engine -> Config: create()
activate Config

Config -> DataManager: saveBlackWhiteList(blackWhiteMap)
activate DataManager
DataManager -> DatabaseHelper: insertBlackWhiteData()
DatabaseHelper --> DataManager
DataManager --> Config
deactivate DataManager

Config -> DataManager: saveScanType(scanTypeStrings)
activate DataManager
DataManager -> SharedPreferences: putString()
SharedPreferences --> DataManager
DataManager --> Config
deactivate DataManager

Config -> DataManager: saveCloudCheckThreshold(threshold)
Config -> DataManager: saveCloudSizeThreshold(threshold)
Config -> DataManager: saveSilentPerformanceThreshold(threshold)
Config -> DataManager: saveHistoryTimeout(timeout)
Config -> DataManager: saveHistorySize(size)

Config --> Engine
deactivate Config

Engine -> Engine: this.config = config
Engine --> App
deactivate Engine

== 配置更新 ==

App -> Engine: updateConfig(updater)
activate Engine

Engine -> Engine: 检查config是否已初始化

Engine -> ConfigUpdater: update(config)
activate ConfigUpdater
note right: 更新特定配置项\n例如更新云查询阈值

ConfigUpdater -> Config: updateCloudThreshold(800)
activate Config
Config -> Config: this.cloudThreshold = 800
Config -> DataManager: saveCloudCheckThreshold(800)
activate DataManager
DataManager -> SharedPreferences: putInt()
SharedPreferences --> DataManager
DataManager --> Config
deactivate DataManager
Config --> ConfigUpdater
deactivate Config

ConfigUpdater --> Engine
deactivate ConfigUpdater

Engine --> App
deactivate Engine

== 配置使用 ==

App -> Engine: scanDir(path, listener)
activate Engine
Engine -> DataManager: getCloudCheckThreshold()
activate DataManager
DataManager -> SharedPreferences: getInt(CLOUDSCAN_COUNT)
SharedPreferences --> DataManager: 800
DataManager --> Engine: 800
deactivate DataManager

note right of Engine: 根据配置决定使用\n云扫描还是本地扫描

Engine --> App
deactivate Engine

@enduml