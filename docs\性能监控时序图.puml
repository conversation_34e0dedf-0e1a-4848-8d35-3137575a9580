@startuml

title 杀毒引擎性能监控流程

actor 用户
participant "MainActivity.kt" as MainActivity
participant "AVLEngine" as AVLEngine
participant "C层主进程" as CMainProcess
participant "监控线程" as MonitorThread
participant "日志线程" as LoggingThread
participant "操作系统" as OS

== 初始化阶段 ==

用户 -> MainActivity: 点击"静默扫描"按钮
activate MainActivity

MainActivity -> AVLEngine: scanDirSilent(path, listener)
activate AVLEngine

AVLEngine -> CMainProcess: 启动CPU监控进程
activate CMainProcess

CMainProcess -> CMainProcess: 解析命令行参数\n(pid, percent, interval等)
CMainProcess -> CMainProcess: 创建命名管道(FIFO)
CMainProcess -> CMainProcess: 注册SIGUSR1信号处理函数

CMainProcess -> LoggingThread: 创建日志线程
activate LoggingThread
LoggingThread -> LoggingThread: 初始化日志文件
LoggingThread -> LoggingThread: 开始记录CPU使用率

CMainProcess -> MonitorThread: 创建监控线程
activate MonitorThread
MonitorThread -> MonitorThread: 初始化PID统计数据
MonitorThread -> MonitorThread: 初始化系统统计数据

CMainProcess -> CMainProcess: 阻塞等待管道命令

== 监控过程 ==

loop 每interval毫秒
    MonitorThread -> OS: 读取/proc/{pid}/stat
    OS --> MonitorThread: 返回进程状态数据
    
    MonitorThread -> OS: 读取/proc/stat
    OS --> MonitorThread: 返回系统CPU数据
    
    MonitorThread -> MonitorThread: 计算CPU使用率百分比
    
    alt CPU使用率 > 阈值(percent)
        MonitorThread -> OS: 发送SIGSTOP信号暂停进程
    else CPU使用率 <= 阈值
        MonitorThread -> OS: 发送SIGCONT信号继续进程
        MonitorThread -> MonitorThread: 更新历史数据
    end
end

== 控制过程 ==

用户 -> MainActivity: 点击"CPU暂停"按钮
activate MainActivity

MainActivity -> MainActivity: sendPipeMessage(PipeMessage.PAUSE)
MainActivity -> AVLEngine: 写入"PAUSE\n"到管道
AVLEngine --> CMainProcess: 管道数据"PAUSE\n"

CMainProcess -> CMainProcess: 设置g_disable_flag = true
CMainProcess -> OS: kill(pid, SIGCONT)
OS -> MonitorThread: 监控线程检测到g_disable_flag为true
MonitorThread -> OS: 始终发送SIGCONT信号

用户 -> MainActivity: 点击"CPU继续"按钮
MainActivity -> MainActivity: sendPipeMessage(PipeMessage.CONTINUE)
MainActivity -> AVLEngine: 写入"CONTINUE\n"到管道
AVLEngine --> CMainProcess: 管道数据"CONTINUE\n"

CMainProcess -> CMainProcess: 设置g_disable_flag = false
OS -> MonitorThread: 监控线程检测到g_disable_flag为false
MonitorThread -> MonitorThread: 恢复正常监控逻辑

== 结束阶段 ==

AVLEngine -> OS: 扫描完成或出错
OS -> CMainProcess: 收到SIGUSR1信号

CMainProcess -> OS: kill(pid, SIGCONT)
CMainProcess -> CMainProcess: 设置should_exit = true

CMainProcess -> LoggingThread: 等待日志线程结束
deactivate LoggingThread

CMainProcess -> MonitorThread: 等待监控线程结束
deactivate MonitorThread

CMainProcess -> CMainProcess: 关闭管道
CMainProcess -> CMainProcess: 删除管道文件
CMainProcess -> CMainProcess: 安全退出
deactivate CMainProcess

AVLEngine --> MainActivity: 扫描完成/出错回调
deactivate AVLEngine
deactivate MainActivity

@enduml