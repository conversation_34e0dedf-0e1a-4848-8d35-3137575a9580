apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'


android {
    namespace 'com.antiy.avlsdk.cloud'
    compileSdk 31

    defaultConfig {
        minSdk 21

        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

}

dependencies {
}