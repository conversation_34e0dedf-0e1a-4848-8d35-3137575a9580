# 车机SDK License刷新技术方案

## 背景

当前SDK在初始化时会立即刷新license，但在车机平台上存在一个问题：车机启动时会同时开启网络服务和SDK服务，但网络服务启动较慢，导致SDK初始化时调用刷新license接口失败。此外，用户的车辆可能在地下车库等无信号区域启动，导致license刷新机制失效，而此时SDK已初始化完成，只能等下次手动启动SDK的应用才会进行license刷新。考虑到license有效期较长，不必在每次SDK初始化时立即刷新，可以将刷新时机调整到更合适的场景。

## 问题分析

1. 当前实现在SDK初始化时立即刷新license：
```java
// 每次请求完后进行更新token
new Thread(() -> instance.requestAuthToken(uuid)).start();
```

2. 车机启动时网络服务尚未就绪，导致刷新失败
3. 在地下车库等无信号区域启动车辆时，license刷新失败，需要等到有网络的环境才能刷新
4. license有较长的有效期，不需要每次初始化都刷新
5. 当前缺少合适的刷新时机，可以考虑在用户主动使用文件扫描功能时进行刷新

## 解决方案：在扫描时刷新 + 检查上次刷新时间

### 方案概述

1. 不在SDK初始化时立即刷新license，而是将刷新时机调整到用户主动调用扫描功能时
2. 在`scanDir`方法中集成license刷新功能，同时增加有网络的判断才进行刷新license
3. 记录上次刷新成功的时间，只有当距离上次刷新超过指定时间才发起刷新
4. 使用原子操作标记当前是否正在刷新license，避免短时间内重复调用
5. 延迟发起刷新请求（如30秒），等待网络服务初始化完成
6. 添加刷新失败的重试机制（最多重试3次）

### 技术实现

#### 1. 在AVLEngine中添加相关字段

在`AVLEngine`类中添加license刷新相关的字段和常量：

```java
// 上次刷新时间的存储键
private static final String LAST_REFRESH_TIME_KEY = "last_license_refresh_time";
// 默认刷新间隔：7天
private static final long DEFAULT_REFRESH_INTERVAL = 7 * 24 * 60 * 60 * 1000;
// 默认刷新延迟：30秒
private static final long DEFAULT_REFRESH_DELAY = 30 * 1000;
// 默认刷新重试间隔：60秒
private static final long DEFAULT_RETRY_INTERVAL = 60 * 1000;
// 默认最大重试次数
private static final int DEFAULT_MAX_RETRIES = 3;
// 原子标记，表示当前是否正在刷新license
private static final AtomicBoolean isRefreshingLicense = new AtomicBoolean(false);
```

#### 2. 修改初始化方法

修改`initMobileEngine`方法，移除立即刷新license的代码：

```java
public static int initMobileEngine(String uuid) {
    // 获取授权令牌
    String authToken = AuthInfoManager.getInstance(getInstance().getContext())
            .getAuthInfo().optString("token", "");

    // 初始化移动引擎
    int mobileInitResult = AVLCoreEngine.getInstance().init(
            AVLCoreEngine.getInstance().getLibPath(),
            AVLCoreEngine.getInstance().getDbPath(),
            authToken
    );

    InitValue result = InitValue.fromCode(mobileInitResult);
    AVLEngine.Logger.error("mobile:" + result.getCode() + ":" + result.getDescription() +
            ",version:" + AVLCoreEngine.getInstance().getSigLibVersion());
    
    // 移除立即刷新license的代码
    // new Thread(() -> instance.requestAuthToken(uuid)).start();

    return mobileInitResult;
}
```

#### 3. 在scanDir方法中集成license刷新

修改`scanDir`方法，在扫描开始时检查并刷新license：

```java
public void scanDir(String path, ScanListener listener) {
    Logger.info("start scanDir");
    Logger.info("scanDir sdk version:" + getVersion());
    if (!validateScanRequest(path, listener)) {
        return;
    }

    // 使用 compareAndSet 确保原子性操作
    if (!isScanning.compareAndSet(false, true)) {
        Logger.error("Scanning is in progress, please wait for the task to complete");
        notifyScanError(listener, "Scan already in progress");
        return;
    }
    
    // 在扫描开始时检查并刷新license
    checkAndRefreshLicense();

    try {
        if (!initializeScan(path, listener)) {
            return;
        }
        
        // ... 现有的扫描代码 ...
    } catch (Exception e) {
        // ... 现有的异常处理代码 ...
    }
}

/**
 * 检查并刷新license
 */
private void checkAndRefreshLicense() {
    // 获取当前设备的UUID
    String uuid = AuthInfoManager.getInstance(getInstance().getContext())
            .getAuthInfo().optString("uuid", "");
    
    // 如果UUID为空，无法刷新license
    if (TextUtils.isEmpty(uuid)) {
        Logger.error("UUID is empty, cannot refresh license");
        return;
    }
    
    // 使用原子操作确保同一时间只有一个刷新请求
    if (!isRefreshingLicense.compareAndSet(false, true)) {
        Logger.info("License is already being refreshed, skipping");
        return;
    }
    
    // 创建单独的线程进行延迟刷新
    Thread refreshThread = new Thread(() -> {
        try {
            // 检查是否需要刷新license
            if (shouldRefreshLicenseByTime()) {
                // 延迟30秒，等待网络服务初始化
                Logger.info("将在" + (DEFAULT_REFRESH_DELAY / 1000) + "秒后尝试刷新License");
                Thread.sleep(DEFAULT_REFRESH_DELAY);
                
                // 尝试刷新license，最多重试3次
                refreshLicenseWithRetry(uuid, DEFAULT_MAX_RETRIES, DEFAULT_RETRY_INTERVAL);
            } else {
                Logger.info("距离上次刷新时间不足，无需刷新License");
            }
        } catch (InterruptedException e) {
            Logger.error("License刷新线程被中断: " + e.getMessage());
        } catch (Exception e) {
            Logger.error("License刷新失败: " + e.getMessage());
        } finally {
            // 无论刷新成功与否，都重置刷新标记
            isRefreshingLicense.set(false);
        }
    });
    
    refreshThread.setName("LicenseRefreshThread");
    refreshThread.setDaemon(true); // 设为守护线程，不阻止JVM退出
    refreshThread.start();
}
```

#### 4. 检查是否需要根据时间刷新

```java
/**
 * 根据上次刷新时间检查是否需要刷新license
 * 
 * @return 如果需要刷新返回true，否则返回false
 */
private static boolean shouldRefreshLicenseByTime() {
    try {
        // 获取上次刷新时间
        long lastRefreshTime = getLastRefreshTime();
        long currentTime = System.currentTimeMillis();
        
        // 如果从未刷新过，或者距离上次刷新已超过指定间隔，则需要刷新
        boolean needRefresh = (lastRefreshTime == 0) || 
                             (currentTime - lastRefreshTime > DEFAULT_REFRESH_INTERVAL);
        
        AVLEngine.Logger.info("License时间检查: 上次刷新时间=" + new Date(lastRefreshTime) + 
                              ", 当前时间=" + new Date(currentTime) + 
                              ", 是否需要刷新=" + needRefresh);
        
        return needRefresh;
    } catch (Exception e) {
        AVLEngine.Logger.error("检查License刷新时间失败: " + e.getMessage());
        // 如果检查失败，默认需要刷新
        return true;
    }
}

/**
 * 获取上次license刷新时间
 * 
 * @return 上次刷新的时间戳，如果从未刷新过则返回0
 */
private static long getLastRefreshTime() {
    try {
        SharedPreferences prefs = getInstance().getContext().getSharedPreferences(
                "avl_sdk_prefs", Context.MODE_PRIVATE);
        return prefs.getLong(LAST_REFRESH_TIME_KEY, 0);
    } catch (Exception e) {
        AVLEngine.Logger.error("获取上次License刷新时间失败: " + e.getMessage());
        return 0;
    }
}

/**
 * 保存license刷新时间
 */
private static void saveRefreshTime() {
    try {
        SharedPreferences prefs = getInstance().getContext().getSharedPreferences(
                "avl_sdk_prefs", Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();
        editor.putLong(LAST_REFRESH_TIME_KEY, System.currentTimeMillis());
        editor.apply();
        AVLEngine.Logger.info("已保存License刷新时间: " + new Date(System.currentTimeMillis()));
    } catch (Exception e) {
        AVLEngine.Logger.error("保存License刷新时间失败: " + e.getMessage());
    }
}
```

#### 5. 带重试机制的刷新实现

```java
/**
 * 带重试机制的license刷新
 * 
 * @param uuid 设备唯一标识
 * @param maxRetries 最大重试次数
 * @param retryInterval 重试间隔（毫秒）
 */
private static void refreshLicenseWithRetry(String uuid, int maxRetries, long retryInterval) {
    int retryCount = 0;
    boolean refreshSuccess = false;
    
    do {
        try {
            // 检查网络是否可用
            if (isNetworkAvailable()) {
                AVLEngine.Logger.info("尝试刷新License，当前尝试次数: " + (retryCount + 1));
                
                // 调用刷新方法
                instance.requestAuthToken(uuid);
                
                // 等待一段时间，检查刷新是否成功
                Thread.sleep(5000); // 等待5秒
                
                // 验证刷新是否成功
                if (verifyLicenseRefresh()) {
                    AVLEngine.Logger.info("License刷新成功");
                    refreshSuccess = true;
                    
                    // 保存刷新时间
                    saveRefreshTime();
                    break;
                } else {
                    AVLEngine.Logger.error("License刷新验证失败");
                }
            } else {
                AVLEngine.Logger.error("网络不可用，无法刷新License");
            }
            
            // 增加重试计数
            retryCount++;
            if (retryCount < maxRetries) {
                AVLEngine.Logger.info("将在" + (retryInterval / 1000) + "秒后进行第" + (retryCount + 1) + "次重试");
                Thread.sleep(retryInterval);
            }
        } catch (InterruptedException e) {
            AVLEngine.Logger.error("License刷新线程被中断: " + e.getMessage());
            break;
        } catch (Exception e) {
            AVLEngine.Logger.error("License刷新失败: " + e.getMessage());
            retryCount++;
        }
    } while (retryCount < maxRetries && !refreshSuccess);
    
    if (!refreshSuccess) {
        AVLEngine.Logger.error("License刷新失败，已达到最大重试次数: " + maxRetries);
    }
}

/**
 * 检查网络是否可用
 * 
 * @return 如果网络可用返回true，否则返回false
 */
private static boolean isNetworkAvailable() {
    try {
        ConnectivityManager cm = (ConnectivityManager) getInstance().getContext()
                .getSystemService(Context.CONNECTIVITY_SERVICE);
        NetworkInfo activeNetwork = cm.getActiveNetworkInfo();
        return activeNetwork != null && activeNetwork.isConnectedOrConnecting();
    } catch (Exception e) {
        AVLEngine.Logger.error("检查网络状态失败: " + e.getMessage());
        return false;
    }
}

/**
 * 验证license刷新是否成功
 * 
 * @return 如果刷新成功返回true，否则返回false
 */
private static boolean verifyLicenseRefresh() {
    try {
        // 获取最新的授权信息
        JSONObject authInfo = AuthInfoManager.getInstance(getInstance().getContext()).getAuthInfo();
        
        // 验证token是否有效
        if (authInfo != null && authInfo.has("token")) {
            String token = authInfo.getString("token");
            
            // 验证token不为空
            return !TextUtils.isEmpty(token);
        }
    } catch (Exception e) {
        AVLEngine.Logger.error("验证License刷新失败: " + e.getMessage());
    }
    
    return false;
}
```

#### 6. 修改requestAuthToken方法

```java
/**
 * 请求认证令牌
 * 本方法通过LicenseManager请求新的认证令牌，并从SharedPreferences中获取最新的认证令牌
 * 认证令牌用于验证和授权，确保应用的合法使用
 */
private void requestAuthToken(String uuid) {
    // 检查网络是否可用
    if (!isNetworkAvailable()) {
        AVLEngine.Logger.error("网络不可用，跳过License刷新");
        return;
    }
    
    try {
        // 创建LicenseManager实例，用于管理许可证和认证令牌
        LicenseManager licenseManager = new LicenseManager(getInstance().getContext());
        // 请求新的认证令牌
        licenseManager.requestAuthToken(uuid);
    } catch (Exception e) {
        AVLEngine.Logger.error("请求认证令牌失败: " + e.getMessage());
        throw e; // 重新抛出异常，让调用者处理
    }
}
```

## 方案优势

1. **更合理的刷新时机**：将license刷新从初始化时调整到用户主动扫描时，此时网络通常已就绪并可用
2. **解决地下车库问题**：即使在地下车库等无信号区域启动车辆，也可以在用户离开无信号区域后使用扫描功能时自动刷新license
3. **避免重复调用**：使用原子操作标记当前是否正在刷新license，避免短时间内多次扫描导致的重复刷新
4. **资源节约**：根据上次刷新时间判断是否需要刷新，避免频繁刷新
5. **可靠性提升**：添加最多3次的重试机制，提高license刷新的成功率
6. **灵活配置**：可以根据实际需求调整刷新间隔、延迟时间、重试次数等参数



## 风险评估

1. **扫描依赖**：将license刷新与扫描功能绑定，如果用户长时间不使用扫描功能，可能导致license过期
2. **扫描性能**：在扫描时增加license刷新逻辑可能会略微影响扫描的性能，但由于刷新是在单独线程中异步进行，影响很小
3. **时间设置**：如果设备时间被修改，可能导致刷新逻辑异常

## 缓解措施

1. 对于扫描依赖问题，可以考虑在应用前台运行时添加定期检查机制，当license接近过期时自动刷新
2. 使用低优先级线程进行license刷新，确保不影响扫描主流程
3. 可以考虑使用其他机制（如启动次数）作为辅助判断条件，减少对系统时间的依赖

## 业务流程图和时序图

### 业务流程图

```mermaid
flowchart TD
    A[用户调用扫描功能] --> B[检查是否需要刷新license]
    B --> C[判断距离上次刷新时间是否超过阈值]
    C -->|Yes| D[延迟30秒后发起license刷新请求]
    C -->|No| G[继续执行扫描操作]
    D --> E[验证license刷新是否成功]
    E -->|Yes| F[保存刷新成功时间]
    E -->|No| H[重试机制（最多3次）]
    H --> D
    F --> G
```

### 时序图

```mermaid
sequenceDiagram
    participant 用户
    participant AVLEngine
    participant ScanListener
    participant LicenseManager
    participant 服务器
    
    用户->>AVLEngine: 调用scanDir()
    AVLEngine->>AVLEngine: 检查是否需要刷新license
    AVLEngine->>AVLEngine: 判断距离上次刷新时间
    
    Note over AVLEngine,ScanListener: 如果需要刷新license
    
    AVLEngine->>ScanListener: 开始扫描操作
    AVLEngine->>+LicenseManager: 异步刷新license
    LicenseManager->>+服务器: 请求刷新license
    服务器-->>-LicenseManager: 返回license响应
    
    Note over LicenseManager: 如果失败，重试最多3次
    
    LicenseManager-->>-AVLEngine: 完成license刷新
    AVLEngine->>AVLEngine: 保存刷新成功时间
    AVLEngine-->>ScanListener: 继续扫描操作
    AVLEngine-->>用户: 返回扫描结果
```

## 结论

将license刷新时机调整到扫描时，并结合上次刷新时间检查的方案可以有效解决车机SDK初始化时license刷新失败的问题。该方案特别适合解决地下车库等无信号区域启动车辆时的问题，因为用户使用扫描功能时通常已经离开无信号区域。通过原子操作标记和重试机制，该方案提高了license刷新的成功率，同时避免了短时间内多次扫描导致的重复刷新问题。该方案实现简单，优化了资源使用，提高了系统稳定性，适合在车机环境中使用。