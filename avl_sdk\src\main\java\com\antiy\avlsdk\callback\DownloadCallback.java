package com.antiy.avlsdk.callback;

/**
 * Copyright (C), 2020-2024
 * FileName: DownloadCallback
 * Author: wang<PERSON>o
 * Date: 2024/9/2 10:18
 * Description: 文件下载回调接口，用于处理下载过程中的错误和完成事件
 */
public interface DownloadCallback {
    /**
     * 下载过程发生错误时调用此方法
     * @param msg 错误信息描述，包含具体的错误原因
     */
    void onError(String msg);

    /**
     * 下载完成时调用此方法，返回下载结果信息
     *
     * @param code 响应状态码，表示下载结果的状态，如200表示成功
     * @param responseFileName 服务器响应的文件名，即下载文件的原始名称
     * @param localFilePath 下载文件保存到本地的完整路径，可用于后续文件访问
     */
    void onFinish(
            int code,
            String responseFileName,
            String localFilePath
    );
}
