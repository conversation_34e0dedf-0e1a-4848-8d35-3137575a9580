package com.antiy.avlsdk.entity;

/**
 * 2 * Copyright (C), 2020-2024
 * 3 * FileName: CacheEntity
 * 4 * Author: wangbiao
 * 5 * Date: 2024/9/4 15:22
 * 6 * Description: 缓存实体
 * 10
 */
public class CacheEntity {
    private String hash;
    private String virusName;
    private long timestamp;

    public CacheEntity(String hash, String virusName, long timestamp) {
        this.hash = hash;
        this.virusName = virusName;
        this.timestamp = timestamp;
    }

    public String getHash() {
        return hash;
    }

    public void setHash(String hash) {
        this.hash = hash;
    }

    public String getVirusName() {
        return virusName;
    }

    public void setVirusName(String virusName) {
        this.virusName = virusName;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    @Override
    public String toString() {
        return "CacheEntity{" +
                "hash='" + hash + '\'' +
                ", virusName='" + virusName + '\'' +
                ", timestamp=" + timestamp +
                '}';
    }
}
