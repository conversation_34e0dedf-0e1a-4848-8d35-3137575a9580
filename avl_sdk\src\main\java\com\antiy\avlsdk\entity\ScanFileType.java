package com.antiy.avlsdk.entity;

import com.antiy.avlsdk.AVLEngine;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public enum ScanFileType {
    JAR,
    DEX,
    APK,
    MP3,
    MP4,
    JPG,
    PNG,
    GIF,
    ELF;

    /**
     * 将 List<ScanFileType> 转换为 List<String>
     */
    public static List<String> toStringList(List<ScanFileType> scanTypes) {
        if (scanTypes == null) return new ArrayList<>();
        return scanTypes.stream()
                .map(ScanFileType::name)
                .collect(Collectors.toList());
    }

    /**
     * 将 List<String> 转换为 List<ScanFileType>
     */
    public static List<ScanFileType> fromStringList(List<String> strings) {
        if (strings == null) return new ArrayList<>();
        return strings.stream()
                .map(str -> {
                    try {
                        return ScanFileType.valueOf(str);
                    } catch (IllegalArgumentException e) {
                        AVLEngine.Logger.info("Invalid ScanFileType: " + str);
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
