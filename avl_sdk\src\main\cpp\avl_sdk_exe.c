/*
 * @Date: 2024-09-14 18:14:18
 * @LastEditors: <PERSON><<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@antiy.cn>
 * @LastEditTime: 2025-02-26 11:20:06
 * @FilePath: /cccj-sdk/avl_sdk/src/main/cpp/avl_sdk_exe.c
 */

#include <dirent.h>
#include <malloc.h>
#include <stdbool.h>
#include <stdio.h>
#include <sys/stat.h>
#include <time.h>
#include <unistd.h>

#include "aes.h"
#include "argparse.h"
#include "avl_sdk.h"
#include "avlm_interface.h"
#include "base64.h"
#include "parson.h"

#define UUID      "00000000-0000-0000-0000-000000000000"
#define CLIENT_ID "zhanch"
#define TOKEN                                                                                                                                     \
        "fOz/DjxF2WBlH/Q1lgTDz/oPQr613zoDp+8y0uDV4mJja57ANpeSyKXziKe9L8quzf+Rrf+PdbG++kXe/"                                                       \
        "5ZTd3tAaGiX1u7t6XynLd5mdZyq1eeD+ZGk+VRZrQCUWjxVyer1e3+yXeqy9rvUpfXOxKYxjRIOPvnrMF19tcLgL3jHCYHC42ytzaMSDIZFCAkEgnZ7iB6TjdDFTocP56E+OrK/" \
        "LAMJD1iDnHSxOn2PylE9tfCBRHJmXe3pvfnqN9YoBQAAAA=="

// 命令行帮助
static const char* const gUsage[] = {
    "avlsdk_exe [required options] [options]",
    NULL,
};

// -- 参数变量 --
// 引擎库文件
char *lib_path = NULL, *sig_path = NULL;
// action 控制
bool action_is_scan = false, action_is_update = false, action_is_auth = false;
// action_scan
char* path_to_check = NULL;
// action_update
char *update_package = NULL, *total_package = NULL, *new_version = NULL;
// action auth
char *uuid = NULL, *client_id = NULL, *token = NULL;
bool  decode = false;

// 参数选项
struct argparse_option gOptions[] = {
    OPT_HELP(),
    OPT_GROUP("Basic Options"),
    OPT_STRING('\0', "lib", &lib_path, "path of dir which contains libavlm.so", NULL, 0, 0),
    OPT_STRING('\0', "sig", &sig_path, "path of avl sig dir", NULL, 0, 0),
    OPT_GROUP("Scan Options"),
    OPT_BOOLEAN('s', "scan", &action_is_scan, "perform scan action", NULL, 0, 0),
    OPT_STRING('\0', "path", &path_to_check, "path to be scanned, could be file or directory", NULL, 0, 0),
    OPT_GROUP("Update Options"),
    OPT_BOOLEAN('u', "update", &action_is_update, "update action", NULL, 0, 0),
    OPT_STRING('\0', "increment", &update_package, "update package(incremental)", NULL, 0, 0),
    OPT_STRING('\0', "total", &total_package, "update package(total)", NULL, 0, 0),
    OPT_STRING('\0', "new-version", &new_version, "new version for total update", NULL, 0, 0),
    OPT_GROUP("Auth Options"),
    OPT_BOOLEAN('a', "auth", &action_is_auth, "action_is_auth test mode", NULL, 0, 0),
    OPT_STRING('\0', "uuid", &uuid, "UUID to use sdk", NULL, 0, 0),
    OPT_STRING('\0', "client", &client_id, "Client ID to use sdk", NULL, 0, 0),
    OPT_BOOLEAN('\0', "decode", &decode, "Decode action_is_auth token", NULL, 0, 0),
    OPT_STRING('\0', "token", &token, "Token to use", NULL, 0, 0),
    OPT_END(),
};

typedef struct {
        char* virus_name;
        int   name_len;
} ScanResult;

/**
 * @brief 获取格式化时间戳
 *
 * @return char* 格式化后的字符串
 */
char* get_formatted_timestamp()
{
        time_t     now;
        struct tm* tm_now;
        char       datetime[128];

        time(&now);
        tm_now = localtime(&now);
        strftime(datetime, 128, "%Y%m%d%H%M%S", tm_now);

        // printf("Timestamp: %s\n", datetime);
        return strdup(datetime);
}

// --- for action scan ---

/**
 * @brief 判断输入参数是否为目录
 *
 * @param path 要检测的路径
 *
 * @return bool true，是目录；false，不是目录
 */
bool is_dir(char* path)
{
        // 判断输入参数有效性
        if (!path) { return false; }

        // 通过 stat 判断是否为文件夹
        struct stat fileInfo;
        int         result = stat(path, &fileInfo);

        // 无效路径或没有权限
        if (result != 0) { return false; }

        // 判断是否文件夹
        return S_ISDIR(fileInfo.st_mode);
}

/**
 * @brief 判断输入参数是否是普通文件
 *
 * @param path 要检测的路径
 *
 * @return bool true，是普通文件；false，不是普通文件
 */
bool is_regular_file(char* path)
{
        // 判断输入参数有效性
        if (!path) return false;

        // 通过 stat 判断输入路径是否为普通文件
        struct stat fileInfo;
        int         result = stat(path, &fileInfo);

        // 输入文件不存在或无权限
        if (result != 0) {
                fprintf(stderr, "%s: stat error.\n", __func__);
                return false;
        }

        // 判断是否普通文件
        if (S_ISREG(fileInfo.st_mode)) {
                fprintf(stderr, "%s: %s is regular file.\n", __func__, path);
                return true;
        }
        else {
                fprintf(stderr, "%s: %s is NOT regular file.\n", __func__, path);
                return false;
        }
}

/**
 * @brief 递归扫描参数路径
 *
 * @param path_to_scan 要扫描的路径
 * @param scan_count
 */
void scan_recursively(char* path_to_scan, int* scan_count)
{
        // fprintf(stdout, "Scanning path %s\n", path_to_scan);
        // 判断是否是文件
        if (is_regular_file(path_to_scan)) {
                printf("%s: %s\n", path_to_scan, native_scan(path_to_scan));
                *scan_count += 1;
                return;
        }
        // 判断是否是目录
        else if (is_dir(path_to_scan)) {
                // printf("%s: is dir\n", path_to_scan);
                DIR* dir = opendir(path_to_scan);
                if (!dir) {
                        perror("opendir");
                        return;
                }

                struct dirent* entry;
                while ((entry = readdir(dir)) != NULL) {
                        // printf("calling on entry: %s\n", entry->d_name);
                        char        path_to_entry[1024];
                        struct stat statbuf;

                        if (strcmp(entry->d_name, ".") == 0 || strcmp(entry->d_name, "..") == 0) {
                                // 跳过当前目录和父目录
                                continue;
                        }

                        // 构造完整路径
                        snprintf(path_to_entry, sizeof(path_to_entry), "%s/%s", path_to_scan, entry->d_name);

                        // 递归调用
                        // printf("Scanning %s\n", path_to_entry);
                        scan_recursively(path_to_entry, scan_count);
                }

                closedir(dir);
        }
}

/**
 * 对传入的文件路径执行扫描操作，返回扫描结果对象。如果是目录，返回扫描结果数组。
 *
 * @param path_to_scan 输入参数，要扫描的路径，可以是文件，也可以是目录
 * @param scan_count 输出参数，本次扫描了多少文件。如果要扫描的路径为目录，需要根据此参数来遍历返回值。
 *
 * @return 直接打印扫描结果，无返回值
 */
void action_scan(char* path_to_scan)
{
        // 判断传入参数的有效性
        if (!path_to_scan) { return; }

        int scan_count = 0;
        scan_recursively(path_to_scan, &scan_count);

        printf("%d files scanned!\n", scan_count);
}

// -- for action update --
/**
 * 执行更新操作
 *
 * @param update_package 要安装的更新包路径
 * @param is_total 更新包是全量包还是增量包
 */
void action_update(char* update_package, char* total_package)
{
        bool is_incremental = !!update_package;

        // 判断传入参数有效性
        if ((!update_package || !is_regular_file(update_package)) && (!total_package || !is_regular_file(total_package))) {
                fprintf(stderr, "Invalid update(total) package!\n");
                return;
        }

        // 对于全量包，需要额外提供 newversion 参数
        if (!is_incremental && !new_version) {
                fprintf(stderr, "Missing newversion for total package!\n");
                return;
        }
        else {
                fprintf(stderr, "%s: trying to install %s as version %s\n", __func__, total_package, new_version);
        }


        // 安装更新并输出更新结果
        if (update_package) {
                fprintf(stderr, "%s: installing update...\n", __func__);
                // 增量包
                char* input_file = strrchr(update_package, '/');
                if (!input_file) { input_file = update_package; }
                else {
                        input_file++;   // to ignore first char: '/'
                }

                // now parse version from input_file base name.
                input_file     = strdup(input_file);   // make a copy, remember to free it
                char* last_dot = strrchr(input_file, '.');
                *last_dot      = '\0';
                last_dot++;
                printf("Input suffix is: %s\n", last_dot);
                char* input_ver  = strdup(input_file);
                char* input_type = strcasecmp(last_dot, "zip") == 0 ? "total" : "tar";
                free(input_file);

                printf("Got update from file %s, to version %s, of type %s.\n", update_package, input_ver, input_type);
                int ret = native_install_package(update_package, 1, input_ver, input_type);
                printf("Update result: %d, version after update is <%s>.\n", ret, native_get_sig_version());

                return;
        }
        else {
                // 全量包
                fprintf(stderr, "%s: installing total...\n", __func__);
                int ret = native_install_package(total_package, 1, new_version, "total");
                printf("Update result: %d, version after update is <%s>.\n", ret, native_get_sig_version());
        }
}

// -- for action auth --
void action_auth(char* uuid, char* client_id, bool decode, char* token)
{
        /*         if (uuid && client_id) { native_set_auth_params(uuid, client_id); }
                else {
                        native_set_auth_params("5252D5E2-1EC7-49E8-88CF-30751BC2518A", "zhanch");
                }

                printf("Auth test mode\n");

                printf("Generate key for uuid[%s] & client_id[%s]: %s\n", uuid, client_id, native_generate_key(timestamp, flag));

                char* json_str;
                asprintf(&json_str, "{\"uuid\": \"%s\", \"client_id\": \"%s\"}", uuid, client_id);
                printf("Json str to be aes: %s\n", json_str);

                char* encrypted = native_encrypt(native_generate_key(timestamp, flag), json_str);
                printf("Encrypted result: %s\n", encrypted);
                free(encrypted);


                if (decode) {
                        char* buf = native_get_auth_info();
                        printf("LicenseInfo: %s\n", buf);
                        free(buf);
                }

                return 0; */
}

int main(int argc, char* argv[])
{
        // 时间戳
        char* timestamp = get_formatted_timestamp();

        // 随机数
        srand(time(NULL));
        int flag = rand() % 32;
        // printf("Flag: %d\n", flag);

        struct argparse argparser;

        // 执行解析操作
        argparse_init(&argparser, gOptions, gUsage, 0);
        argparse_describe(&argparser, "\nThis is a caller app for avlm engine.", NULL);
        argc = argparse_parse(&argparser, argc, (const char**)argv);
        if (argc != 0) {
                // there's extra argument
                printf("There are %d extra args", argc);
                for (int i = 0; i < argc; i++) { printf("\targ %d: %s", i + 1, *(argv + i)); }
                return -1;
        }

        // 检测基础选项是否提供
        if (!lib_path || !sig_path) {
                fprintf(stderr, "Please provide both sig_path and lib_path!\n");
                return -1;
        }

        // 设置认证参数
        if (action_is_auth) { action_auth(uuid, client_id, decode, token); }
        else {
                // 手动使用默认参数认证
                native_set_auth_params(UUID, CLIENT_ID);
                int   token_len = 0;
                char* token     = base64_decode(TOKEN, &token_len);
                native_set_auth_token(token, token_len);
                free(token);
        }

        if (access(lib_path, 0) == 0 && access(sig_path, 0) == 0) {
                int ret = native_init(lib_path, sig_path);
                if (ret == 0) {   // printf("Init OK!\n");
                }
                else {
                        printf("Init error: %d\n", ret);
                        return -1;
                }

                // printf("Engine version: %s, siglib version %s.\n", native_get_engine_version(), native_get_sig_version());
        }
        else {
                printf("Failed to access libpath %s or sigpath %s\n", lib_path, sig_path);
        }

        if (action_is_update) { action_update(update_package, total_package); }

        if (action_is_scan) { action_scan(path_to_check); }

        return 0;
}
