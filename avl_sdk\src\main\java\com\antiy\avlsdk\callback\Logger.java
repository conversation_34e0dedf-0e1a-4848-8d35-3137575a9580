package com.antiy.avlsdk.callback;

/**
 * 2 * Copyright (C), 2020-2024
 * 3 * FileName: Logger
 * 4 * Author: wangbiao
 * 5 * Date: 2024/9/2 10:15
 * 6 * Description: Log日志接口
 * 10
 */
public interface Logger {
    /**
     * 最冗余的信息
     * @param msg
     */
    void verbose(String msg);

    /**
     * 提示信息
     * @param msg
     */
    void info(String msg);

    /**
     * 比提示信息更重要，调试相关的
     * @param msg
     */
    void debug(String msg);

    /**
     * 警告信息，非致命错误
     * @param msg
     */
    void warn(String msg);

    /**
     * 错误信息，接口内部报错，不影响sdk
     * @param msg
     */
    void error(String msg);

    /**
     * 致命错误，导致sdk、app崩溃的信息
     * @param msg
     */
    void fatal(String msg);
}
