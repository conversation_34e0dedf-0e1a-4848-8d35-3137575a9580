#!/usr/bin/env python
# -*- coding: utf8 -*-

import requests
import datetime
import hashlib
import random
import base64
import json
from Cryptodome.Cipher import AES


def gen_key(uuid, req_time, flag):
    print("Gen key for uuid[%s], req_time[%s], flag[%d]" % (uuid, req_time, flag))
    uuid_bytes = uuid.encode("utf8")
    uuid_md5 = hashlib.md5(uuid_bytes).hexdigest().upper()
    key_str = "%s%s%s" % (req_time, uuid_md5, int(flag) << 2)
    print("Key str: %s" % key_str)
    key_bytes = key_str.encode("utf8")
    return hashlib.md5(key_bytes).hexdigest().upper().encode("utf8")


# 验证license申请算法

# 请求方 初始化几个变量
uuid = "5252D5E2-1EC7-49E8-88CF-30751BC2518A"
uuid_bytes = uuid.encode("utf8")
client_id = "zhanch"

requestTime = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
requestTime = "20241010150138"

flag = random.randint(0, 31)
flag = 8

# 设置请求头
headers = {
    "uuid": hashlib.md5(uuid_bytes).hexdigest().upper(),
    "requestTime": requestTime,
    "flag": str(flag),
}

print("Headers:", headers)

# 请求地址
# license_url = "http://cccj.dev.be.gk8s.avlyun.org/sdk/license"
# license_url = "http://cccj.lzdev.g.avlyun.org/cccj/api/sdk/license"
license_url = "http://cccj.test.be-pre.gk8s.avlyun.org/cccj/api/sdk/license"
# license_url = "http://10.251.41.29:8080/sdk/license"

# ------------ 请求body ---------------
# 原始json内容
json_str = json.dumps({"uuid": uuid, "client_id": client_id})
json_str_bytes = json_str.encode("utf8")
print("JSON str: %s" % json_str_bytes)

# aes key
aes_key = gen_key(uuid, requestTime, flag)
print("Key is %s, " % aes_key)

# 加密
aes = AES.new(aes_key, AES.MODE_CBC, aes_key[0:16])
pad = lambda s: s + (AES.block_size - len(s) % AES.block_size) * (
        AES.block_size - len(s) % AES.block_size
).to_bytes(1, "big")
encrypted = aes.encrypt(pad(json_str_bytes))
base64_str = base64.b64encode(encrypted).decode("utf8")
print("Encrypted result: %s" % base64_str)

headers['Content-type'] = 'application/json'
headers['Accept'] = 'text/plain'

data = {"content": base64_str,
        "uuid": hashlib.md5(uuid_bytes).hexdigest().upper(),
        "requestTime": requestTime,
        "flag": str(flag)}

print("Request json: %s" % json.dumps(data))

resp = requests.post(license_url, data=json.dumps(data), headers=headers)

# print(resp, resp.text)
print(resp.text)

json_obj = json.loads(resp.text)

if json_obj["code"] != 0:
    exit(-1)

resp_time = json_obj["data"]["responseTime"]
resp_flag = json_obj["data"]["flag"]
data = json_obj['data']["content"]
msg_to_decrypt = base64.b64decode(data)
print(msg_to_decrypt)
print("Decrypt using reqtime %s and flag %s" % (resp_time, resp_flag))
dec_key = gen_key(uuid, resp_time, resp_flag)
aes = AES.new(dec_key, AES.MODE_CBC, dec_key[0:16])
license = aes.decrypt(msg_to_decrypt)
print(license)
