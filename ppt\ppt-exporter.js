/**
 * PPT导出功能模块
 * 使用PptxGenJS库将网页PPT导出为PowerPoint文件
 */

class PPTExporter {
    constructor() {
        this.pptx = null;
        this.initPptx();
    }

    // 初始化PptxGenJS
    initPptx() {
        if (typeof PptxGenJS !== 'undefined') {
            this.pptx = new PptxGenJS();
            this.setupPresentationProperties();
        } else {
            console.error('PptxGenJS library not loaded');
        }
    }

    // 设置演示文稿属性
    setupPresentationProperties() {
        try {
            this.pptx.author = 'CCCJ SDK Team';
            this.pptx.company = 'CCCJ';
            this.pptx.title = 'CCCJ SDK 开发工作报告';
            this.pptx.subject = '项目概览与技术改进详情';

            // 设置幻灯片尺寸 (16:9)
            this.pptx.defineLayout({ name: 'LAYOUT_16x9', width: 10, height: 5.625 });
            this.pptx.layout = 'LAYOUT_16x9';
        } catch (error) {
            console.error('设置演示文稿属性失败:', error);
        }
    }

    // 确保颜色值格式正确（移除#号，确保6位十六进制）
    formatColor(color) {
        if (!color) return 'FFFFFF';
        const cleanColor = String(color).replace('#', '').toUpperCase();
        // 确保是6位十六进制
        if (cleanColor.length === 3) {
            return cleanColor.split('').map(c => c + c).join('');
        }
        return cleanColor.length === 6 ? cleanColor : 'FFFFFF';
    }

    // 确保文本内容为字符串
    formatText(text) {
        if (text === null || text === undefined) return '';
        return String(text);
    }

    // 确保数值为有效数字
    formatNumber(num) {
        const parsed = parseFloat(num);
        return isNaN(parsed) ? 0 : parsed;
    }

    // 导出第一页幻灯片（简化版本）
    exportSlide1() {
        try {
            const slide = this.pptx.addSlide();

            // 设置简单的背景色（避免渐变可能的问题）
            slide.background = { color: this.formatColor('667eea') };

            // 标题
            slide.addText(this.formatText('CCCJ SDK 开发工作报告'), {
                x: this.formatNumber(1),
                y: this.formatNumber(0.5),
                w: this.formatNumber(8),
                h: this.formatNumber(1),
                fontSize: this.formatNumber(36),
                bold: true,
                color: this.formatColor('FFFFFF'),
                align: 'center'
            });

            slide.addText(this.formatText('项目概览与核心成果展示'), {
                x: this.formatNumber(1),
                y: this.formatNumber(1.3),
                w: this.formatNumber(8),
                h: this.formatNumber(0.5),
                fontSize: this.formatNumber(20),
                color: this.formatColor('FFFFFF'),
                align: 'center'
            });

            slide.addText(this.formatText('报告期间：2025年5月8日 - 5月26日'), {
                x: this.formatNumber(1),
                y: this.formatNumber(1.8),
                w: this.formatNumber(8),
                h: this.formatNumber(0.4),
                fontSize: this.formatNumber(14),
                color: this.formatColor('FFFFFF'),
                align: 'center'
            });

            // 简化的指标卡片
            this.addSimpleMetrics(slide);

            return slide;
        } catch (error) {
            console.error('导出第一页失败:', error);
            throw error;
        }
    }

    // 添加简化的指标
    addSimpleMetrics(slide) {
        try {
            const metrics = [
                { title: '总提交数', value: '20' },
                { title: '新增功能', value: '6' },
                { title: '问题修复', value: '4' },
                { title: '重构优化', value: '8' }
            ];

            let yPos = 2.5;
            metrics.forEach((metric, index) => {
                // 简单的文本显示，避免复杂的形状
                slide.addText(this.formatText(`${metric.title}: ${metric.value}`), {
                    x: this.formatNumber(1),
                    y: this.formatNumber(yPos + index * 0.4),
                    w: this.formatNumber(8),
                    h: this.formatNumber(0.3),
                    fontSize: this.formatNumber(18),
                    color: this.formatColor('FFFFFF'),
                    bold: true
                });
            });
        } catch (error) {
            console.error('添加指标失败:', error);
        }
    }

    // 原来的复杂指标卡片（暂时不用）
    addMetricsCards(slide) {
        const metrics = [
            { title: '总提交数', value: '20', color: '3B82F6', x: 0.5 },
            { title: '新增功能', value: '6', color: '10B981', x: 2.8 },
            { title: '问题修复', value: '4', color: 'EF4444', x: 5.1 },
            { title: '重构优化', value: '8', color: '8B5CF6', x: 7.4 }
        ];

        metrics.forEach(metric => {
            // 卡片背景
            slide.addShape('rect', {
                x: this.formatNumber(metric.x),
                y: this.formatNumber(2.5),
                w: this.formatNumber(2),
                h: this.formatNumber(1.2),
                fill: { color: this.formatColor('FFFFFF') },
                line: { width: 0 }
            });

            // 指标标题
            slide.addText(this.formatText(metric.title), {
                x: this.formatNumber(metric.x + 0.1),
                y: this.formatNumber(2.7),
                w: this.formatNumber(1.8),
                h: this.formatNumber(0.3),
                fontSize: this.formatNumber(12),
                color: this.formatColor('6B7280')
            });

            // 指标数值
            slide.addText(this.formatText(metric.value), {
                x: this.formatNumber(metric.x + 0.1),
                y: this.formatNumber(3.0),
                w: this.formatNumber(1.8),
                h: this.formatNumber(0.5),
                fontSize: this.formatNumber(28),
                bold: true,
                color: this.formatColor(metric.color)
            });
        });
    }

    // 添加核心功能
    addCoreFeatures(slide) {
        slide.addText(this.formatText('核心功能增强'), {
            x: 0.5, y: 4.0, w: 4, h: 0.4,
            fontSize: 18, bold: true, color: this.formatColor('FFFFFF')
        });

        const features = [
            '• 文件扫描功能 - ScanFragment新增UI界面和异步扫描逻辑',
            '• 云查杀阈值设置 - 可配置云查杀文件大小阈值，提升扫描效率',
            '• 内容URI处理 - 支持外部应用传入的内容URI处理'
        ];

        features.forEach((feature, index) => {
            slide.addText(this.formatText(feature), {
                x: 0.5, y: 4.4 + index * 0.3, w: 4.5, h: 0.25,
                fontSize: 11, color: this.formatColor('FFFFFF'), transparency: 10
            });
        });
    }

    // 添加技术改进
    addTechImprovements(slide) {
        slide.addText(this.formatText('技术改进亮点'), {
            x: 5.5, y: 4.0, w: 4, h: 0.4,
            fontSize: 18, bold: true, color: this.formatColor('FFFFFF')
        });

        // 进度条
        const improvements = [
            { name: '性能优化', progress: 85 },
            { name: '代码质量', progress: 92 },
            { name: '用户体验', progress: 78 }
        ];

        improvements.forEach((item, index) => {
            const y = 4.4 + index * 0.4;

            slide.addText(this.formatText(`${item.name} ${item.progress}%`), {
                x: 5.5, y: y, w: 4, h: 0.2,
                fontSize: 11, color: this.formatColor('FFFFFF')
            });

            // 进度条背景
            slide.addShape('rect', {
                x: 5.5, y: y + 0.2, w: 3.5, h: 0.1,
                fill: { color: this.formatColor('FFFFFF'), transparency: 70 }
            });

            // 进度条填充
            const colors = ['3B82F6', '10B981', '8B5CF6'];
            slide.addShape('rect', {
                x: 5.5, y: y + 0.2, w: 3.5 * (item.progress / 100), h: 0.1,
                fill: { color: this.formatColor(colors[index]) }
            });
        });
    }

    // 导出第二页幻灯片（简化版本）
    exportSlide2() {
        try {
            const slide = this.pptx.addSlide();

            // 设置简单的背景色
            slide.background = { color: this.formatColor('764ba2') };

            // 标题
            slide.addText(this.formatText('技术改进详情与发展规划'), {
                x: this.formatNumber(1),
                y: this.formatNumber(0.5),
                w: this.formatNumber(8),
                h: this.formatNumber(1),
                fontSize: this.formatNumber(32),
                bold: true,
                color: this.formatColor('FFFFFF'),
                align: 'center'
            });

            slide.addText(this.formatText('深度解析技术优化成果与未来展望'), {
                x: this.formatNumber(1),
                y: this.formatNumber(1.3),
                w: this.formatNumber(8),
                h: this.formatNumber(0.5),
                fontSize: this.formatNumber(18),
                color: this.formatColor('FFFFFF'),
                align: 'center'
            });

            // 简化的时间线
            this.addSimpleTimeline(slide);

            return slide;
        } catch (error) {
            console.error('导出第二页失败:', error);
            throw error;
        }
    }

    // 简化的时间线
    addSimpleTimeline(slide) {
        try {
            const timelineItems = [
                '2025年5月26日 - 文件类型识别重构',
                '2025年5月22日 - 云查杀响应优化',
                '2025年5月21日 - 日志系统规范化',
                '2025年5月13日 - 算法优化升级'
            ];

            let yPos = 2.2;
            timelineItems.forEach((item, index) => {
                slide.addText(this.formatText(item), {
                    x: this.formatNumber(1),
                    y: this.formatNumber(yPos + index * 0.4),
                    w: this.formatNumber(8),
                    h: this.formatNumber(0.3),
                    fontSize: this.formatNumber(16),
                    color: this.formatColor('FFFFFF'),
                    bold: true
                });
            });
        } catch (error) {
            console.error('添加时间线失败:', error);
        }
    }

    // 添加时间线
    addTimeline(slide) {
        slide.addText(this.formatText('技术架构改进时间线'), {
            x: 0.5, y: 1.6, w: 9, h: 0.4,
            fontSize: 20, bold: true, color: this.formatColor('FFFFFF')
        });

        const timelineItems = [
            { date: '2025年5月26日', title: '文件类型识别重构', desc: 'ArchiveIdentifier类，优化APK和JAR文件识别' },
            { date: '2025年5月22日', title: '云查杀响应优化', desc: '修复停止响应问题，优化任务队列处理逻辑' },
            { date: '2025年5月21日', title: '日志系统规范化', desc: '统一日志级别，减少输出量，提升性能' },
            { date: '2025年5月13日', title: '算法优化升级', desc: 'SHA256哈希值替代路径匹配，提升效率和安全性' }
        ];

        const colors = ['3B82F6', '10B981', '8B5CF6', 'F59E0B'];

        timelineItems.forEach((item, index) => {
            const y = 2.2 + index * 0.6;

            // 时间点
            slide.addShape('circle', {
                x: 0.8, y: y, w: 0.15, h: 0.15,
                fill: { color: this.formatColor(colors[index]) }
            });

            // 日期
            slide.addText(this.formatText(item.date), {
                x: 1.2, y: y - 0.1, w: 2, h: 0.3,
                fontSize: 10, bold: true, color: this.formatColor(colors[index])
            });

            // 标题
            slide.addText(this.formatText(item.title), {
                x: 1.2, y: y + 0.15, w: 3.5, h: 0.3,
                fontSize: 14, bold: true, color: this.formatColor('1F2937')
            });

            // 描述
            slide.addText(this.formatText(item.desc), {
                x: 1.2, y: y + 0.35, w: 7.5, h: 0.3,
                fontSize: 11, color: this.formatColor('6B7280')
            });
        });
    }

    // 添加技术突破亮点
    addTechBreakthroughs(slide) {
        const breakthroughs = [
            { title: 'ArchiveIdentifier 重构', desc: '性能提升 25%', x: 5.5, y: 2.2 },
            { title: '扫描引擎增强', desc: '稳定性大幅提升', x: 5.5, y: 3.0 },
            { title: 'Hash匹配算法', desc: '准确率达到 98%', x: 5.5, y: 3.8 }
        ];

        breakthroughs.forEach(item => {
            // 卡片背景
            slide.addShape('rect', {
                x: item.x, y: item.y, w: 4, h: 0.6,
                fill: { color: this.formatColor('FFFFFF') },
                line: { width: 0 }
            });

            // 标题
            slide.addText(this.formatText(item.title), {
                x: item.x + 0.2, y: item.y + 0.1, w: 3.6, h: 0.25,
                fontSize: 14, bold: true, color: this.formatColor('1F2937')
            });

            // 描述
            slide.addText(this.formatText(item.desc), {
                x: item.x + 0.2, y: item.y + 0.35, w: 3.6, h: 0.2,
                fontSize: 11, color: this.formatColor('10B981')
            });
        });
    }

    // 导出完整演示文稿（简化版本）
    async exportFullPresentation() {
        try {
            console.log('开始导出完整演示文稿...');

            // 创建新的实例
            const pptx = new PptxGenJS();

            // 设置基本属性
            pptx.author = 'CCCJ SDK Team';
            pptx.title = 'CCCJ SDK 开发工作报告';

            // 添加第一页 - 项目概览
            const slide1 = pptx.addSlide();
            slide1.background = { color: '667EEA' };

            // 第一页标题
            slide1.addText('CCCJ SDK 开发工作报告', {
                x: 1, y: 0.5, w: 8, h: 0.8,
                fontSize: 36, bold: true, color: 'FFFFFF',
                align: 'center'
            });
            slide1.addText('项目概览与核心成果展示', {
                x: 1, y: 1.2, w: 8, h: 0.4,
                fontSize: 20, color: 'FFFFFF',
                align: 'center'
            });
            slide1.addText('报告期间：2025年5月8日 - 5月26日', {
                x: 1, y: 1.6, w: 8, h: 0.3,
                fontSize: 14, color: 'FFFFFF',
                align: 'center'
            });

            // 第一页主要指标
            slide1.addText('主要指标', {
                x: 1, y: 2.2, w: 8, h: 0.4,
                fontSize: 24, bold: true, color: 'FFFFFF',
                align: 'center'
            });

            const metrics = ['总提交数: 20次', '新增功能: 6项', '问题修复: 4项', '重构优化: 8项'];
            metrics.forEach((metric, index) => {
                slide1.addText(metric, {
                    x: 1 + (index % 2) * 4, y: 2.8 + Math.floor(index / 2) * 0.5,
                    w: 3.5, h: 0.4,
                    fontSize: 18, bold: true, color: 'FFFFFF',
                    align: 'center'
                });
            });

            // 第一页核心功能
            slide1.addText('核心功能增强', {
                x: 1, y: 4.0, w: 8, h: 0.4,
                fontSize: 20, bold: true, color: 'FFFFFF',
                align: 'left'
            });

            const features = [
                '• 文件扫描功能 - ScanFragment新增UI界面和异步扫描逻辑',
                '• 云查杀阈值设置 - 可配置云查杀文件大小阈值，提升扫描效率',
                '• 内容URI处理 - 支持外部应用传入的内容URI处理'
            ];

            features.forEach((feature, index) => {
                slide1.addText(feature, {
                    x: 1, y: 4.4 + index * 0.3,
                    w: 8, h: 0.25,
                    fontSize: 12, color: 'FFFFFF',
                    align: 'left'
                });
            });

            // 添加第二页 - 技术详情
            const slide2 = pptx.addSlide();
            slide2.background = { color: '764BA2' };

            // 第二页标题
            slide2.addText('技术改进详情与发展规划', {
                x: 1, y: 0.5, w: 8, h: 0.8,
                fontSize: 32, bold: true, color: 'FFFFFF',
                align: 'center'
            });
            slide2.addText('深度解析技术优化成果与未来展望', {
                x: 1, y: 1.2, w: 8, h: 0.4,
                fontSize: 18, color: 'FFFFFF',
                align: 'center'
            });

            // 第二页时间线
            slide2.addText('技术架构改进时间线', {
                x: 1, y: 1.8, w: 8, h: 0.4,
                fontSize: 22, bold: true, color: 'FFFFFF',
                align: 'left'
            });

            const timelineItems = [
                '2025年5月26日 - 文件类型识别重构',
                '  ArchiveIdentifier类，优化APK和JAR文件识别',
                '2025年5月22日 - 云查杀响应优化',
                '  修复停止响应问题，优化任务队列处理逻辑',
                '2025年5月21日 - 日志系统规范化',
                '  统一日志级别，减少输出量，提升性能',
                '2025年5月13日 - 算法优化升级',
                '  SHA256哈希值替代路径匹配，提升效率和安全性'
            ];

            timelineItems.forEach((item, index) => {
                const isTitle = !item.startsWith('  ');
                slide2.addText(item, {
                    x: isTitle ? 1 : 1.5,
                    y: 2.3 + index * 0.25,
                    w: isTitle ? 8 : 7.5,
                    h: 0.2,
                    fontSize: isTitle ? 14 : 11,
                    bold: isTitle,
                    color: 'FFFFFF',
                    align: 'left'
                });
            });

            // 第二页技术突破
            slide2.addText('技术突破亮点', {
                x: 1, y: 4.5, w: 8, h: 0.3,
                fontSize: 20, bold: true, color: 'FFFFFF',
                align: 'left'
            });

            const breakthroughs = [
                '• ArchiveIdentifier 重构 - 性能提升 25%',
                '• 扫描引擎增强 - 稳定性大幅提升',
                '• Hash匹配算法 - 准确率达到 98%'
            ];

            breakthroughs.forEach((item, index) => {
                slide2.addText(item, {
                    x: 1, y: 4.9 + index * 0.3,
                    w: 8, h: 0.25,
                    fontSize: 14, bold: true, color: 'FFFFFF',
                    align: 'left'
                });
            });

            // 生成并下载文件
            const fileName = `CCCJ_SDK_开发工作报告_${new Date().toISOString().split('T')[0]}.pptx`;
            console.log('正在生成文件:', fileName);

            await pptx.writeFile({ fileName });
            console.log('文件生成成功');

            return fileName;
        } catch (error) {
            console.error('导出完整演示文稿失败:', error);
            throw error;
        }
    }

    // 导出当前页面（简化版本）
    async exportCurrentSlide(slideNumber) {
        try {
            console.log(`开始导出第${slideNumber}页...`);

            // 创建新的实例
            const pptx = new PptxGenJS();

            // 设置基本属性
            pptx.author = 'CCCJ SDK Team';
            pptx.title = `CCCJ SDK 开发工作报告 - 第${slideNumber}页`;

            // 添加对应页面
            const slide = pptx.addSlide();

            if (slideNumber === 1) {
                slide.background = { color: '667EEA' };

                // 标题区域
                slide.addText('CCCJ SDK 开发工作报告', {
                    x: 1, y: 0.5, w: 8, h: 0.8,
                    fontSize: 36, bold: true, color: 'FFFFFF',
                    align: 'center'
                });
                slide.addText('项目概览与核心成果展示', {
                    x: 1, y: 1.2, w: 8, h: 0.4,
                    fontSize: 20, color: 'FFFFFF',
                    align: 'center'
                });
                slide.addText('报告期间：2025年5月8日 - 5月26日', {
                    x: 1, y: 1.6, w: 8, h: 0.3,
                    fontSize: 14, color: 'FFFFFF',
                    align: 'center'
                });

                // 主要指标
                slide.addText('主要指标', {
                    x: 1, y: 2.2, w: 8, h: 0.4,
                    fontSize: 24, bold: true, color: 'FFFFFF',
                    align: 'center'
                });

                const metrics = [
                    '总提交数: 20次',
                    '新增功能: 6项',
                    '问题修复: 4项',
                    '重构优化: 8项'
                ];

                metrics.forEach((metric, index) => {
                    slide.addText(metric, {
                        x: 1 + (index % 2) * 4, y: 2.8 + Math.floor(index / 2) * 0.5,
                        w: 3.5, h: 0.4,
                        fontSize: 18, bold: true, color: 'FFFFFF',
                        align: 'center'
                    });
                });

                // 核心功能增强
                slide.addText('核心功能增强', {
                    x: 1, y: 4.0, w: 8, h: 0.4,
                    fontSize: 20, bold: true, color: 'FFFFFF',
                    align: 'left'
                });

                const features = [
                    '• 文件扫描功能 - ScanFragment新增UI界面和异步扫描逻辑',
                    '• 云查杀阈值设置 - 可配置云查杀文件大小阈值，提升扫描效率',
                    '• 内容URI处理 - 支持外部应用传入的内容URI处理'
                ];

                features.forEach((feature, index) => {
                    slide.addText(feature, {
                        x: 1, y: 4.4 + index * 0.3,
                        w: 8, h: 0.25,
                        fontSize: 12, color: 'FFFFFF',
                        align: 'left'
                    });
                });
            } else if (slideNumber === 2) {
                slide.background = { color: '764BA2' };

                // 标题区域
                slide.addText('技术改进详情与发展规划', {
                    x: 1, y: 0.5, w: 8, h: 0.8,
                    fontSize: 32, bold: true, color: 'FFFFFF',
                    align: 'center'
                });
                slide.addText('深度解析技术优化成果与未来展望', {
                    x: 1, y: 1.2, w: 8, h: 0.4,
                    fontSize: 18, color: 'FFFFFF',
                    align: 'center'
                });

                // 技术架构改进时间线
                slide.addText('技术架构改进时间线', {
                    x: 1, y: 1.8, w: 8, h: 0.4,
                    fontSize: 22, bold: true, color: 'FFFFFF',
                    align: 'left'
                });

                const timelineItems = [
                    '2025年5月26日 - 文件类型识别重构',
                    '  ArchiveIdentifier类，优化APK和JAR文件识别',
                    '2025年5月22日 - 云查杀响应优化',
                    '  修复停止响应问题，优化任务队列处理逻辑',
                    '2025年5月21日 - 日志系统规范化',
                    '  统一日志级别，减少输出量，提升性能',
                    '2025年5月13日 - 算法优化升级',
                    '  SHA256哈希值替代路径匹配，提升效率和安全性'
                ];

                timelineItems.forEach((item, index) => {
                    const isTitle = !item.startsWith('  ');
                    slide.addText(item, {
                        x: isTitle ? 1 : 1.5,
                        y: 2.3 + index * 0.25,
                        w: isTitle ? 8 : 7.5,
                        h: 0.2,
                        fontSize: isTitle ? 14 : 11,
                        bold: isTitle,
                        color: 'FFFFFF',
                        align: 'left'
                    });
                });

                // 技术突破亮点
                slide.addText('技术突破亮点', {
                    x: 1, y: 4.5, w: 8, h: 0.3,
                    fontSize: 20, bold: true, color: 'FFFFFF',
                    align: 'left'
                });

                const breakthroughs = [
                    '• ArchiveIdentifier 重构 - 性能提升 25%',
                    '• 扫描引擎增强 - 稳定性大幅提升',
                    '• Hash匹配算法 - 准确率达到 98%'
                ];

                breakthroughs.forEach((item, index) => {
                    slide.addText(item, {
                        x: 1, y: 4.9 + index * 0.3,
                        w: 8, h: 0.25,
                        fontSize: 14, bold: true, color: 'FFFFFF',
                        align: 'left'
                    });
                });
            } else {
                throw new Error(`Invalid slide number: ${slideNumber}`);
            }

            const fileName = `CCCJ_SDK_报告_第${slideNumber}页_${new Date().toISOString().split('T')[0]}.pptx`;
            console.log('正在生成文件:', fileName);

            await pptx.writeFile({ fileName });
            console.log('文件生成成功');

            return fileName;
        } catch (error) {
            console.error(`导出第${slideNumber}页失败:`, error);
            throw error;
        }
    }
}

// 全局导出函数
window.exportToPPT = {
    // 导出完整演示文稿
    exportFull: async function() {
        try {
            const exporter = new PPTExporter();
            const fileName = await exporter.exportFullPresentation();
            alert(`演示文稿已成功导出：${fileName}`);
        } catch (error) {
            console.error('导出失败:', error);
            alert('导出失败，请检查浏览器控制台获取详细信息');
        }
    },

    // 导出当前页面
    exportCurrent: async function(slideNumber) {
        try {
            const exporter = new PPTExporter();
            const fileName = await exporter.exportCurrentSlide(slideNumber);
            alert(`当前页面已成功导出：${fileName}`);
        } catch (error) {
            console.error('导出失败:', error);
            alert('导出失败，请检查浏览器控制台获取详细信息');
        }
    }
};
