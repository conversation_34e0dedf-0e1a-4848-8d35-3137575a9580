package com.antiy.demo.activity

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.widget.Toast
import androidx.recyclerview.widget.LinearLayoutManager
import com.antiy.avlsdk.AVLEngine
import com.antiy.avlsdk.entity.CacheEntity
import com.antiy.avlsdk.storage.DataManager
import com.antiy.demo.adapter.CacheManagementAdapter
import com.antiy.demo.base.BaseActivity
import com.antiy.demo.databinding.ActivityCacheManagementBinding
import com.antiy.demo.entity.CacheItem
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import java.util.concurrent.Executors

/**
 * 缓存管理页面
 * 提供缓存数据的查看、删除功能
 */
class CacheManagementActivity : BaseActivity<ActivityCacheManagementBinding>() {

    companion object {
        /**
         * 启动缓存管理页面
         * @param context 上下文
         */
        fun start(context: Context) {
            val intent = Intent(context, CacheManagementActivity::class.java)
            context.startActivity(intent)
        }
    }

    private lateinit var adapter: CacheManagementAdapter
    private val cacheItems = mutableListOf<CacheItem>()

    override fun getViewBinding(inflater: LayoutInflater): ActivityCacheManagementBinding {
        return ActivityCacheManagementBinding.inflate(inflater)
    }

    override fun initView() {
        super.initView()
        
        setupToolbar()
        setupRecyclerView()
        setupButtons()
        loadCacheData()
    }

    private fun setupToolbar() {
        binding.toolbar.setNavigationOnClickListener {
            finish()
        }
        
        // 设置标题
        binding.toolbar.title = "缓存管理"
    }

    private fun setupRecyclerView() {
        adapter = CacheManagementAdapter(
            items = cacheItems,
            onDeleteClick = { item ->
                showDeleteConfirmDialog(item)
            },
            onItemClick = { item ->
                showItemDetailDialog(item)
            }
        )
        
        binding.recyclerView.apply {
            layoutManager = LinearLayoutManager(this@CacheManagementActivity)
            adapter = <EMAIL>
        }
    }

    private fun setupButtons() {
        binding.btnClearAll.setOnClickListener {
            showClearAllConfirmDialog()
        }
        
        binding.btnRefresh.setOnClickListener {
            loadCacheData()
        }
    }

    private fun loadCacheData() {
        binding.progressBar.visibility = View.VISIBLE
        binding.emptyView.visibility = View.GONE
        
        val executor = Executors.newSingleThreadExecutor()
        val handler = Handler(Looper.getMainLooper())
        
        executor.execute {
            try {
                val cacheEntities = DataManager.getInstance().allCacheData
                AVLEngine.Logger.debug("缓存数据:$cacheEntities")
                val items = cacheEntities.map { entity ->
                    CacheItem(
                        hash = entity.hash,
                        virusName = entity.virusName ?: "",
                        timestamp = entity.timestamp
                    )
                }
                
                handler.post {
                    updateListData(items)
                    binding.progressBar.visibility = View.GONE
                    
                    if (cacheItems.isEmpty()) {
                        binding.emptyView.visibility = View.VISIBLE
                        binding.recyclerView.visibility = View.GONE
                    } else {
                        binding.emptyView.visibility = View.GONE
                        binding.recyclerView.visibility = View.VISIBLE
                    }
                }
            } catch (e: Exception) {
                handler.post {
                    binding.progressBar.visibility = View.GONE
                    Toast.makeText(this@CacheManagementActivity, 
                        "加载数据失败: ${e.message}", Toast.LENGTH_SHORT).show()
                    AVLEngine.Logger.error("Failed to load cache data: ${e.message}")
                }
            }
        }
    }

    private fun updateListData(items: List<CacheItem>) {
        cacheItems.clear()
        cacheItems.addAll(items)
        
        // 按时间倒序排序（最新的在前面）
        cacheItems.sortByDescending { it.timestamp }
        adapter.notifyDataSetChanged()
        
        // 更新统计信息
        updateStatistics()
    }

    private fun updateStatistics() {
        val threatCount = adapter.getThreatCount()
        val safeCount = adapter.getSafeCount()
        val totalCount = cacheItems.size
        
        binding.tvStatistics.text = "总计: $totalCount 个，威胁: $threatCount 个，安全: $safeCount 个"
    }

    private fun showDeleteConfirmDialog(item: CacheItem) {
        MaterialAlertDialogBuilder(this)
            .setTitle("删除确认")
            .setMessage("确定要删除这个缓存项吗？\n\nHash: ${item.hash}\n状态: ${item.getStatusText()}")
            .setPositiveButton("删除") { _, _ ->
                deleteCacheItem(item)
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun deleteCacheItem(item: CacheItem) {
        val executor = Executors.newSingleThreadExecutor()
        val handler = Handler(Looper.getMainLooper())
        
        executor.execute {
            try {
                // 从数据库删除
                DataManager.getInstance().deleteCacheItem(item.hash)
                
                handler.post {
                    Toast.makeText(this@CacheManagementActivity, 
                        "删除缓存项成功", Toast.LENGTH_SHORT).show()
                    loadCacheData() // 重新加载数据
                }
            } catch (e: Exception) {
                handler.post {
                    Toast.makeText(this@CacheManagementActivity, 
                        "删除失败: ${e.message}", Toast.LENGTH_SHORT).show()
                    AVLEngine.Logger.error("Failed to delete cache item: ${e.message}")
                }
            }
        }
    }

    private fun showClearAllConfirmDialog() {
        MaterialAlertDialogBuilder(this)
            .setTitle("清空确认")
            .setMessage("确定要清空所有缓存数据吗？\n\n此操作不可撤销，将删除 ${cacheItems.size} 个缓存项。")
            .setPositiveButton("清空") { _, _ ->
                clearAllCache()
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun clearAllCache() {
        val executor = Executors.newSingleThreadExecutor()
        val handler = Handler(Looper.getMainLooper())
        
        executor.execute {
            try {
                // 清空所有缓存数据
                DataManager.getInstance().clearCacheData()
                
                handler.post {
                    Toast.makeText(this@CacheManagementActivity, 
                        "清空缓存成功", Toast.LENGTH_SHORT).show()
                    loadCacheData() // 重新加载数据
                }
            } catch (e: Exception) {
                handler.post {
                    Toast.makeText(this@CacheManagementActivity, 
                        "清空失败: ${e.message}", Toast.LENGTH_SHORT).show()
                    AVLEngine.Logger.error("Failed to clear cache: ${e.message}")
                }
            }
        }
    }

    private fun showItemDetailDialog(item: CacheItem) {
        // 复制Hash值到剪贴板
        val clipboard = getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
        val clip = ClipData.newPlainText("Hash值", item.hash)
        clipboard.setPrimaryClip(clip)
        
        // 显示详情对话框
        val message = """
            Hash值: ${item.hash}
            状态: ${item.getStatusText()}
            病毒名称: ${item.getVirusDisplayName()}
            扫描时间: ${item.getFormattedTime()}
            相对时间: ${item.getRelativeTime()}
            
            ✓ Hash值已复制到剪贴板
        """.trimIndent()
        
        MaterialAlertDialogBuilder(this)
            .setTitle("缓存详情")
            .setMessage(message)
            .setPositiveButton("确定", null)
            .show()
        
        // 显示复制成功提示
        Toast.makeText(this, "Hash值已复制到剪贴板", Toast.LENGTH_SHORT).show()
    }
}
