package com.antiy.avlsdk.license;

import android.content.Context;
import android.text.TextUtils;

import com.antiy.avlsdk.AVLEngine;
import com.antiy.avlsdk.auth.AuthInfoManager;
import com.antiy.avlsdk.auth.LicenseManager;

import org.json.JSONObject;

import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

/**
 * License刷新管理器
 * 独立的license刷新管理模块，负责协调license的刷新逻辑
 * 采用单一职责原则，与AVLEngine完全解耦
 * 
 * 主要功能：
 * - 并发安全的刷新控制
 * - 基于策略的刷新判断
 * - 网络状态检查
 * - 重试机制
 * - 异步回调通知
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class LicenseRefreshManager {
    
    // 默认配置常量
    private static final long DEFAULT_REFRESH_DELAY = 30 * 1000; // 30秒延迟
    private static final long DEFAULT_RETRY_INTERVAL = 60 * 1000; // 60秒重试间隔
    private static final int DEFAULT_MAX_RETRIES = 3; // 最大重试次数
    private static final long DEFAULT_REFRESH_TIMEOUT = 60; // 60秒超时
    
    // 核心组件
    private final Context context;
    private final RefreshPolicy refreshPolicy;
    private final NetworkChecker networkChecker;
    
    // 并发控制
    private final Semaphore refreshSemaphore = new Semaphore(1);
    private final AtomicReference<RefreshState> currentState = new AtomicReference<>(RefreshState.IDLE);
    
    // 配置参数
    private final long refreshDelay;
    private final long retryInterval;
    private final int maxRetries;
    private final long refreshTimeout;
    
    /**
     * 私有构造函数，使用Builder模式创建实例
     */
    private LicenseRefreshManager(Builder builder) {
        this.context = builder.context;
        this.refreshPolicy = builder.refreshPolicy;
        this.networkChecker = builder.networkChecker;
        this.refreshDelay = builder.refreshDelay;
        this.retryInterval = builder.retryInterval;
        this.maxRetries = builder.maxRetries;
        this.refreshTimeout = builder.refreshTimeout;
    }
    
    /**
     * 检查并刷新license
     * 这是主要的对外接口，会根据策略判断是否需要刷新
     * 
     * @param callback 刷新结果回调
     */
    public void checkAndRefresh(RefreshCallback callback) {
        // 使用默认的异步执行
        checkAndRefreshAsync(callback);
    }
    
    /**
     * 异步检查并刷新license
     * 
     * @param callback 刷新结果回调
     */
    public void checkAndRefreshAsync(RefreshCallback callback) {
        Thread refreshThread = new Thread(() -> {
            try {
                checkAndRefreshSync(callback);
            } catch (Exception e) {
                if (AVLEngine.Logger != null) {
                    AVLEngine.Logger.error("License刷新线程异常: " + e.getMessage());
                }
                if (callback != null) {
                    callback.onRefreshFailed("刷新线程异常: " + e.getMessage());
                }
            }
        });
        
        refreshThread.setName("LicenseRefreshThread");
        refreshThread.setDaemon(true);
        refreshThread.start();
    }
    
    /**
     * 同步检查并刷新license
     * 
     * @param callback 刷新结果回调
     */
    public void checkAndRefreshSync(RefreshCallback callback) {
        // 尝试获取刷新锁，避免并发刷新
        try {
            if (!refreshSemaphore.tryAcquire(refreshTimeout, TimeUnit.SECONDS)) {
                String reason = "刷新操作超时，可能有其他刷新正在进行";
                if (AVLEngine.Logger != null) {
                    AVLEngine.Logger.error(reason);
                }
                if (callback != null) {
                    callback.onRefreshFailed(reason);
                }
                return;
            }
            
            try {
                // 双重检查，确保状态一致性
                if (!currentState.compareAndSet(RefreshState.IDLE, RefreshState.REFRESHING)) {
                    String reason = "License正在刷新中，跳过本次请求";
                    if (AVLEngine.Logger != null) {
                        AVLEngine.Logger.info(reason);
                    }
                    if (callback != null) {
                        callback.onRefreshSkipped(reason);
                    }
                    return;
                }
                
                // 执行实际的刷新逻辑
                performRefresh(callback);
                
            } finally {
                // 确保状态重置和资源释放
                currentState.set(RefreshState.IDLE);
                refreshSemaphore.release();
            }
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            String reason = "刷新操作被中断";
            if (AVLEngine.Logger != null) {
                AVLEngine.Logger.error(reason);
            }
            if (callback != null) {
                callback.onRefreshFailed(reason);
            }
        }
    }
    
    /**
     * 执行实际的刷新操作
     * 
     * @param callback 刷新结果回调
     */
    private void performRefresh(RefreshCallback callback) {
        try {
            // 1. 检查是否需要刷新
            if (!refreshPolicy.shouldRefresh()) {
                String reason = "根据刷新策略，当前不需要刷新License";
                if (AVLEngine.Logger != null) {
                    AVLEngine.Logger.info(reason);
                }
                if (callback != null) {
                    callback.onRefreshSkipped(reason);
                }
                return;
            }
            
            // 2. 获取UUID
            String uuid = getDeviceUuid();
            if (TextUtils.isEmpty(uuid)) {
                String reason = "设备UUID为空，无法刷新License";
                if (AVLEngine.Logger != null) {
                    AVLEngine.Logger.error(reason);
                }
                refreshPolicy.onRefreshFailed(reason);
                if (callback != null) {
                    callback.onRefreshFailed(reason);
                }
                return;
            }
            
            // 3. 通知开始刷新
            if (callback != null) {
                callback.onRefreshStart();
            }
            
            // 4. 延迟执行，等待网络服务初始化
            if (refreshDelay > 0) {
                if (AVLEngine.Logger != null) {
                    AVLEngine.Logger.info("将在" + (refreshDelay / 1000) + "秒后尝试刷新License");
                }
                Thread.sleep(refreshDelay);
            }
            
            // 5. 执行带重试的刷新
            boolean success = refreshWithRetry(uuid);
            
            // 6. 处理结果
            if (success) {
                refreshPolicy.onRefreshSuccess();
                if (callback != null) {
                    callback.onRefreshSuccess();
                }
            } else {
                String reason = "License刷新失败，已达到最大重试次数: " + maxRetries;
                refreshPolicy.onRefreshFailed(reason);
                if (callback != null) {
                    callback.onRefreshFailed(reason);
                }
            }
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            String reason = "License刷新被中断";
            if (AVLEngine.Logger != null) {
                AVLEngine.Logger.error(reason);
            }
            refreshPolicy.onRefreshFailed(reason);
            if (callback != null) {
                callback.onRefreshFailed(reason);
            }
        } catch (Exception e) {
            String reason = "License刷新异常: " + e.getMessage();
            if (AVLEngine.Logger != null) {
                AVLEngine.Logger.error(reason);
            }
            refreshPolicy.onRefreshFailed(reason);
            if (callback != null) {
                callback.onRefreshFailed(reason);
            }
        }
    }
    
    /**
     * 获取设备UUID
     *
     * @return 设备UUID，如果获取失败返回null
     */
    private String getDeviceUuid() {
        try {
            JSONObject authInfo = AuthInfoManager.getInstance(context).getAuthInfo();
            return authInfo.optString("uuid", "");
        } catch (Exception e) {
            if (AVLEngine.Logger != null) {
                AVLEngine.Logger.error("获取设备UUID失败: " + e.getMessage());
            }
            return null;
        }
    }

    /**
     * 带重试机制的license刷新
     *
     * @param uuid 设备唯一标识
     * @return true表示刷新成功，false表示刷新失败
     */
    private boolean refreshWithRetry(String uuid) {
        int retryCount = 0;
        boolean refreshSuccess = false;

        do {
            try {
                // 检查网络是否可用
                if (!networkChecker.isNetworkAvailable()) {
                    if (AVLEngine.Logger != null) {
                        AVLEngine.Logger.error("网络不可用，无法刷新License");
                    }

                    // 如果是第一次尝试，等待网络可用
                    if (retryCount == 0) {
                        if (AVLEngine.Logger != null) {
                            AVLEngine.Logger.info("等待网络可用...");
                        }
                        // 等待30秒网络可用
                        if (!networkChecker.waitForNetworkAvailable(30000, 5000)) {
                            if (AVLEngine.Logger != null) {
                                AVLEngine.Logger.error("等待网络超时");
                            }
                        }
                    }

                    // 再次检查网络
                    if (!networkChecker.isNetworkAvailable()) {
                        retryCount++;
                        if (retryCount < maxRetries) {
                            if (AVLEngine.Logger != null) {
                                AVLEngine.Logger.info("将在" + (retryInterval / 1000) + "秒后进行第" + (retryCount + 1) + "次重试");
                            }
                            Thread.sleep(retryInterval);
                        }
                        continue;
                    }
                }

                if (AVLEngine.Logger != null) {
                    AVLEngine.Logger.info("尝试刷新License，当前尝试次数: " + (retryCount + 1));
                }

                // 执行刷新
                LicenseManager licenseManager = new LicenseManager(context);
                licenseManager.requestAuthToken(uuid);

                // 等待刷新完成
                Thread.sleep(5000);

                // 验证刷新是否成功
                if (verifyLicenseRefresh()) {
                    if (AVLEngine.Logger != null) {
                        AVLEngine.Logger.info("License刷新成功");
                    }
                    refreshSuccess = true;
                    break;
                } else {
                    if (AVLEngine.Logger != null) {
                        AVLEngine.Logger.error("License刷新验证失败");
                    }
                }

            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                if (AVLEngine.Logger != null) {
                    AVLEngine.Logger.error("License刷新线程被中断: " + e.getMessage());
                }
                break;
            } catch (Exception e) {
                if (AVLEngine.Logger != null) {
                    AVLEngine.Logger.error("License刷新失败: " + e.getMessage());
                }
            }

            // 增加重试计数
            retryCount++;
            if (retryCount < maxRetries && !refreshSuccess) {
                try {
                    if (AVLEngine.Logger != null) {
                        AVLEngine.Logger.info("将在" + (retryInterval / 1000) + "秒后进行第" + (retryCount + 1) + "次重试");
                    }
                    Thread.sleep(retryInterval);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }

        } while (retryCount < maxRetries && !refreshSuccess);

        return refreshSuccess;
    }

    /**
     * 验证license刷新是否成功
     *
     * @return true表示刷新成功，false表示刷新失败
     */
    private boolean verifyLicenseRefresh() {
        try {
            // 获取最新的授权信息
            JSONObject authInfo = AuthInfoManager.getInstance(context).getAuthInfo();

            // 验证token是否有效
            if (authInfo != null && authInfo.has("token")) {
                String token = authInfo.getString("token");

                // 验证token不为空
                boolean isValid = !TextUtils.isEmpty(token);

                if (AVLEngine.Logger != null) {
                    AVLEngine.Logger.info("License验证结果: " + (isValid ? "有效" : "无效"));
                }

                return isValid;
            }
        } catch (Exception e) {
            if (AVLEngine.Logger != null) {
                AVLEngine.Logger.error("验证License刷新失败: " + e.getMessage());
            }
        }

        return false;
    }

    /**
     * 获取当前刷新状态
     *
     * @return 当前的刷新状态
     */
    public RefreshState getCurrentState() {
        return currentState.get();
    }

    /**
     * 强制刷新license
     * 忽略策略判断，强制执行刷新操作
     *
     * @param callback 刷新结果回调
     */
    public void forceRefresh(RefreshCallback callback) {
        Thread refreshThread = new Thread(() -> {
            try {
                // 尝试获取刷新锁
                if (!refreshSemaphore.tryAcquire(refreshTimeout, TimeUnit.SECONDS)) {
                    String reason = "强制刷新操作超时";
                    if (AVLEngine.Logger != null) {
                        AVLEngine.Logger.error(reason);
                    }
                    if (callback != null) {
                        callback.onRefreshFailed(reason);
                    }
                    return;
                }

                try {
                    if (!currentState.compareAndSet(RefreshState.IDLE, RefreshState.REFRESHING)) {
                        String reason = "License正在刷新中，无法强制刷新";
                        if (AVLEngine.Logger != null) {
                            AVLEngine.Logger.error(reason);
                        }
                        if (callback != null) {
                            callback.onRefreshFailed(reason);
                        }
                        return;
                    }

                    // 获取UUID
                    String uuid = getDeviceUuid();
                    if (TextUtils.isEmpty(uuid)) {
                        String reason = "设备UUID为空，无法强制刷新License";
                        if (AVLEngine.Logger != null) {
                            AVLEngine.Logger.error(reason);
                        }
                        if (callback != null) {
                            callback.onRefreshFailed(reason);
                        }
                        return;
                    }

                    // 通知开始刷新
                    if (callback != null) {
                        callback.onRefreshStart();
                    }

                    // 执行强制刷新
                    boolean success = refreshWithRetry(uuid);

                    // 处理结果
                    if (success) {
                        refreshPolicy.onRefreshSuccess();
                        if (callback != null) {
                            callback.onRefreshSuccess();
                        }
                    } else {
                        String reason = "强制刷新失败";
                        refreshPolicy.onRefreshFailed(reason);
                        if (callback != null) {
                            callback.onRefreshFailed(reason);
                        }
                    }

                } finally {
                    currentState.set(RefreshState.IDLE);
                    refreshSemaphore.release();
                }

            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                String reason = "强制刷新被中断";
                if (AVLEngine.Logger != null) {
                    AVLEngine.Logger.error(reason);
                }
                if (callback != null) {
                    callback.onRefreshFailed(reason);
                }
            } catch (Exception e) {
                String reason = "强制刷新异常: " + e.getMessage();
                if (AVLEngine.Logger != null) {
                    AVLEngine.Logger.error(reason);
                }
                if (callback != null) {
                    callback.onRefreshFailed(reason);
                }
            }
        });

        refreshThread.setName("LicenseForceRefreshThread");
        refreshThread.setDaemon(true);
        refreshThread.start();
    }

    /**
     * 获取刷新策略描述
     *
     * @return 刷新策略的描述信息
     */
    public String getRefreshPolicyDescription() {
        return refreshPolicy.getDescription();
    }

    /**
     * 检查是否正在刷新
     *
     * @return true表示正在刷新，false表示空闲
     */
    public boolean isRefreshing() {
        return currentState.get() == RefreshState.REFRESHING;
    }

    /**
     * Builder模式构建器
     * 用于创建LicenseRefreshManager实例
     */
    public static class Builder {
        private final Context context;
        private RefreshPolicy refreshPolicy;
        private NetworkChecker networkChecker;
        private long refreshDelay = DEFAULT_REFRESH_DELAY;
        private long retryInterval = DEFAULT_RETRY_INTERVAL;
        private int maxRetries = DEFAULT_MAX_RETRIES;
        private long refreshTimeout = DEFAULT_REFRESH_TIMEOUT;

        /**
         * 构造函数
         *
         * @param context 应用上下文
         */
        public Builder(Context context) {
            this.context = context.getApplicationContext();
        }

        /**
         * 设置刷新策略
         *
         * @param refreshPolicy 刷新策略
         * @return Builder实例
         */
        public Builder setRefreshPolicy(RefreshPolicy refreshPolicy) {
            this.refreshPolicy = refreshPolicy;
            return this;
        }

        /**
         * 设置网络检查器
         *
         * @param networkChecker 网络检查器
         * @return Builder实例
         */
        public Builder setNetworkChecker(NetworkChecker networkChecker) {
            this.networkChecker = networkChecker;
            return this;
        }

        /**
         * 设置刷新延迟时间
         *
         * @param refreshDelay 延迟时间（毫秒）
         * @return Builder实例
         */
        public Builder setRefreshDelay(long refreshDelay) {
            this.refreshDelay = refreshDelay;
            return this;
        }

        /**
         * 设置重试间隔
         *
         * @param retryInterval 重试间隔（毫秒）
         * @return Builder实例
         */
        public Builder setRetryInterval(long retryInterval) {
            this.retryInterval = retryInterval;
            return this;
        }

        /**
         * 设置最大重试次数
         *
         * @param maxRetries 最大重试次数
         * @return Builder实例
         */
        public Builder setMaxRetries(int maxRetries) {
            this.maxRetries = maxRetries;
            return this;
        }

        /**
         * 设置刷新超时时间
         *
         * @param refreshTimeout 超时时间（秒）
         * @return Builder实例
         */
        public Builder setRefreshTimeout(long refreshTimeout) {
            this.refreshTimeout = refreshTimeout;
            return this;
        }

        /**
         * 构建LicenseRefreshManager实例
         *
         * @return LicenseRefreshManager实例
         */
        public LicenseRefreshManager build() {
            // 设置默认值
            if (refreshPolicy == null) {
                // 默认7天刷新间隔
                refreshPolicy = new TimeBasedRefreshPolicy(context, 7 * 24 * 60 * 60 * 1000L);
            }

            if (networkChecker == null) {
                networkChecker = new NetworkChecker(context);
            }

            return new LicenseRefreshManager(this);
        }
    }
}
