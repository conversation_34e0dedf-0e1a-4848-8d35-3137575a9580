package com.antiy.avlsdk.license;

import android.content.Context;
import android.content.SharedPreferences;

import com.antiy.avlsdk.AVLEngine;

import java.util.Date;

/**
 * 基于时间的License刷新策略
 * 根据上次刷新时间和设定的刷新间隔来判断是否需要刷新
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class TimeBasedRefreshPolicy implements RefreshPolicy {
    
    private static final String PREFS_NAME = "license_refresh_policy";
    private static final String KEY_LAST_REFRESH_TIME = "last_refresh_time";
    private static final String KEY_LAST_REFRESH_SUCCESS = "last_refresh_success";
    
    private final Context context;
    private final long refreshInterval;
    private final SharedPreferences preferences;
    
    /**
     * 构造函数
     * 
     * @param context 应用上下文
     * @param refreshInterval 刷新间隔（毫秒）
     */
    public TimeBasedRefreshPolicy(Context context, long refreshInterval) {
        this.context = context.getApplicationContext();
        this.refreshInterval = refreshInterval;
        this.preferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
    }
    
    @Override
    public boolean shouldRefresh() {
        try {
            long lastRefreshTime = getLastRefreshTime();
            long currentTime = System.currentTimeMillis();
            
            // 如果从未刷新过，或者距离上次刷新已超过指定间隔，则需要刷新
            boolean needRefresh = (lastRefreshTime == 0) || 
                                 (currentTime - lastRefreshTime > refreshInterval);
            
            if (AVLEngine.Logger != null) {
                AVLEngine.Logger.info("License时间检查: 上次刷新时间=" + new Date(lastRefreshTime) + 
                                      ", 当前时间=" + new Date(currentTime) + 
                                      ", 刷新间隔=" + (refreshInterval / 1000 / 60 / 60) + "小时" +
                                      ", 是否需要刷新=" + needRefresh);
            }
            
            return needRefresh;
        } catch (Exception e) {
            if (AVLEngine.Logger != null) {
                AVLEngine.Logger.error("检查License刷新时间失败: " + e.getMessage());
            }
            // 如果检查失败，默认需要刷新
            return true;
        }
    }
    
    @Override
    public void onRefreshSuccess() {
        try {
            SharedPreferences.Editor editor = preferences.edit();
            long currentTime = System.currentTimeMillis();
            editor.putLong(KEY_LAST_REFRESH_TIME, currentTime);
            editor.putBoolean(KEY_LAST_REFRESH_SUCCESS, true);
            editor.apply();
            
            if (AVLEngine.Logger != null) {
                AVLEngine.Logger.info("已保存License刷新成功时间: " + new Date(currentTime));
            }
        } catch (Exception e) {
            if (AVLEngine.Logger != null) {
                AVLEngine.Logger.error("保存License刷新时间失败: " + e.getMessage());
            }
        }
    }
    
    @Override
    public void onRefreshFailed(String reason) {
        try {
            SharedPreferences.Editor editor = preferences.edit();
            editor.putBoolean(KEY_LAST_REFRESH_SUCCESS, false);
            editor.apply();
            
            if (AVLEngine.Logger != null) {
                AVLEngine.Logger.error("License刷新失败: " + reason);
            }
        } catch (Exception e) {
            if (AVLEngine.Logger != null) {
                AVLEngine.Logger.error("保存License刷新失败状态时出错: " + e.getMessage());
            }
        }
    }
    
    @Override
    public String getDescription() {
        long hours = refreshInterval / (1000 * 60 * 60);
        return "基于时间的刷新策略，刷新间隔: " + hours + "小时";
    }
    
    /**
     * 获取上次license刷新时间
     * 
     * @return 上次刷新的时间戳，如果从未刷新过则返回0
     */
    private long getLastRefreshTime() {
        return preferences.getLong(KEY_LAST_REFRESH_TIME, 0);
    }
    
    /**
     * 获取上次刷新是否成功
     * 
     * @return true表示上次刷新成功，false表示失败
     */
    public boolean wasLastRefreshSuccessful() {
        return preferences.getBoolean(KEY_LAST_REFRESH_SUCCESS, false);
    }
    
    /**
     * 获取距离上次刷新的时间
     * 
     * @return 距离上次刷新的毫秒数
     */
    public long getTimeSinceLastRefresh() {
        long lastRefreshTime = getLastRefreshTime();
        if (lastRefreshTime == 0) {
            return Long.MAX_VALUE; // 从未刷新过
        }
        return System.currentTimeMillis() - lastRefreshTime;
    }
}
