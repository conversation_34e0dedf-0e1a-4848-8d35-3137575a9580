package com.antiy.demo

import android.content.Context
import android.os.Build
import android.util.Log
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.platform.app.InstrumentationRegistry
import com.antiy.avlsdk.AVLEngine
import com.antiy.avlsdk.callback.DownloadCallback
import com.antiy.avlsdk.callback.RequestCallback
import com.antiy.avlsdk.callback.ScanListener
import com.antiy.avlsdk.entity.RequestMethod
import com.antiy.avlsdk.entity.ResultScan
import com.antiy.avlsdk.utils.SdkConst
import org.junit.Assert.assertEquals
import org.junit.Assert.assertFalse
import org.junit.Assert.assertNotNull
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import java.io.File
import java.io.FileOutputStream
import java.util.UUID

/**
 * Instrumented test, which will execute on an Android device.
 *
 * See [testing documentation](http://d.android.com/tools/testing).
 */
@RunWith(AndroidJUnit4::class)
class AVLEngineTest {
    private var context: Context? = null

    @Before
    fun setUp() {
        context = InstrumentationRegistry.getInstrumentation().targetContext


        // 手动请求权限
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            InstrumentationRegistry.getInstrumentation().uiAutomation
                .executeShellCommand(
                    "pm grant " + context?.packageName +
                            " android.permission.READ_EXTERNAL_STORAGE"
                )

            InstrumentationRegistry.getInstrumentation().uiAutomation
                .executeShellCommand(
                    "pm grant " + context?.packageName +
                            " android.permission.WRITE_EXTERNAL_STORAGE"
                )
        }


    }

    @Test
    fun useAppContext() {
        // Context of the app under test.
        val appContext = InstrumentationRegistry.getInstrumentation().targetContext
        assertEquals("com.antiy.demo", appContext.packageName)
    }

    @Test
    fun testInit() {
        AVLEngine.getInstance().release()
        val result = AVLEngine.init(context, UUID.randomUUID().toString(), ALog(context!!), NetworkManager())
        assertEquals(result.isSuccess, true)
    }

    @Test
    fun testGetVersion() {
        val version = AVLEngine.getVersion()
        assertEquals(version, "1.1")
    }

    @Test
    fun testGetVdbVersion() {
        val version = AVLEngine.getVdbVersion()
        Log.e("test", version)
    }

    @Test
    fun testCheckUpdate() {
        AVLEngine.getInstance().release()
        val init = AVLEngine.init(context, UUID.randomUUID().toString(), ALog(context!!), NetworkManager())
        assertEquals(init.isSuccess, true)

        val result = AVLEngine.getInstance().checkUpdate()
        assertNotNull("更新结果不应为空", result)
        
        if (!result.isUpdateSucceeded) {
            // 如果更新失败，确保有错误原因
            assertFalse("失败时应该有错误原因", result.isUpdateSucceeded)
        }
    }

    @Test
    fun testCheckUpdateTimeout() {
        // 创建一个会超时的网络管理器，并跟踪请求是否被取消
        val slowNetworkManager = object : NetworkManager() {
            var requestCancelled = false
            var downloadCancelled = false
            
            override fun request(uri: String?, method: RequestMethod?, param: Any?, callback: RequestCallback?) {
                Thread.sleep(31000) // 31秒，超过30秒超时限制
            }
            
            override fun download(uri: String?, callback: DownloadCallback?) {
                if (!downloadCancelled) {
                    callback?.onFinish(200, "test.zip", "/test/path")
                }
            }
            
            override fun cancelRequest() {
                requestCancelled = true
                downloadCancelled = true
            }
        }

        AVLEngine.getInstance().release()
        val uuid = UUID.randomUUID().toString()
        AVLEngine.init(context, uuid, ALog(context!!), slowNetworkManager)
        
        val result = AVLEngine.getInstance().checkUpdate()
        Log.e("testCheckUpdateTimeout",result.toString())
        // 验证结果
        assertFalse(result.isUpdateSucceeded)
        assertFalse(result.isHasUpdate)
        
        // 验证请求是否被取消
        assertTrue("超时后请求应该被取消", slowNetworkManager.requestCancelled)
        assertTrue("超时后下载应该被取消", slowNetworkManager.downloadCancelled)
        
        // 等待一段时间，确保没有后续操作执行
        Thread.sleep(2000)
    }

    @Test
    fun testCheckUpdateError() {
        // 创建一个会返回错误的网络管理器
        val errorNetworkManager = object : NetworkManager() {
            override fun request(uri: String?, method: RequestMethod?, param: Any?, callback: RequestCallback?) {
                callback?.onError("Network error")
            }
        }
        AVLEngine.getInstance().release()
        val uuid = UUID.randomUUID().toString()
        AVLEngine.init(context, uuid, ALog(context!!), errorNetworkManager)
        
        val result = AVLEngine.getInstance().checkUpdate()
        assertNotNull(result)
        assertFalse(result.isUpdateSucceeded)
    }

    @Test
    fun testScanFile() {
        // 创建测试文件
        val testFile = createTestFile()
        
        val result = AVLEngine.getInstance().scanFile(testFile.absolutePath)
        assertNotNull(result)
        assertFalse(result.isMalicious)
        
        // 清理测试文件
        testFile.delete()
    }

    @Test 
    fun testScanDir() {
        val testDir = createTestDirectory()
        val scanFinished = BooleanArray(1)
        
        AVLEngine.getInstance().scanDir(testDir.absolutePath, object : ScanListener {
            override fun scanStart() {}
            override fun scanStop() {}
            override fun scanFinish() {
                scanFinished[0] = true
            }
            override fun scanCount(count: Int) {
                assertTrue(count > 0)
            }
            override fun scanFileStart(index: Int, path: String) {}
            override fun scanFileFinish(index: Int, path: String, result: ResultScan) {}
            override fun scanError(errorMsg: String?) {

            }
        })
        
        Thread.sleep(5000)
        assertTrue(scanFinished[0])
        deleteDirectory(testDir)
    }

    @Test
    fun testScanControl() {
        // 1. 准备测试目录和文件
        val testDir = createTestDirectory()
        val emptyDir = createEmptyDirectory()
        val testFile = createTestFile()
        val nonExistDir = File(context?.externalCacheDir, "non_exist_dir")
        val scanStatus = BooleanArray(3) // [isPaused, isResumed, isStopped]
        
        // 2. 测试空目录
        AVLEngine.getInstance().scanDir(emptyDir.absolutePath, createTestListener(scanStatus))
        Thread.sleep(1000)
        assertTrue("空目录扫描应该完成", scanStatus[2])
        
        // 3. 测试不存在的目录
        scanStatus.fill(false)
        AVLEngine.getInstance().scanDir(nonExistDir.absolutePath, createTestListener(scanStatus))
        Thread.sleep(1000)
        assertTrue("不存在目录应该触发扫描完成", scanStatus[2])
        
        // 4. 测试文件路径（非目录）
        scanStatus.fill(false)
        AVLEngine.getInstance().scanDir(testFile.absolutePath, createTestListener(scanStatus))
        Thread.sleep(1000)
        assertTrue("文件路径应该触发扫描完成", scanStatus[2])
        
        // 5. 测试正常目录的扫描控制
        scanStatus.fill(false)
        AVLEngine.getInstance().scanDir(testDir.absolutePath, createTestListener(scanStatus))
        
        // 5.1 测试暂停
        AVLEngine.getInstance().scanPause()
        scanStatus[0] = true
        Thread.sleep(1000)
        
        // 5.2 测试恢复
        AVLEngine.getInstance().scanResume()
        scanStatus[0] = false
        scanStatus[1] = true
        Thread.sleep(1000)
        
        // 5.3 测试停止
        AVLEngine.getInstance().scanStop()
        Thread.sleep(1000)
        assertTrue("正常扫描停止应该触发回调", scanStatus[2])
        
        // 6. 测试特殊路径
        val specialPaths = arrayOf(
            "", // 空字符串
            " ", // 空格
            "   ", // 多个空格
            "/", // 根目录
            ".", // 当前目录
            "..", // 上级目录
            testDir.absolutePath + File.separator, // 路径末尾带分隔符
            testDir.absolutePath + File.separator + ".", // 路径末尾带当前目录
            testDir.absolutePath.repeat(10) // 超长路径
        )
        
        for (path in specialPaths) {
            scanStatus.fill(false)
            AVLEngine.getInstance().scanDir(path, createTestListener(scanStatus))
            Thread.sleep(1000)
            assertTrue("特殊路径 '$path' 应该正常完成扫描", scanStatus[2])
        }
        
        // 7. 清理测试资源
        deleteDirectory(testDir)
        deleteDirectory(emptyDir)
        testFile.delete()
    }

    // 创建测试监听器
    private fun createTestListener(scanStatus: BooleanArray) = object : ScanListener {
        override fun scanStart() {
            // 验证开始扫描时状态是否正确
            assertFalse("扫描开始时不应该是暂停状态", scanStatus[0])
            assertFalse("扫描开始时不应该是停止状态", scanStatus[2])
        }
        
        override fun scanStop() {
            scanStatus[2] = true
        }
        
        override fun scanFinish() {
            // 扫描完成时，确保不在暂停状态
            assertFalse("扫描完成时不应该是暂停状态", scanStatus[0])
        }
        
        override fun scanCount(count: Int) {
            if (scanStatus[0]) {
                assertEquals("暂停状态下不应该更新计数", 0, count)
            } else {
                assertTrue("扫描文件数量应该大于等于0", count >= 0)
            }
        }
        
        override fun scanFileStart(index: Int, path: String) {
            assertFalse("暂停状态下不应该开始新文件扫描", scanStatus[0])
            assertTrue("文件索引应该大于等于0", index >= 0)
            assertNotNull("文件路径不应该为null", path)
            assertFalse("文件路径不应该为空", path.isEmpty())
        }
        
        override fun scanFileFinish(index: Int, path: String, result: ResultScan) {
            assertNotNull("扫描结果不应该为null", result)
            assertTrue("文件索引应该大于等于0", index >= 0)
            assertNotNull("文件路径不应该为null", path)
            assertFalse("文件路径不应该为空", path.isEmpty())
        }

        override fun scanError(errorMsg: String?) {

        }
    }

    // 创建空目录
    private fun createEmptyDirectory(): File {
        val dir = File(context?.externalCacheDir, "empty_dir")
        dir.mkdirs()
        return dir
    }

    @Test
    fun testRelease() {
        AVLEngine.getInstance().release()
        // 验证释放后的状态或重新初始化是否成功
        val result = AVLEngine.init(context, UUID.randomUUID().toString(), ALog(context!!), NetworkManager())
        assertEquals(result.isSuccess, true)
    }

    // 辅助方法
    private fun createTestFile(): File {
        val file = File(context?.externalCacheDir, "test.txt")
        FileOutputStream(file).use { 
            it.write("Test content".toByteArray())
        }
        return file
    }

    private fun createTestDirectory(): File {
        val dir = File(context?.externalCacheDir, "test_dir")
        dir.mkdirs()
        createTestFile("test1.txt", dir)
        createTestFile("test2.txt", dir)
        return dir
    }

    private fun createTestFile(name: String, dir: File): File {
        val file = File(dir, name)
        FileOutputStream(file).use {
            it.write("Test content".toByteArray())
        }
        return file
    }

    private fun deleteDirectory(dir: File) {
        dir.listFiles()?.forEach { it.delete() }
        dir.delete()
    }
}