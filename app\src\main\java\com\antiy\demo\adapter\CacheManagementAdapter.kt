package com.antiy.demo.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.antiy.demo.databinding.ItemCacheManagementBinding
import com.antiy.demo.entity.CacheItem

/**
 * 缓存管理RecyclerView适配器
 */
class CacheManagementAdapter(
    private val items: MutableList<CacheItem>,
    private val onDeleteClick: (CacheItem) -> Unit,
    private val onItemClick: (CacheItem) -> Unit
) : RecyclerView.Adapter<CacheManagementAdapter.ViewHolder>() {

    inner class ViewHolder(private val binding: ItemCacheManagementBinding) : 
        RecyclerView.ViewHolder(binding.root) {
        
        fun bind(item: CacheItem) {
            binding.apply {
                // 设置Hash值（显示简短版本）
                tvHash.text = item.getShortHash()
                
                // 设置完整Hash值作为副标题
                tvFullHash.text = "${item.hash}\n点击查看详情并复制"
                
                // 设置状态标签
                tvStatus.text = item.getStatusText()
                tvStatus.setTextColor(ContextCompat.getColor(root.context, item.getStatusColorRes()))
                
                // 设置状态背景
                val backgroundRes = if (item.isThreat()) {
                    android.R.color.holo_red_light
                } else {
                    android.R.color.holo_green_light
                }
                chipStatus.setChipBackgroundColorResource(backgroundRes)
                chipStatus.text = item.getStatusText()
                
                // 设置病毒名称
                tvVirusName.text = item.getVirusDisplayName()
                
                // 设置时间
                tvTime.text = item.getRelativeTime()
                
                // 设置点击事件
                root.setOnClickListener {
                    onItemClick(item)
                }
                
                // 设置删除按钮点击事件
                btnDelete.setOnClickListener {
                    onDeleteClick(item)
                }
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding = ItemCacheManagementBinding.inflate(
            LayoutInflater.from(parent.context), 
            parent, 
            false
        )
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(items[position])
    }

    override fun getItemCount(): Int = items.size

    /**
     * 更新数据
     */
    fun updateItems(newItems: List<CacheItem>) {
        items.clear()
        items.addAll(newItems)
        notifyDataSetChanged()
    }

    /**
     * 删除项目
     */
    fun removeItem(position: Int) {
        if (position in 0 until items.size) {
            items.removeAt(position)
            notifyItemRemoved(position)
        }
    }

    /**
     * 根据hash删除项目
     */
    fun removeItemByHash(hash: String) {
        val position = items.indexOfFirst { it.hash == hash }
        if (position != -1) {
            removeItem(position)
        }
    }

    /**
     * 获取威胁项目数量
     */
    fun getThreatCount(): Int {
        return items.count { it.isThreat() }
    }

    /**
     * 获取安全项目数量
     */
    fun getSafeCount(): Int {
        return items.count { !it.isThreat() }
    }
}
