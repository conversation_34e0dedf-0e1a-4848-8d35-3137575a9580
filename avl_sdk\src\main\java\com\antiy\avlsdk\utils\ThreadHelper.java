package com.antiy.avlsdk.utils;

import android.os.Handler;
import android.os.Looper;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.List;

/**
 * Author: wangbiao
 * Date: 2024/9/14 10:00
 * Description:
 */
public class ThreadHelper {
    private static volatile ThreadHelper instance;
    private static ThreadPoolExecutor threadPoolExecutor;
    private static final int CORE_POOL_SIZE = 5; // 核心线程数量
    private static final int MAXIMUM_POOL_SIZE = 10; // 最大线程数量
    private static final int KEEP_ALIVE_TIME = 30; // 空闲线程保持时间
    private static final TimeUnit KEEP_ALIVE_UNIT = TimeUnit.SECONDS; // 时间单位
    private static final BlockingQueue<Runnable> WORK_QUEUE = new LinkedBlockingQueue<>(); // 工作队列大小
    private static Handler mainHandler;

    private ThreadHelper() {
        threadPoolExecutor = new ThreadPoolExecutor(
                CORE_POOL_SIZE,
                MAXIMUM_POOL_SIZE,
                KEEP_ALIVE_TIME,
                KEEP_ALIVE_UNIT,
                WORK_QUEUE,
                Executors.defaultThreadFactory(),
                new ThreadPoolExecutor.CallerRunsPolicy());
        mainHandler = new Handler(Looper.getMainLooper());
    }

    public static ThreadHelper getInstance() {
        if (instance == null) {
            synchronized (ThreadHelper.class) {
                if (instance == null) {
                    instance = new ThreadHelper();
                }
            }
        }
        return instance;
    }

    /**
     * 提交一个任务到线程池执行。
     *
     * @param task 要执行的任务
     */
    public void execute(Runnable task) {
        threadPoolExecutor.execute(task);
    }

    public Future submit(Runnable task){
        return threadPoolExecutor.submit(task);
    }

    /**
     * 在主线程中执行任务。
     *
     * @param runnable 要在主线程中执行的任务
     */
    public void runOnUiThread(Runnable runnable) {
        mainHandler.post(runnable);
    }

    /**
     * 关闭线程池。
     */
    public void shutdown() {
        if(!threadPoolExecutor.isShutdown()){
            threadPoolExecutor.shutdown();
        }
    }

    public boolean isShutdown(){
        return threadPoolExecutor.isShutdown();
    }

    public void shutdownNow() {
        if (threadPoolExecutor != null && !threadPoolExecutor.isShutdown()) {
            threadPoolExecutor.shutdownNow();
            // 可以在这里处理未执行的任务
            threadPoolExecutor = null; // 将引用设为 null
        }
    }
}
