package com.antiy.demo

import android.content.Context
import android.content.SharedPreferences
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.platform.app.InstrumentationRegistry
import com.antiy.avlsdk.AVLEngine
import com.antiy.avlsdk.config.Config
import com.antiy.avlsdk.config.ConfigKey
import com.antiy.avlsdk.entity.ScanFileType
import com.antiy.avlsdk.storage.DBConstants
import com.antiy.avlsdk.storage.DataManager
import com.antiy.avlsdk.utils.JsonUtils
import com.antiy.avlsdk.utils.SdkConst
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import java.util.*
import kotlin.collections.HashMap

@RunWith(AndroidJUnit4::class)
class ConfigTest {
    private lateinit var context: Context
    private lateinit var sharedPreferences: SharedPreferences
    private lateinit var avlEngine: AVLEngine

    @Before
    fun setUp() {
        context = InstrumentationRegistry.getInstrumentation().targetContext
        sharedPreferences = context.getSharedPreferences(DBConstants.SHARE_PREFERENCES_CONFIG, Context.MODE_PRIVATE)
        // 清除之前的配置
        sharedPreferences.edit().clear().apply()
        // 初始化引擎
        val uuid = sharedPreferences.getString(SdkConst.AUTH_UUID,"")
        AVLEngine.init(context, uuid, ALog(context), NetworkManager())
        avlEngine = AVLEngine.getInstance()
        // 清除数据库数据
        DataManager.getInstance().clearBlackWhiteList()
    }

    @Test
    fun testNormalConfig() {
        // 创建正常配置
        val config = Config.Builder()
            .setBlackWhiteList(HashMap<String, Boolean>().apply { 
                put("test_hash1", true)
                put("test_hash2", false) 
            })
            .setScanType(listOf(ScanFileType.APK, ScanFileType.DEX, ScanFileType.JAR))
            .setCloudCheckThreshold(500)
            .setCloudSizeThreshold(100 * 1024 * 1024)
            .setSilentPerformanceThreshold(80)
            .setHistoryTimeout(7 * 24 * 60 * 60 * 1000)
            .setHistorySize(1000)
            .build()

        // 加载配置
        avlEngine.loadConfig(config)

        // 验证配置是否正确保存
        val blackWhiteList = DataManager.getInstance().blackWhiteListData
        assertEquals("黑白名单大小应该为2", 2, blackWhiteList.size)
        assertTrue(blackWhiteList["test_hash1"] == true)
        assertTrue(blackWhiteList["test_hash2"] == false)

        val savedScanTypes = JsonUtils.jsonToList(
            sharedPreferences.getString(ConfigKey.SCAN_TYPE.toString(), ""),
            String::class.java
        )
        assertEquals(3, savedScanTypes.size)
        assertTrue(savedScanTypes.contains("apk"))

        val cloudThreshold = sharedPreferences.getInt(ConfigKey.CLOUDSCAN_COUNT.toString(), 0)
        assertEquals(500, cloudThreshold)
    }

    @Test
    fun testBoundaryConditions() {
        // 测试边界值
        val config = Config.Builder()
            .setBlackWhiteList(HashMap()) // 空黑白名单
            .setScanType(emptyList()) // 空扫描类型列表
            .setCloudCheckThreshold(0) // 最小阈值
            .setCloudSizeThreshold(0)
            .setSilentPerformanceThreshold(0)
            .setHistoryTimeout(0)
            .setHistorySize(0)
            .build()

        avlEngine.loadConfig(config)

        // 验证边界值处理
        val cloudThreshold = sharedPreferences.getInt(ConfigKey.CLOUDSCAN_COUNT.toString(), -1)
        assertEquals(0, cloudThreshold)

        val blackWhiteList = DataManager.getInstance().blackWhiteListData
        assertTrue(blackWhiteList.isEmpty())

        val savedScanTypes = JsonUtils.jsonToList(
            sharedPreferences.getString(ConfigKey.SCAN_TYPE.toString(), ""),
            String::class.java
        )
        assertTrue(savedScanTypes.isEmpty())
    }

    @Test
    fun testMaxValues() {
        // 测试最大值
        val config = Config.Builder()
            .setBlackWhiteList(HashMap<String, Boolean>().apply {
                repeat(1000) { put("hash_$it", it % 2 == 0) }
            })
//            .setScanType(List(100) { "type_$it" })
            .setCloudCheckThreshold(Int.MAX_VALUE)
            .setCloudSizeThreshold(Long.MAX_VALUE)
            .setSilentPerformanceThreshold(100)
            .setHistoryTimeout(Long.MAX_VALUE)
            .setHistorySize(Long.MAX_VALUE)
            .build()

        avlEngine.loadConfig(config)

        // 验证最大值处理
        val cloudThreshold = sharedPreferences.getInt(ConfigKey.CLOUDSCAN_COUNT.toString(), -1)
        assertEquals(Int.MAX_VALUE, cloudThreshold)

        val silentThreshold = sharedPreferences.getInt(ConfigKey.SILENTSCAN_RES.toString(), -1)
        assertEquals(100, silentThreshold)
    }

    @Test
    fun testInvalidValues() {
        // 测试无效值
        val config = Config.Builder()
            .setBlackWhiteList(null) // null 黑白名单
            .setScanType(null) // null 扫描类型
            .setCloudCheckThreshold(-1) // 负阈值
            .setCloudSizeThreshold(-1)
            .setSilentPerformanceThreshold(-1)
            .setHistoryTimeout(-1)
            .setHistorySize(-1)
            .build()

        avlEngine.loadConfig(config)

        // 验证无效值处理
        val cloudThreshold = DataManager.getInstance().getCloudCheckThreshold()
        assertEquals("无效的云查阈值应该返回默认值500", 500, cloudThreshold)

        val blackWhiteList = DataManager.getInstance().getBlackWhiteListData()
        assertNotNull("黑白名单不应为null", blackWhiteList)
        
        val silentThreshold = DataManager.getInstance().getSilentPerformanceThreshold()
        assertEquals("无效的静默扫描阈值应该返回默认值100", 100, silentThreshold)
    }

    @Test
    fun testSpecialCharacters() {
        // 测试特殊字符
        val config = Config.Builder()
            .setBlackWhiteList(HashMap<String, Boolean>().apply {
                put("!@#$%^&*()", true)
                put("中文测试", false)
                put(" ", true)
                put("", false)
            })
            .setScanType(listOf(ScanFileType.APK, ScanFileType.DEX, ScanFileType.JAR))
            .build()

        avlEngine.loadConfig(config)

        // 验证特殊字符处理
        val blackWhiteList = DataManager.getInstance().blackWhiteListData
        assertTrue(blackWhiteList.containsKey("!@#$%^&*()"))
        assertTrue(blackWhiteList.containsKey("中文测试"))

        val savedScanTypes = JsonUtils.jsonToList(
            sharedPreferences.getString(ConfigKey.SCAN_TYPE.toString(), ""),
            String::class.java
        )
        assertTrue(savedScanTypes.contains("apk!"))
    }

    @Test
    fun testMultipleConfigs() {
        // 测试多次加载配置
        val config1 = Config.Builder()
            .setBlackWhiteList(HashMap()) // 提供空的HashMap而不是null
            .setCloudCheckThreshold(100)
            .build()

        val config2 = Config.Builder()
            .setBlackWhiteList(HashMap()) // 提供空的HashMap而不是null
            .setCloudCheckThreshold(200)
            .build()

        // 连续加载两次配置
        avlEngine.loadConfig(config1)
        avlEngine.loadConfig(config2)

        // 验证是否使用最新配置
        val cloudThreshold = sharedPreferences.getInt(ConfigKey.CLOUDSCAN_COUNT.toString(), -1)
        assertEquals(200, cloudThreshold)
    }

    @Test
    fun testConfigPersistence() {
        // 测试配置持久化
        val config = Config.Builder()
            .setCloudCheckThreshold(300)
            .build()

        avlEngine.loadConfig(config)

        // 重新创建 SharedPreferences 实例
        val newSharedPreferences = context.getSharedPreferences(
            DBConstants.SHARE_PREFERENCES_CONFIG,
            Context.MODE_PRIVATE
        )

        // 验证配置是否持久化
        val cloudThreshold = newSharedPreferences.getInt(ConfigKey.CLOUDSCAN_COUNT.toString(), -1)
        assertEquals(300, cloudThreshold)
    }
} 