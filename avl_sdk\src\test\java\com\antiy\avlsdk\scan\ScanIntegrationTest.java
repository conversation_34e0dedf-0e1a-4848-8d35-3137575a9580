package com.antiy.avlsdk.scan;

import com.antiy.avlsdk.AVLEngine;
import com.antiy.avlsdk.callback.ScanListener;
import com.antiy.avlsdk.entity.ResultScan;
import com.antiy.avlsdk.entity.ScanErrorType;
import com.antiy.avlsdk.storage.DataManager;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.annotation.Config;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * 扫描集成测试
 * 验证整体扫描流程的正确性和稳定性
 */
@RunWith(RobolectricTestRunner.class)
@Config(sdk = 28)
public class ScanIntegrationTest {

    @Mock
    private AVLEngine mockEngine;
    
    @Mock
    private DataManager mockDataManager;

    private File tempDir;
    private List<File> smallFiles;
    private List<File> largeFiles;

    @Before
    public void setUp() throws IOException {
        MockitoAnnotations.initMocks(this);
        
        // 创建测试环境
        tempDir = createTempDirectory();
        smallFiles = createSmallFiles(3); // 小于云扫描阈值
        largeFiles = createLargeFiles(2);  // 大于云扫描阈值
        
        // Mock基础组件
        setupMocks();
    }

    /**
     * 测试1：完整的扫描生命周期
     * 验证：从开始到结束的完整流程
     */
    @Test
    public void testCompleteScanLifecycle() throws InterruptedException {
        final AtomicBoolean scanStarted = new AtomicBoolean(false);
        final AtomicBoolean scanFinished = new AtomicBoolean(false);
        final AtomicInteger filesScanned = new AtomicInteger(0);
        final CountDownLatch finishLatch = new CountDownLatch(1);

        List<File> allFiles = new ArrayList<>();
        allFiles.addAll(smallFiles);
        allFiles.addAll(largeFiles);

        CloudScanner scanner = new CloudScanner(allFiles, new ScanListener() {
            @Override
            public void scanStart() {
                scanStarted.set(true);
                System.out.println("扫描开始");
            }

            @Override
            public void scanStop() {
                System.out.println("扫描停止");
            }

            @Override
            public void scanFinish() {
                scanFinished.set(true);
                finishLatch.countDown();
                System.out.println("扫描完成");
            }

            @Override
            public void scanCount(int count) {
                System.out.println("文件总数: " + count);
            }

            @Override
            public void scanFileStart(int index, String path) {
                System.out.println("开始扫描文件 " + index + ": " + path);
            }

            @Override
            public void scanFileFinish(int index, String path, ResultScan result) {
                filesScanned.incrementAndGet();
                System.out.println("完成扫描文件 " + index + ": " + path + 
                                 " (恶意: " + (result != null ? result.isMalicious : "null") + ")");
            }

            @Override
            public void scanError(ScanErrorType errorMsg) {

            }
        }, true);

        // 启动扫描
        scanner.startScan();

        // 等待扫描完成
        boolean completed = finishLatch.await(30, TimeUnit.SECONDS);

        // 验证结果
        assertTrue("扫描应该在30秒内完成", completed);
        assertTrue("扫描应该已开始", scanStarted.get());
        assertTrue("扫描应该已完成", scanFinished.get());
        assertEquals("应该扫描了所有文件", allFiles.size(), filesScanned.get());

        System.out.println("完整生命周期测试通过 - 扫描了 " + filesScanned.get() + " 个文件");
    }

    /**
     * 测试2：混合文件大小的扫描策略
     * 验证：小文件和大文件的不同处理路径
     */
    @Test
    public void testMixedFileSizeScanStrategy() throws InterruptedException {
        final AtomicInteger smallFileCount = new AtomicInteger(0);
        final AtomicInteger largeFileCount = new AtomicInteger(0);
        final CountDownLatch finishLatch = new CountDownLatch(1);

        // 测试单个小文件（应该走本地扫描）
        for (File smallFile : smallFiles) {
            ResultScan result = FileScanner.scan(smallFile.getAbsolutePath(), false);
            assertNotNull("小文件应该返回扫描结果", result);
            smallFileCount.incrementAndGet();
        }

        // 测试单个大文件（应该走云扫描）
        for (File largeFile : largeFiles) {
            ResultScan result = FileScanner.scan(largeFile.getAbsolutePath(), false);
            assertNotNull("大文件应该返回扫描结果", result);
            largeFileCount.incrementAndGet();
        }

        assertEquals("应该处理了所有小文件", smallFiles.size(), smallFileCount.get());
        assertEquals("应该处理了所有大文件", largeFiles.size(), largeFileCount.get());

        System.out.println("混合文件大小测试通过 - 小文件: " + smallFileCount.get() + 
                         ", 大文件: " + largeFileCount.get());
    }

    /**
     * 测试3：并发扫描的稳定性
     * 验证：多个扫描任务并发执行时的稳定性
     */
    @Test
    public void testConcurrentScanStability() throws InterruptedException {
        final int CONCURRENT_SCANS = 3;
        final CountDownLatch allFinishedLatch = new CountDownLatch(CONCURRENT_SCANS);
        final AtomicInteger successCount = new AtomicInteger(0);
        final AtomicInteger errorCount = new AtomicInteger(0);

        // 启动多个并发扫描
        for (int i = 0; i < CONCURRENT_SCANS; i++) {
            final int scanIndex = i;
            new Thread(() -> {
                try {
                    List<File> filesToScan = (scanIndex % 2 == 0) ? smallFiles : largeFiles;
                    
                    CloudScanner scanner = new CloudScanner(filesToScan, new ScanListener() {
                        @Override
                        public void scanStart() {}

                        @Override
                        public void scanStop() {}

                        @Override
                        public void scanFinish() {
                            successCount.incrementAndGet();
                            allFinishedLatch.countDown();
                            System.out.println("并发扫描 " + scanIndex + " 完成");
                        }

                        @Override
                        public void scanCount(int count) {}

                        @Override
                        public void scanFileStart(int index, String path) {}

                        @Override
                        public void scanFileFinish(int index, String path, ResultScan result) {}
                    }, true);

                    scanner.startScan();

                } catch (Exception e) {
                    errorCount.incrementAndGet();
                    allFinishedLatch.countDown();
                    System.err.println("并发扫描 " + scanIndex + " 失败: " + e.getMessage());
                }
            }).start();
        }

        // 等待所有扫描完成
        boolean allCompleted = allFinishedLatch.await(45, TimeUnit.SECONDS);

        assertTrue("所有并发扫描应该在45秒内完成", allCompleted);
        assertEquals("所有扫描应该成功", CONCURRENT_SCANS, successCount.get());
        assertEquals("不应该有错误", 0, errorCount.get());

        System.out.println("并发稳定性测试通过 - 成功: " + successCount.get() + 
                         ", 错误: " + errorCount.get());
    }

    /**
     * 测试4：资源使用监控
     * 验证：扫描过程中的资源使用情况
     */
    @Test
    public void testResourceUsageMonitoring() throws InterruptedException {
        final AtomicInteger activeThreadsBefore = new AtomicInteger(Thread.activeCount());
        final AtomicInteger activeThreadsDuring = new AtomicInteger(0);
        final AtomicInteger activeThreadsAfter = new AtomicInteger(0);
        final CountDownLatch finishLatch = new CountDownLatch(1);

        List<File> allFiles = new ArrayList<>();
        allFiles.addAll(smallFiles);
        allFiles.addAll(largeFiles);

        CloudScanner scanner = new CloudScanner(allFiles, new ScanListener() {
            @Override
            public void scanStart() {
                activeThreadsDuring.set(Thread.activeCount());
            }

            @Override
            public void scanStop() {}

            @Override
            public void scanFinish() {
                finishLatch.countDown();
            }

            @Override
            public void scanCount(int count) {}

            @Override
            public void scanFileStart(int index, String path) {}

            @Override
            public void scanFileFinish(int index, String path, ResultScan result) {}
        }, true);

        scanner.startScan();

        // 等待扫描完成
        assertTrue("扫描应该在20秒内完成", finishLatch.await(20, TimeUnit.SECONDS));

        // 等待资源清理
        Thread.sleep(2000);
        activeThreadsAfter.set(Thread.activeCount());

        // 验证资源使用
        System.out.println("线程数 - 开始前: " + activeThreadsBefore.get() + 
                         ", 扫描中: " + activeThreadsDuring.get() + 
                         ", 结束后: " + activeThreadsAfter.get());

        // 扫描结束后线程数应该回到接近开始前的水平
        int threadDifference = activeThreadsAfter.get() - activeThreadsBefore.get();
        assertTrue("扫描结束后线程数增长应该在合理范围内", Math.abs(threadDifference) <= 2);

        System.out.println("资源使用监控测试通过");
    }

    // 辅助方法：设置Mock对象
    private void setupMocks() {
        // Mock AVLEngine
        when(mockEngine.getInitResult()).thenReturn(createSuccessInitResult());
        when(mockEngine.getNetworkManager()).thenReturn(createMockNetworkManager());

        // Mock DataManager
        when(mockDataManager.getCloudSizeThreshold()).thenReturn(2048L); // 2KB阈值
    }

    // 辅助方法：创建临时目录
    private File createTempDirectory() throws IOException {
        File tempDir = File.createTempFile("integration_test", "dir");
        tempDir.delete();
        tempDir.mkdirs();
        tempDir.deleteOnExit();
        return tempDir;
    }

    // 辅助方法：创建小文件（小于云扫描阈值）
    private List<File> createSmallFiles(int count) throws IOException {
        List<File> files = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            File file = new File(tempDir, "small_file_" + i + ".txt");
            FileWriter writer = new FileWriter(file);
            writer.write("Small file content " + i); // 小于2KB
            writer.close();
            file.deleteOnExit();
            files.add(file);
        }
        return files;
    }

    // 辅助方法：创建大文件（大于云扫描阈值）
    private List<File> createLargeFiles(int count) throws IOException {
        List<File> files = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            File file = new File(tempDir, "large_file_" + i + ".txt");
            FileWriter writer = new FileWriter(file);
            // 写入超过2KB的内容
            for (int j = 0; j < 200; j++) {
                writer.write("Large file content line " + j + " for file " + i + "\n");
            }
            writer.close();
            file.deleteOnExit();
            files.add(file);
        }
        return files;
    }

    // 辅助方法：创建成功的初始化结果
    private Object createSuccessInitResult() {
        return new Object() {
            public boolean isSuccess = true;
        };
    }

    // 辅助方法：创建Mock网络管理器
    private Object createMockNetworkManager() {
        return new Object() {
            public boolean isAvailable() {
                return true;
            }
        };
    }
}
