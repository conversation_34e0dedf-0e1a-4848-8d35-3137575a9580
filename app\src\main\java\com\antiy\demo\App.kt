package com.antiy.demo

import android.app.Application
import com.antiy.avlsdk.AVLEngine
import java.util.UUID


/**
2 * Copyright (C), 2020-2024
3 * FileName: App
4 * Author: wangbiao
5 * Date: 2024/9/2 15:06
6 * Description:
10 */
class App : Application() {
    // 保存UUID供初始化时使用
    private val uuid = UUID.randomUUID().toString()

    override fun onCreate() {
        super.onCreate()
        // 在这里不再初始化AVLEngine，而是在获取权限后调用
    }

    /**
     * 初始化 AVLEngine
     * 在获取到文件权限后调用此方法
     * 
     * @return 返回初始化后的UUID
     */
    fun initializeAVLEngine(): String {
        AVLEngine.init(this, uuid, ALog(this), NetworkManager())
        return uuid
    }

    /**
     * 检查AVLEngine是否已初始化
     * 
     * @return 如果已初始化则返回true，否则返回false
     */
    fun isAVLEngineInitialized(): <PERSON><PERSON><PERSON> {
        return AVLEngine.getInstance().initResult?.isSuccess == true
    }
}
