package com.antiy.demo

import android.content.Context
import android.util.Pair
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.platform.app.InstrumentationRegistry
import com.antiy.avlsdk.AVLEngine
import com.antiy.avlsdk.entity.ResultUpdate
import com.antiy.avlsdk.entity.VirusUpdateEntity
import com.antiy.avlsdk.update.*
import com.antiy.avlsdk.utils.SdkConst
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import java.io.File

@RunWith(AndroidJUnit4::class)
class UpdateTest {
    private lateinit var context: Context
    private lateinit var updateCoordinator: UpdateCoordinator
    private lateinit var virusDatabaseUpdater: VirusDatabaseUpdater

    @Before
    fun setUp() {
        context = InstrumentationRegistry.getInstrumentation().targetContext
        // 初始化引擎
        AVLEngine.init(context, "", AL<PERSON>(context), NetworkManager())
        updateCoordinator = UpdateCoordinator()
        virusDatabaseUpdater = VirusDatabaseUpdater.getInstance()
        
        // 清理测试目录
        cleanTestDirectories()
    }

    /**
     * 测试更新类型解析功能
     * 验证:
     * 1. 不同更新类型组合的解析
     * 2. 无效输入的处理
     */
    @Test
    fun testUpdateTypesParsing() {
        val testCases = mapOf(
            "0_0" to Pair(UpdateTypeEnum.NONE, UpdateTypeEnum.NONE),
            "0_1" to Pair(UpdateTypeEnum.NONE, UpdateTypeEnum.INCREMENTAL),
            "0_2" to Pair(UpdateTypeEnum.NONE, UpdateTypeEnum.FULL),
            "1_0" to Pair(UpdateTypeEnum.INCREMENTAL, UpdateTypeEnum.NONE),
            "2_0" to Pair(UpdateTypeEnum.FULL, UpdateTypeEnum.NONE),
            "1_1" to Pair(UpdateTypeEnum.INCREMENTAL, UpdateTypeEnum.INCREMENTAL),
            "2_2" to Pair(UpdateTypeEnum.FULL, UpdateTypeEnum.FULL)
        )

        testCases.forEach { (input, expected) ->
            val entity = VirusUpdateEntity().apply {
                istotal = input
                version = "20241009.bl_2024101016161200"
            }
            val result = updateCoordinator.handleUpdate(entity, "", "")
            assertNotNull("更新结果不应为空", result)
        }
    }

    /**
     * 测试更新策略工厂
     * 验证:
     * 1. 不同平台和更新类型的策略创建
     * 2. 策略类型的正确性
     */
    @Test
    fun testUpdateStrategyFactory() {
        // Android全量更新策略
        val androidFullStrategy = UpdateStrategyFactory.createStrategy(
            UpdateTypeEnum.FULL, 
            PlatformType.ANDROID
        )
        assertTrue(androidFullStrategy is AndroidFullUpdateStrategy)

        // PC增量更新策略
        val pcIncrementalStrategy = UpdateStrategyFactory.createStrategy(
            UpdateTypeEnum.INCREMENTAL, 
            PlatformType.PC
        )
        assertTrue(pcIncrementalStrategy is PCIncrementUpdateStrategy)
    }

    /**
     * 测试更新包路径生成
     * 验证:
     * 1. 不同更新类型组合的路径生成
     * 2. 路径格式的正确性
     */
    @Test
    fun testUpdatePathGeneration() {
        val baseDir = context.filesDir.absolutePath + "/test_update/"
        File(baseDir).mkdirs()

        val entity = VirusUpdateEntity().apply {
            istotal = "2_2"
            version = "20241009.bl_2024101016161200"
        }

        val paths = updateCoordinator.generatePath(
            entity,
            Pair(UpdateTypeEnum.FULL, UpdateTypeEnum.FULL),
            baseDir
        )
        
        assertNotNull("路径数组不应为空", paths)
        assertEquals("应该生成两个路径", 2, paths.size)
        assertTrue("Android更新包路径应包含.zip后缀", 
            paths[0].endsWith(SdkConst.ZIP_SUFFIX))
    }

    /**
     * 测试增量更新功能
     * 验证:
     * 1. 增量更新包的解析
     * 2. 文件变更的应用
     * 3. diff文件的处理
     */
    @Test
    fun testIncrementalUpdate() {
        val strategy = PCIncrementUpdateStrategy()
        val testDir = createTestIncrementalUpdateFiles()
        
        try {
            strategy.incrementalUpdate(
                testDir + "/update",
                testDir + "/target"
            )
            
            // 验证更新结果
            val targetDir = File(testDir + "/target")
            assertTrue("目标文件夹应该存在", targetDir.exists())
            assertTrue("新增文件应该存在", 
                File(targetDir, "new_file.txt").exists())
            assertFalse("删除的文件不应存在", 
                File(targetDir, "deleted_file.txt").exists())
            
        } finally {
            // 清理测试文件
            File(testDir).deleteRecursively()
        }
    }

    /**
     * 测试全量更新功能
     * 验证:
     * 1. 更新包的解压和安装
     * 2. 版本信息的更新
     * 3. 备份和回滚机制
     */
    @Test
    fun testFullUpdate() {
        val strategy = PCFullUpdateStrategy()
        val testDir = createTestFullUpdateFiles()
        
        try {
            val result = strategy.performUpdate(
                testDir + "/update.zip",
                Pair(UpdateTypeEnum.FULL, UpdateTypeEnum.FULL)
            )
            
            assertTrue("全量更新应该成功", result)
            assertFalse("备份文件应该被清理",
                File(testDir + SdkConst.AVL_PC_BACKUP_SUFFIX).exists())
            
        } finally {
            File(testDir).deleteRecursively()
        }
    }

    /**
     * 测试更新协调器
     * 验证:
     * 1. 多平台更新的协调
     * 2. 更新结果的处理
     * 3. 错误恢复机制
     */
    @Test
    fun testUpdateCoordination() {
        val entity = VirusUpdateEntity().apply {
            istotal = "2_2"
            version = "20241009.bl_2024101016161200"
            downloadUrl = "test_update.zip"
        }
        
        val result = updateCoordinator.handleUpdate(
            entity,
            context.filesDir.absolutePath + "/test_update.zip",
            context.filesDir.absolutePath + "/update/"
        )
        
        assertNotNull("更新结果不应为空", result)
        assertTrue("更新应该有变更", result.hasUpdate)
    }

    // 辅助方法

    private fun cleanTestDirectories() {
        File(context.filesDir, "test_update").deleteRecursively()
        File(context.filesDir, "update").deleteRecursively()
    }

    private fun createTestIncrementalUpdateFiles(): String {
        val testDir = context.filesDir.absolutePath + "/test_incremental"
        File(testDir).mkdirs()
        
        // 创建更新源目录
        File(testDir + "/update").mkdirs()
        File(testDir + "/update/new_file.txt").writeText("new content")
        File(testDir + "/update/diff.txt").writeText(
            """
            +new_file.txt
            -deleted_file.txt
            """.trimIndent()
        )
        
        // 创建目标目录
        File(testDir + "/target").mkdirs()
        File(testDir + "/target/deleted_file.txt").writeText("old content")
        
        return testDir
    }

    private fun createTestFullUpdateFiles(): String {
        val testDir = context.filesDir.absolutePath + "/test_full"
        File(testDir).mkdirs()
        
        // 创建测试更新包
        File(testDir + "/update.zip").writeBytes(ByteArray(1024))
        
        return testDir
    }
} 