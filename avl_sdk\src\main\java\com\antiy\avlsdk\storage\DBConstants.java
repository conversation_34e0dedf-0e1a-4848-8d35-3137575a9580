package com.antiy.avlsdk.storage;

/**
 * 2 * Copyright (C), 2020-2024
 * 3 * FileName: DBConstants
 * 4 * Author: wang<PERSON>o
 * 5 * Date: 2024/9/4 11:37
 * 6 * Description: 数据库常量
 * 10
 */
public class DBConstants {
    public static final String DATABASE_NAME = "avl_storage.db";
    public static final int DATABASE_VERSION = 1;
    public static final String SHARE_PREFERENCES_CONFIG = "avl_config";

    //黑白名单表
    public static final String TABLE_BLACK_WHITE = "black_white_list";
    public static final String BLACK_WHITE_COLUMN_HASH = "hash";
    public static final String BLACK_WHITE_COLUMN_IS_BLACK = "is_black";

    //缓存表
    public static final String TABLE_CACHE = "cache";
    public static final String CACHE_HASH = "hash";
    public static final String CACHE_VIRUS_NAME = "virus_name";
    public static final String CACHE_TIMESTAMP = "timestamp";





}