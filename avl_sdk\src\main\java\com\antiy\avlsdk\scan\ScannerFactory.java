package com.antiy.avlsdk.scan;

import com.antiy.avlsdk.callback.IScanner;
import com.antiy.avlsdk.callback.ScanListener;
import com.antiy.avlsdk.entity.ScanMode;

import java.io.File;
import java.util.List;

public class ScannerFactory {
    public static IScanner createScanner(ScanMode mode, List<File> files, ScanListener callback) {
        switch (mode) {
            case CLOUD:
                return new CloudScanner(files, callback,true);
            case LOCAL:
                return new LocalScanner(files, callback);
            default:
                throw new IllegalArgumentException("Unsupported scan mode: " + mode);
        }
    }
}
