<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F5F5F5"
    android:fitsSystemWindows="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingBottom="16dp">

        <!-- 顶部标题栏 -->
        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:text="安全卫士"
            android:textColor="#000000"
            android:textSize="24sp"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- 通知图标 -->
        <ImageView
            android:id="@+id/ivNotification"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginEnd="16dp"
            android:src="@drawable/ic_notification"
            app:layout_constraintBottom_toBottomOf="@+id/tvTitle"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tvTitle" />

        <!-- 设备安全状态卡片 -->
        <androidx.cardview.widget.CardView
            android:id="@+id/cardSecurity"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvTitle">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="16dp">

                <!-- 安全评分圆环 -->
                <com.google.android.material.progressindicator.CircularProgressIndicator
                    android:id="@+id/progressSecurity"
                    android:layout_width="120dp"
                    android:layout_height="120dp"
                    android:progress="95"
                    app:indicatorColor="#4285F4"
                    app:indicatorSize="120dp"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:trackColor="#E0E0E0"
                    app:trackThickness="8dp" />

                <!-- 安全评分数字 -->
                <TextView
                    android:id="@+id/tvScore"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="95"
                    android:visibility="gone"
                    android:textColor="#4285F4"
                    android:textSize="36sp"
                    android:textStyle="bold"
                    app:layout_constraintBottom_toBottomOf="@+id/progressSecurity"
                    app:layout_constraintEnd_toEndOf="@+id/progressSecurity"
                    app:layout_constraintStart_toStartOf="@+id/progressSecurity"
                    app:layout_constraintTop_toTopOf="@+id/progressSecurity" />

                <!-- 设备安全状态文本 -->
                <TextView
                    android:id="@+id/tvSecurityTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="24dp"
                    android:text="设备安全"
                    android:textColor="#000000"
                    android:textSize="18sp"
                    app:layout_constraintStart_toEndOf="@+id/progressSecurity"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tvSecurityStatus"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="您的设备处于良好状态"
                    android:textColor="#757575"
                    android:textSize="14sp"
                    app:layout_constraintStart_toStartOf="@+id/tvSecurityTitle"
                    app:layout_constraintTop_toBottomOf="@+id/tvSecurityTitle" />

                <!-- 立即扫描按钮 -->
                <Button
                    android:id="@+id/btnScan"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:backgroundTint="#4285F4"
                    android:text="立即扫描"
                    android:textColor="#FFFFFF"
                    app:layout_constraintStart_toStartOf="@+id/tvSecurityStatus"
                    app:layout_constraintTop_toBottomOf="@+id/tvSecurityStatus" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.cardview.widget.CardView>

        <!-- 快捷功能标题 -->
        <TextView
            android:id="@+id/tvQuickFunctions"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="24dp"
            android:text="快捷功能"
            android:textColor="#000000"
            android:textSize="18sp"
            android:textStyle="bold"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/cardSecurity" />

        <!-- 快捷功能网格 - 第一行 -->
        <androidx.cardview.widget.CardView
            android:id="@+id/cardDeepScan"
            style="@style/QuickFunctionCard"
            android:visibility="gone"
            app:layout_constraintEnd_toStartOf="@+id/cardCleanup"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvQuickFunctions">

            <LinearLayout
                style="@style/QuickFunctionLayout">

                <ImageView
                    style="@style/QuickFunctionIcon"
                    android:src="@drawable/ic_deep_scan" />

                <TextView
                    style="@style/QuickFunctionText"
                    android:text="深度扫描" />
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            android:id="@+id/cardCleanup"
            style="@style/QuickFunctionCard"
            android:visibility="gone"
            app:layout_constraintEnd_toStartOf="@+id/cardPrivacy"
            app:layout_constraintStart_toEndOf="@+id/cardDeepScan"
            app:layout_constraintTop_toBottomOf="@+id/tvQuickFunctions">

            <LinearLayout
                style="@style/QuickFunctionLayout">

                <ImageView
                    style="@style/QuickFunctionIcon"
                    android:src="@drawable/ic_cleanup" />

                <TextView
                    style="@style/QuickFunctionText"
                    android:text="垃圾清理" />
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            android:id="@+id/cardPrivacy"
            style="@style/QuickFunctionCard"
            android:visibility="gone"
            app:layout_constraintEnd_toStartOf="@+id/cardPerformance"
            app:layout_constraintStart_toEndOf="@+id/cardCleanup"
            app:layout_constraintTop_toBottomOf="@+id/tvQuickFunctions">

            <LinearLayout
                style="@style/QuickFunctionLayout">

                <ImageView
                    style="@style/QuickFunctionIcon"
                    android:src="@drawable/ic_privacy" />

                <TextView
                    style="@style/QuickFunctionText"
                    android:text="隐私保护" />
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            android:id="@+id/cardPerformance"
            style="@style/QuickFunctionCard"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/cardPrivacy"
            app:layout_constraintTop_toBottomOf="@+id/tvQuickFunctions">

            <LinearLayout
                style="@style/QuickFunctionLayout">

                <ImageView
                    style="@style/QuickFunctionIcon"
                    android:src="@drawable/ic_performance" />

                <TextView
                    style="@style/QuickFunctionText"
                    android:text="性能优化" />
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- 快捷功能网格 - 第二行 -->
        <androidx.cardview.widget.CardView
            android:id="@+id/cardAppManage"
            android:visibility="gone"
            style="@style/QuickFunctionCard"
            app:layout_constraintEnd_toStartOf="@+id/cardNetworkCheck"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/cardDeepScan">

            <LinearLayout
                style="@style/QuickFunctionLayout">

                <ImageView
                    style="@style/QuickFunctionIcon"
                    android:src="@drawable/ic_app_manage" />

                <TextView
                    style="@style/QuickFunctionText"
                    android:text="应用管理" />
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            android:id="@+id/cardNetworkCheck"
            style="@style/QuickFunctionCard"
            android:visibility="gone"
            app:layout_constraintEnd_toStartOf="@+id/cardRealTimeProtection"
            app:layout_constraintStart_toEndOf="@+id/cardAppManage"
            app:layout_constraintTop_toBottomOf="@+id/cardCleanup">

            <LinearLayout
                style="@style/QuickFunctionLayout">

                <ImageView
                    style="@style/QuickFunctionIcon"
                    android:src="@drawable/ic_network" />

                <TextView
                    style="@style/QuickFunctionText"
                    android:text="网络检测" />
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            android:id="@+id/cardRealTimeProtection"
            style="@style/QuickFunctionCard"
            android:visibility="gone"
            app:layout_constraintEnd_toStartOf="@+id/cardMore"
            app:layout_constraintStart_toEndOf="@+id/cardNetworkCheck"
            app:layout_constraintTop_toBottomOf="@+id/cardPrivacy">

            <LinearLayout
                style="@style/QuickFunctionLayout">

                <ImageView
                    style="@style/QuickFunctionIcon"
                    android:src="@drawable/ic_realtime_protection" />

                <TextView
                    style="@style/QuickFunctionText"
                    android:text="旧版本" />
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            android:id="@+id/cardMore"
            style="@style/QuickFunctionCard"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/cardRealTimeProtection"
            app:layout_constraintTop_toBottomOf="@+id/cardPerformance">

            <LinearLayout
                style="@style/QuickFunctionLayout">

                <ImageView
                    style="@style/QuickFunctionIcon"
                    android:src="@drawable/ic_cleanup" />

                <TextView
                    style="@style/QuickFunctionText"
                    android:text="清除缓存" />
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- 最近活动标题 -->
        <TextView
            android:id="@+id/tvSDKStatus"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="24dp"
            android:text="SDK状态"
            android:textColor="#000000"
            android:textSize="18sp"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/cardAppManage" />

        <androidx.cardview.widget.CardView
            android:id="@+id/card_sdk_status"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:layout_marginTop="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvSDKStatus"
            >
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="16dp"
                >
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="SDK初始化："
                    android:textColor="#000000"
                    android:textSize="16sp"
                    android:layout_alignParentStart="true"
                />
                <TextView
                    android:id="@+id/tv_sdk_status"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="成功"
                    android:layout_alignParentEnd="true"
                    />
            </RelativeLayout>

        </androidx.cardview.widget.CardView>

        <!-- 最近活动标题 -->
        <TextView
            android:id="@+id/tvRecentActivities"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="24dp"
            android:text="最近活动"
            android:textColor="#000000"
            android:textSize="18sp"
            android:textStyle="bold"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/card_sdk_status" />

        <!-- 最近活动列表 -->
        <androidx.cardview.widget.CardView
            android:id="@+id/cardRecentActivities"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp"
            android:visibility="gone"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvRecentActivities">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- 系统扫描完成 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:padding="16dp">

                    <ImageView
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:background="@drawable/circle_background_blue"
                        android:padding="6dp"
                        android:src="@drawable/ic_check" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="系统扫描完成"
                            android:textColor="#000000"
                            android:textSize="16sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="未发现威胁，您的设备安全"
                            android:textColor="#757575"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="今天 10:30"
                        android:textColor="#757575"
                        android:textSize="12sp" />
                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="#E0E0E0" />

                <!-- 病毒库已更新 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:padding="16dp">

                    <ImageView
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:background="@drawable/circle_background_blue"
                        android:padding="6dp"
                        android:src="@drawable/ic_refresh" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="病毒库已更新"
                            android:textColor="#000000"
                            android:textSize="16sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="已更新至最新版本 V2023.06.15"
                            android:textColor="#757575"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="昨天 15:45"
                        android:textColor="#757575"
                        android:textSize="12sp" />
                </LinearLayout>

            </LinearLayout>
        </androidx.cardview.widget.CardView>

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.core.widget.NestedScrollView> 