package com.antiy.avlsdk.scan;

import com.jaredrummler.apkparser.BuildConfig;

/**
 * 扫描结果配置
 * 当aar的变体是changan的时候，在扫描结果里面是无需返回sha256sum字段，长安反应sdk扫描IO占比过高
 * 减少sha256计算，避免sdk扫描IO占比过高
 */
public class ScanResultConfig {
    private static final String FLAVOR = "changan";
    /**
     * 是否应该计算并返回sha256sum
     */
    public static boolean shouldCalculateSha256() {
        return BuildConfig.FLAVOR.equals(FLAVOR);
    }
}
