package com.antiy.demo

import com.antiy.demo.utils.VinUtil
import org.junit.Test

/**
 * Example local unit test, which will execute on the development machine (host).
 *
 * See [testing documentation](http://d.android.com/tools/testing).
 */
class ExampleUnitTest {
    @Test
    fun getCertHash() {
    }

    /*@Test
    fun testVin() {
        val vinUUID = VinUtil.vinToUuidHash("LGWURUOUKPE046654")
        println(vinUUID)
    }*/

    @Test
    fun testVin1() {
        val uuid = VinUtil.vinToUuid("LGWURUOUKPE046654")
        println(uuid)
    }
}