package com.antiy.demo

import android.content.Context
import android.net.ConnectivityManager
import com.antiy.avlsdk.AVLEngine
import com.antiy.avlsdk.callback.DownloadCallback
import com.antiy.avlsdk.callback.NetworkTunnel
import com.antiy.avlsdk.callback.RequestCallback
import com.antiy.avlsdk.entity.RequestMethod
import okhttp3.Call
import okhttp3.Callback
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody
import okhttp3.Response
import okhttp3.logging.HttpLoggingInterceptor
import java.io.File
import java.io.IOException


/**
 * Author: wangbiao
 * Date: 2024/9/26 16:39
 * Description:
 */
open class NetworkManager : NetworkTunnel {
    private val DOWNLOAD_BARS_URL: String = "https://the-xiaomi.oss-cn-beijing.aliyuncs.com/";
    private val BASE_URL: String = "http://yx.avlsec.com"
    companion object {
        const val CONTENT_TYPE = "application/json;charset=UTF-8"
    }
    private val okHttpClient by lazy {
        OkHttpClient.Builder()
            .addInterceptor(HttpLoggingInterceptor(CustomLogger()).apply {
                this.level = HttpLoggingInterceptor.Level.BODY
            })
            .build()
    }
    private var currentCall: Call? = null
    private var downloadCall: Call? = null

    override fun isAvailable(): Boolean {
        val connectivityManager = AVLEngine.getInstance().context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val activeNetworkInfo = connectivityManager.activeNetworkInfo
        return activeNetworkInfo != null && activeNetworkInfo.isConnected
    }

    override fun request(
        uri: String?,
        method: RequestMethod?,
        param: Any?,
        callback: RequestCallback?
    ) {
        val contentType = CONTENT_TYPE.toMediaTypeOrNull()
        val body = RequestBody.create(contentType, param.toString())
        val request = Request.Builder()
            .url(BASE_URL + uri!!)
            .post(body)
            .build()
        currentCall = okHttpClient.newCall(request)
        currentCall?.enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                if (!call.isCanceled()) {
                    callback?.onError(e.message)
                }
            }

            override fun onResponse(call: Call, response: Response) {
                if (!call.isCanceled()) {
                    response.use {
                        if (response.code == 200){
                            callback?.onFinish(response.code, response.body?.string())
                        }else {
                            callback?.onError(response.body?.string())
                        }
                    }
                }
            }
        })
    }

    override fun download(uri: String?, callback: DownloadCallback?) {
        val request = Request.Builder().url(DOWNLOAD_BARS_URL + uri!!).build()
        downloadCall = okHttpClient.newCall(request)
        downloadCall?.enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                if (!call.isCanceled()) {
                    callback?.onError(e.message)
                }
            }

            override fun onResponse(call: Call, response: Response) {
                if (!call.isCanceled()) {
                    DownloadManager.download(uri, response, callback)
                }
            }
        })
    }

    override fun cancelRequest() {
        currentCall?.cancel()
        downloadCall?.cancel()
        currentCall = null
        downloadCall = null
    }
}

object DownloadManager {
    /**
     * @param uri 下载地址
     * @param response 响应
     * @param callback 下载回调
     */
    fun download(uri : String,response: Response,callback: DownloadCallback?){
        AVLEngine.Logger.info("download url : $uri")
        response.body?.byteStream()?.use { inputStream ->
            // 假设保存到本地的逻辑
            val fileName = uri.substring(uri.lastIndexOf("/") + 1)
            val file = File("/sdcard/Download/$fileName")
            file.outputStream().use { outputStream ->
                inputStream.copyTo(outputStream)
            }
            callback?.onFinish(200, fileName,file.absolutePath)
        }
    }
}