package com.antiy.avlsdk.update;

import static com.antiy.avlsdk.utils.SdkConst.AVL_PC_BACKUP_SUFFIX;
import static com.antiy.avlsdk.utils.SdkConst.AVL_PC_LICENSE;

import android.os.Build;
import android.util.Pair;

import com.antiy.avlsdk.AVLEngine;
import com.antiy.avlsdk.pc.AVLEnginePC;
import com.antiy.avlsdk.utils.ZipExtractor;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;

/**
 * Author: wangbiao
 * Date: 2024/10/11 17:08
 * Description:
 */
public class PCFullUpdateStrategy extends UpdateStrategy {


    @Override
    public boolean performUpdate(String packagePath, Pair<UpdateTypeEnum, UpdateTypeEnum> pair) {
        String pcEngineDirectory = AVLEnginePC.getInstance().getAVLPCPath();
        AVLEngine.Logger.error("pcEngineDirectory:" + pcEngineDirectory);
        AVLEngine.Logger.error("Update the virus database version before updating:" + AVLEnginePC.getInstance().getDbInfo());
        AVLEnginePC.getInstance().unloadEngine();

        // 1.重新命名avl_pc为avl_pc.bak
        renameAvlPcDir();

        // 2. 解压PC病毒库压缩包
        AVLEngine.Logger.error("The PC directory exist:" + new File(pcEngineDirectory).exists());
        AVLEngine.Logger.error("Start decompressing PC virus library");
        ZipExtractor extractor = new ZipExtractor();
        extractor.extractZipPackage(packagePath, pcEngineDirectory);
        AVLEngine.Logger.error("Successfully decompressed PC virus database");

        // 拷贝license文件
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            try {
                Files.copy(Paths.get(pcEngineDirectory + AVL_PC_BACKUP_SUFFIX + AVL_PC_LICENSE),
                        Paths.get(pcEngineDirectory + AVL_PC_LICENSE), StandardCopyOption.REPLACE_EXISTING);
            } catch (Exception e) {
                e.printStackTrace();
                return false;
            }
        }

        // 检查pc的更新库是否OK
        boolean pcResult = pcVirusUpdateCheck();
        if (!pcResult) {
            AVLEngine.Logger.info("Failed to update PC Full package through binary detection");
//            resetPcEngine();
            return false;
        }

        // 初始化引擎
        boolean result = (AVLEnginePC.getInstance().init() == 0);
        if (result) {
            AVLEngine.Logger.info("init success");
            AVLEngine.Logger.info("update pc virus database version ：" + AVLEnginePC.getInstance().getDbInfo());

            // 更新成功后删除临时文件
//            cleanupTempFiles();
        }
        /*else {
            // 回滚
            resetPcEngine();
        }*/
        return result;
    }

    /**
     * 重新命名avl_pc的名字为avl_pc.bak
     */
    public void renameAvlPcDir() {
        File rename = new File(AVLEnginePC.getInstance().getAVLPCPath());
        rename.renameTo(new File(AVLEnginePC.getInstance().getAVLPCPath() + AVL_PC_BACKUP_SUFFIX));
    }


}
