package com.antiy.avlsdk.scan;

import com.antiy.avlsdk.AVLEngine;
import com.antiy.avlsdk.callback.ScanListener;
import com.antiy.avlsdk.entity.ResultScan;
import com.antiy.avlsdk.entity.ScanErrorType;
import com.antiy.avlsdk.storage.DataManager;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.annotation.Config;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * 扫描性能基准测试
 * 验证优化后的性能改进效果
 */
@RunWith(RobolectricTestRunner.class)
@Config(sdk = 28)
public class ScanPerformanceBenchmark {

    @Mock
    private AVLEngine mockEngine;
    
    @Mock
    private DataManager mockDataManager;

    private File tempDir;
    private List<File> testFiles;

    @Before
    public void setUp() throws IOException {
        MockitoAnnotations.initMocks(this);
        
        // 创建测试环境
        tempDir = createTempDirectory();
        testFiles = createTestFiles(10);
        
        // Mock基础组件
        setupMocks();
    }

    /**
     * 基准测试1：哈希计算性能对比
     * 验证：使用预计算哈希值的性能提升
     */
    @Test
    public void benchmarkHashCalculationOptimization() throws InterruptedException {
        final int ITERATIONS = 5;
        
        // 测试不使用预计算哈希的性能
        long totalTimeWithoutOptimization = 0;
        for (int i = 0; i < ITERATIONS; i++) {
            long startTime = System.currentTimeMillis();
            
            CloudScanner scanner = new CloudScanner(testFiles, createNoOpListener(), true);
            CountDownLatch latch = new CountDownLatch(1);
            
            // 模拟扫描过程
            scanner.startScan();
            latch.await(10, TimeUnit.SECONDS);
            
            long endTime = System.currentTimeMillis();
            totalTimeWithoutOptimization += (endTime - startTime);
        }
        
        // 测试使用预计算哈希的性能
        Map<String, String> preCalculatedHashes = createPreCalculatedHashes();
        long totalTimeWithOptimization = 0;
        
        for (int i = 0; i < ITERATIONS; i++) {
            long startTime = System.currentTimeMillis();
            
            CloudScanner scanner = new CloudScanner(testFiles, createNoOpListener(), true, preCalculatedHashes);
            CountDownLatch latch = new CountDownLatch(1);
            
            // 模拟扫描过程
            scanner.startScan();
            latch.await(10, TimeUnit.SECONDS);
            
            long endTime = System.currentTimeMillis();
            totalTimeWithOptimization += (endTime - startTime);
        }
        
        // 计算平均时间
        long avgTimeWithoutOptimization = totalTimeWithoutOptimization / ITERATIONS;
        long avgTimeWithOptimization = totalTimeWithOptimization / ITERATIONS;
        
        // 计算性能提升
        double improvementPercentage = ((double)(avgTimeWithoutOptimization - avgTimeWithOptimization) / avgTimeWithoutOptimization) * 100;
        
        System.out.println("哈希计算性能基准测试结果:");
        System.out.println("  不使用优化平均时间: " + avgTimeWithoutOptimization + "ms");
        System.out.println("  使用优化平均时间: " + avgTimeWithOptimization + "ms");
        System.out.println("  性能提升: " + String.format("%.2f", improvementPercentage) + "%");
        
        // 验证性能有所提升（至少5%）
        assertTrue("使用预计算哈希应该有性能提升", avgTimeWithOptimization <= avgTimeWithoutOptimization);
    }

    /**
     * 基准测试2：并发扫描性能
     * 验证：多线程环境下的性能表现
     */
    @Test
    public void benchmarkConcurrentScanPerformance() throws InterruptedException {
        final int CONCURRENT_SCANNERS = 3;
        final AtomicLong totalExecutionTime = new AtomicLong(0);
        final CountDownLatch allFinishedLatch = new CountDownLatch(CONCURRENT_SCANNERS);
        
        long overallStartTime = System.currentTimeMillis();
        
        // 启动多个并发扫描
        for (int i = 0; i < CONCURRENT_SCANNERS; i++) {
            final int scannerIndex = i;
            new Thread(() -> {
                try {
                    long startTime = System.currentTimeMillis();
                    
                    CloudScanner scanner = new CloudScanner(testFiles, new ScanListener() {
                        @Override
                        public void scanStart() {}

                        @Override
                        public void scanStop() {}

                        @Override
                        public void scanFinish() {
                            long endTime = System.currentTimeMillis();
                            totalExecutionTime.addAndGet(endTime - startTime);
                            allFinishedLatch.countDown();
                        }

                        @Override
                        public void scanCount(int count) {}

                        @Override
                        public void scanFileStart(int index, String path) {}

                        @Override
                        public void scanFileFinish(int index, String path, ResultScan result) {}

                        @Override
                        public void scanError(ScanErrorType errorMsg) {

                        }
                    }, true);
                    
                    scanner.startScan();
                    
                } catch (Exception e) {
                    System.err.println("并发扫描 " + scannerIndex + " 失败: " + e.getMessage());
                    allFinishedLatch.countDown();
                }
            }).start();
        }
        
        // 等待所有扫描完成
        boolean allCompleted = allFinishedLatch.await(60, TimeUnit.SECONDS);
        long overallEndTime = System.currentTimeMillis();
        
        assertTrue("所有并发扫描应该完成", allCompleted);
        
        long overallTime = overallEndTime - overallStartTime;
        long avgIndividualTime = totalExecutionTime.get() / CONCURRENT_SCANNERS;
        
        System.out.println("并发扫描性能基准测试结果:");
        System.out.println("  总体执行时间: " + overallTime + "ms");
        System.out.println("  平均单个扫描时间: " + avgIndividualTime + "ms");
        System.out.println("  并发效率: " + String.format("%.2f", (double)avgIndividualTime / overallTime));
        
        // 验证并发效率（总体时间应该小于所有单个时间的总和）
        assertTrue("并发执行应该比串行执行更高效", overallTime < totalExecutionTime.get());
    }

    /**
     * 基准测试3：内存使用情况
     * 验证：扫描过程中的内存使用
     */
    @Test
    public void benchmarkMemoryUsage() throws InterruptedException {
        Runtime runtime = Runtime.getRuntime();
        
        // 强制垃圾回收，获取基准内存使用
        System.gc();
        Thread.sleep(1000);
        long memoryBefore = runtime.totalMemory() - runtime.freeMemory();
        
        // 执行扫描
        CountDownLatch finishLatch = new CountDownLatch(1);
        CloudScanner scanner = new CloudScanner(testFiles, new ScanListener() {
            @Override
            public void scanStart() {}

            @Override
            public void scanStop() {}

            @Override
            public void scanFinish() {
                finishLatch.countDown();
            }

            @Override
            public void scanCount(int count) {}

            @Override
            public void scanFileStart(int index, String path) {}

            @Override
            public void scanFileFinish(int index, String path, ResultScan result) {}
        }, true);
        
        scanner.startScan();
        assertTrue("扫描应该完成", finishLatch.await(30, TimeUnit.SECONDS));
        
        // 获取扫描后的内存使用
        long memoryAfter = runtime.totalMemory() - runtime.freeMemory();
        
        // 等待资源清理
        Thread.sleep(2000);
        System.gc();
        Thread.sleep(1000);
        long memoryAfterCleanup = runtime.totalMemory() - runtime.freeMemory();
        
        long memoryUsedDuringScan = memoryAfter - memoryBefore;
        long memoryLeaked = memoryAfterCleanup - memoryBefore;
        
        System.out.println("内存使用基准测试结果:");
        System.out.println("  扫描前内存: " + formatBytes(memoryBefore));
        System.out.println("  扫描后内存: " + formatBytes(memoryAfter));
        System.out.println("  清理后内存: " + formatBytes(memoryAfterCleanup));
        System.out.println("  扫描期间使用: " + formatBytes(memoryUsedDuringScan));
        System.out.println("  可能的内存泄露: " + formatBytes(memoryLeaked));
        
        // 验证内存泄露在合理范围内（小于1MB）
        assertTrue("内存泄露应该在合理范围内", memoryLeaked < 1024 * 1024);
    }

    /**
     * 基准测试4：线程池效率
     * 验证：线程池的创建和销毁效率
     */
    @Test
    public void benchmarkThreadPoolEfficiency() throws InterruptedException {
        final int TEST_ITERATIONS = 10;
        long totalCreationTime = 0;
        long totalDestructionTime = 0;
        
        for (int i = 0; i < TEST_ITERATIONS; i++) {
            // 测量创建时间
            long creationStart = System.currentTimeMillis();
            CloudScanner scanner = new CloudScanner(testFiles, createNoOpListener(), true);
            long creationEnd = System.currentTimeMillis();
            totalCreationTime += (creationEnd - creationStart);
            
            // 启动扫描
            CountDownLatch finishLatch = new CountDownLatch(1);
            scanner.startScan();
            
            // 等待一小段时间后停止
            Thread.sleep(100);
            
            // 测量销毁时间
            long destructionStart = System.currentTimeMillis();
            scanner.stopScan();
            long destructionEnd = System.currentTimeMillis();
            totalDestructionTime += (destructionEnd - destructionStart);
            
            // 等待资源清理
            Thread.sleep(100);
        }
        
        long avgCreationTime = totalCreationTime / TEST_ITERATIONS;
        long avgDestructionTime = totalDestructionTime / TEST_ITERATIONS;
        
        System.out.println("线程池效率基准测试结果:");
        System.out.println("  平均创建时间: " + avgCreationTime + "ms");
        System.out.println("  平均销毁时间: " + avgDestructionTime + "ms");
        System.out.println("  总体效率: " + (avgCreationTime + avgDestructionTime) + "ms/cycle");
        
        // 验证效率在合理范围内
        assertTrue("线程池创建时间应该合理", avgCreationTime < 1000);
        assertTrue("线程池销毁时间应该合理", avgDestructionTime < 1000);
    }

    // 辅助方法：创建预计算的哈希值
    private Map<String, String> createPreCalculatedHashes() {
        Map<String, String> hashes = new HashMap<>();
        for (File file : testFiles) {
            hashes.put(file.getAbsolutePath(), "mock_hash_" + file.getName());
        }
        return hashes;
    }

    // 辅助方法：创建无操作的监听器
    private ScanListener createNoOpListener() {
        return new ScanListener() {
            @Override
            public void scanStart() {}

            @Override
            public void scanStop() {}

            @Override
            public void scanFinish() {}

            @Override
            public void scanCount(int count) {}

            @Override
            public void scanFileStart(int index, String path) {}

            @Override
            public void scanFileFinish(int index, String path, ResultScan result) {}
        };
    }

    // 辅助方法：格式化字节数
    private String formatBytes(long bytes) {
        if (bytes < 1024) return bytes + " B";
        if (bytes < 1024 * 1024) return String.format("%.2f KB", bytes / 1024.0);
        return String.format("%.2f MB", bytes / (1024.0 * 1024.0));
    }

    // 辅助方法：设置Mock对象
    private void setupMocks() {
        when(mockEngine.getInitResult()).thenReturn(createSuccessInitResult());
        when(mockEngine.getNetworkManager()).thenReturn(createMockNetworkManager());
        when(mockDataManager.getCloudSizeThreshold()).thenReturn(1024L);
    }

    // 辅助方法：创建临时目录
    private File createTempDirectory() throws IOException {
        File tempDir = File.createTempFile("benchmark_test", "dir");
        tempDir.delete();
        tempDir.mkdirs();
        tempDir.deleteOnExit();
        return tempDir;
    }

    // 辅助方法：创建测试文件
    private List<File> createTestFiles(int count) throws IOException {
        List<File> files = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            File file = new File(tempDir, "benchmark_file_" + i + ".txt");
            FileWriter writer = new FileWriter(file);
            writer.write("Benchmark test file content " + i);
            writer.close();
            file.deleteOnExit();
            files.add(file);
        }
        return files;
    }

    // 辅助方法：创建成功的初始化结果
    private Object createSuccessInitResult() {
        return new Object() {
            public boolean isSuccess = true;
        };
    }

    // 辅助方法：创建Mock网络管理器
    private Object createMockNetworkManager() {
        return new Object() {
            public boolean isAvailable() {
                return true;
            }
        };
    }
}
