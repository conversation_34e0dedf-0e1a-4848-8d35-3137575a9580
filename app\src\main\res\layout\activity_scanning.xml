<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <TextView
        android:id="@+id/tvScanType"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="快速扫描"
        android:textColor="@color/black"
        android:textSize="24sp"
        android:textStyle="bold"
        android:layout_marginTop="20dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.google.android.material.progressindicator.CircularProgressIndicator
        android:id="@+id/progressCircular"
        android:layout_width="200dp"
        android:layout_height="200dp"
        android:progress="0"
        app:indicatorColor="@color/primary"
        app:indicatorSize="200dp"
        android:layout_marginTop="10dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvScanType"
        app:layout_constraintVertical_bias="0.2"
        app:trackColor="#E0E0E0"
        app:trackThickness="8dp" />

    <TextView
        android:id="@+id/tvProgress"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="0%"
        android:textColor="@color/primary"
        android:textSize="40sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/progressCircular"
        app:layout_constraintEnd_toEndOf="@id/progressCircular"
        app:layout_constraintStart_toStartOf="@id/progressCircular"
        app:layout_constraintTop_toTopOf="@id/progressCircular" />

    <TextView
        android:id="@+id/tvScanStatus"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="正在扫描"
        android:textColor="@color/gray"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="@id/progressCircular"
        app:layout_constraintStart_toStartOf="@id/progressCircular"
        app:layout_constraintTop_toBottomOf="@id/tvProgress" />

    <TextView
        android:id="@+id/tvTimeRemaining"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:text="预计剩余时间: 计算中..."
        android:textColor="@color/gray"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/progressCircular" />

    <TextView
        android:id="@+id/tvCurrentFile"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:ellipsize="middle"
        android:gravity="center"
        android:singleLine="true"
        android:text="准备扫描..."
        android:textColor="@color/gray"
        app:layout_constraintTop_toBottomOf="@id/tvTimeRemaining" />

    <LinearLayout
        android:id="@+id/layoutStats"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:orientation="horizontal"
        app:layout_constraintTop_toBottomOf="@id/tvCurrentFile">

        <TextView
            android:id="@+id/tvScannedFiles"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="已扫描文件\n0" />

        <TextView
            android:id="@+id/tvThreats"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="发现威胁\n0"
            android:textColor="@color/danger_red" />

        <TextView
            android:id="@+id/tvTime"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="耗时\n0:00" />
    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvScanResults"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="16dp"
        app:layout_constraintBottom_toTopOf="@id/layoutButtons"
        app:layout_constraintTop_toBottomOf="@id/layoutStats" />

    <LinearLayout
        android:id="@+id/layoutButtons"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent">

        <Button
            android:id="@+id/btnPause"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:layout_weight="1"
            android:text="暂停"
            app:cornerRadius="24dp" />

        <Button
            android:id="@+id/btnStop"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="8dp"
            android:layout_weight="1"
            android:text="停止"
            app:cornerRadius="24dp" />
            
        <Button
            android:id="@+id/btnReport"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_weight="1"
            android:text="扫描报告"
            android:visibility="gone"
            android:backgroundTint="@color/success_green"
            app:cornerRadius="24dp" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>