apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'

android {
    namespace 'com.antiy.avlsdk.monitor'
    compileSdk 31
    ndkVersion "27.0.12077973"

    defaultConfig {
        minSdk 21

        consumerProguardFiles "consumer-rules.pro"

        sourceSets {
            main {
                jniLibs.srcDir(['libs'])
            }
        }

        ndk {
            abiFilters "arm64-v8a"
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    sourceSets {
        main {
            assets.srcDirs = ['src/main/assets']
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    externalNativeBuild {
        cmake {
            path file('CMakeLists.txt')
        }
    }

}

dependencies {
}