package com.antiy.avlsdk.license;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;

import com.antiy.avlsdk.AVLEngine;

/**
 * 网络状态检查器
 * 负责检查网络连接状态和网络稳定性
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class NetworkChecker {
    
    private final Context context;
    
    /**
     * 构造函数
     * 
     * @param context 应用上下文
     */
    public NetworkChecker(Context context) {
        this.context = context.getApplicationContext();
    }
    
    /**
     * 检查网络是否可用
     * 
     * @return true表示网络可用，false表示网络不可用
     */
    public boolean isNetworkAvailable() {
        try {
            ConnectivityManager cm = (ConnectivityManager) context
                    .getSystemService(Context.CONNECTIVITY_SERVICE);
            
            if (cm == null) {
                return false;
            }
            
            NetworkInfo activeNetwork = cm.getActiveNetworkInfo();
            boolean isAvailable = activeNetwork != null && activeNetwork.isConnectedOrConnecting();
            
            if (AVLEngine.Logger != null) {
                AVLEngine.Logger.info("网络状态检查: " + (isAvailable ? "可用" : "不可用"));
            }
            
            return isAvailable;
        } catch (Exception e) {
            if (AVLEngine.Logger != null) {
                AVLEngine.Logger.error("检查网络状态失败: " + e.getMessage());
            }
            return false;
        }
    }
    
    /**
     * 检查网络是否稳定
     * 通过检查网络类型和连接状态来判断网络稳定性
     * 
     * @return true表示网络稳定，false表示网络不稳定
     */
    public boolean isNetworkStable() {
        try {
            ConnectivityManager cm = (ConnectivityManager) context
                    .getSystemService(Context.CONNECTIVITY_SERVICE);
            
            if (cm == null) {
                return false;
            }
            
            NetworkInfo activeNetwork = cm.getActiveNetworkInfo();
            if (activeNetwork == null || !activeNetwork.isConnected()) {
                return false;
            }
            
            // 检查网络类型，WiFi通常比移动网络更稳定
            boolean isStable = activeNetwork.getType() == ConnectivityManager.TYPE_WIFI ||
                              activeNetwork.getType() == ConnectivityManager.TYPE_ETHERNET;
            
            if (AVLEngine.Logger != null) {
                String networkType = getNetworkTypeName(activeNetwork.getType());
                AVLEngine.Logger.info("网络稳定性检查: 类型=" + networkType + 
                                      ", 稳定=" + (isStable ? "是" : "否"));
            }
            
            return isStable;
        } catch (Exception e) {
            if (AVLEngine.Logger != null) {
                AVLEngine.Logger.error("检查网络稳定性失败: " + e.getMessage());
            }
            return false;
        }
    }
    
    /**
     * 获取当前网络类型名称
     * 
     * @return 网络类型的描述字符串
     */
    public String getCurrentNetworkType() {
        try {
            ConnectivityManager cm = (ConnectivityManager) context
                    .getSystemService(Context.CONNECTIVITY_SERVICE);
            
            if (cm == null) {
                return "未知";
            }
            
            NetworkInfo activeNetwork = cm.getActiveNetworkInfo();
            if (activeNetwork == null) {
                return "无网络";
            }
            
            return getNetworkTypeName(activeNetwork.getType());
        } catch (Exception e) {
            return "检查失败";
        }
    }
    
    /**
     * 根据网络类型常量获取网络类型名称
     * 
     * @param networkType 网络类型常量
     * @return 网络类型名称
     */
    private String getNetworkTypeName(int networkType) {
        switch (networkType) {
            case ConnectivityManager.TYPE_WIFI:
                return "WiFi";
            case ConnectivityManager.TYPE_MOBILE:
                return "移动网络";
            case ConnectivityManager.TYPE_ETHERNET:
                return "以太网";
            case ConnectivityManager.TYPE_BLUETOOTH:
                return "蓝牙";
            default:
                return "其他(" + networkType + ")";
        }
    }
    
    /**
     * 等待网络可用
     * 在指定时间内等待网络变为可用状态
     * 
     * @param timeoutMs 超时时间（毫秒）
     * @param checkIntervalMs 检查间隔（毫秒）
     * @return true表示网络在超时时间内变为可用，false表示超时
     */
    public boolean waitForNetworkAvailable(long timeoutMs, long checkIntervalMs) {
        long startTime = System.currentTimeMillis();
        
        while (System.currentTimeMillis() - startTime < timeoutMs) {
            if (isNetworkAvailable()) {
                return true;
            }
            
            try {
                Thread.sleep(checkIntervalMs);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return false;
            }
        }
        
        return false;
    }
}
