package com.antiy.avlsdk.update;

import android.util.Pair;

import com.antiy.avlsdk.AVLCoreEngine;
import com.antiy.avlsdk.AVLEngine;
import com.antiy.avlsdk.entity.ResultUpdate;
import com.antiy.avlsdk.entity.VirusUpdateEntity;
import com.antiy.avlsdk.pc.AVLEnginePC;
import com.antiy.avlsdk.utils.SdkConst;
import com.antiy.avlsdk.utils.ZipExtractor;

import java.io.IOException;
import java.util.List;

/**
 * Author: wangbiao
 * Date: 2024/10/11 17:10
 * Description:
 */
public class UpdateCoordinator {

    private UpdateStrategy strategy;

    /**
     * 处理下载的数据
     * @param entity 下载的更新实体
     * @param localFilePath 下载的更新包的本地地址
     * @param totalZipPath 下载的包的解压目标路径 /data/data/com.antiy.demo/files/update
     */
    public ResultUpdate handleUpdate(VirusUpdateEntity entity, String localFilePath, String totalZipPath) {
        Pair<UpdateTypeEnum, UpdateTypeEnum> pair = parseUpdateTypes(entity.getIstotal(),entity.getVersion());
        boolean isZipSuccess = extractPackages( totalZipPath , localFilePath);
        if (isZipSuccess) {
            // 如果zip解压是空，表示未解压成功，更新失败。
            AVLEngine.Logger.error("maybe virus lib download fail,unzip fail!");
            return new ResultUpdate(true,false);
        }
        String[] packagePaths = generatePath(entity,pair,totalZipPath);
        // todo 先更新PC
        boolean pcSuccess;
        if (pair.second == UpdateTypeEnum.NONE) {
            pcSuccess = true;
        }else {
            pcSuccess = updatePlatform(PlatformType.PC, pair.second, packagePaths[1],pair);
        }

        if (!pcSuccess) {
            AVLEngine.Logger.info("pc virus update fail.");
            return new ResultUpdate(true,false);
        }

        // todo 更新 android
        boolean androidSuccess;
        if (pair.first == UpdateTypeEnum.NONE) {
            androidSuccess = true;
        }else{
            androidSuccess = updatePlatform(PlatformType.ANDROID, pair.first, packagePaths[0],pair);
        }

        if (androidSuccess) {
            AVLEngine.Logger.info("Virus database updated successfully.");
            // 清空update目录和pc备份文件
            strategy.cleanupTempFiles();
            return new ResultUpdate(true,true);
        }

        return new ResultUpdate(true,false);
    }

    /**
     * 检测病毒库的包
     */
    private void checkVirusPackage() {
        try {
            Runtime.getRuntime().exec("");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 解压下载包
     * @param targetDir 解压目标文件夹
     * @param localFilePath 下载包位置
     * @return 如果返回true，表示解压失败，返回false表示解压成功
     */
    private boolean extractPackages(String targetDir,String localFilePath) {
        // 先解压
        ZipExtractor extractor = new ZipExtractor();
        List<String> zipPaths = extractor.extractZipPackage(localFilePath, targetDir);
        return zipPaths.isEmpty();
    }

    /**
     * 生成解压后的文件路径
     * @param entity
     * @param pair
     * @param totalZipPath
     * @return
     */
    public String[] generatePath(VirusUpdateEntity entity , Pair<UpdateTypeEnum,UpdateTypeEnum> pair,String totalZipPath) {
        String[] versions = entity.getVersion().split("_");
        String androidPath = "";
        String pcPath = "";
        if (pair.first == UpdateTypeEnum.FULL && pair.second == UpdateTypeEnum.FULL) {
            // 2_2
            androidPath = totalZipPath  + versions[0] + getSuffix(pair.first);
            pcPath = totalZipPath  + versions[1] + getSuffix(pair.second);

        } else if (pair.first == UpdateTypeEnum.FULL && pair.second == UpdateTypeEnum.INCREMENTAL) {
            // 2_1
            androidPath = totalZipPath + versions[0] + getSuffix(pair.first);
            pcPath = totalZipPath  + AVLEnginePC.getInstance().getDbInfo() + getSuffix(pair.second);
        } else if (pair.first == UpdateTypeEnum.FULL && pair.second == UpdateTypeEnum.NONE) {
            // 2_0
            androidPath = totalZipPath  + versions[0] + getSuffix(pair.first);
            pcPath = "";
        } else if (pair.first == UpdateTypeEnum.INCREMENTAL && pair.second == UpdateTypeEnum.NONE) {
            // 1_0
            androidPath = totalZipPath + AVLCoreEngine.getInstance().getSigLibVersion() + getSuffix(pair.first);
            pcPath = "";
        } else if (pair.first == UpdateTypeEnum.INCREMENTAL && pair.second == UpdateTypeEnum.INCREMENTAL) {
            // 1_1
            androidPath = totalZipPath + AVLCoreEngine.getInstance().getSigLibVersion() + getSuffix(pair.first);
            pcPath = totalZipPath + AVLEnginePC.getInstance().getDbInfo() + getSuffix(pair.second);
        } else if (pair.first == UpdateTypeEnum.INCREMENTAL && pair.second == UpdateTypeEnum.FULL) {
            // 1_2
            androidPath = totalZipPath + AVLCoreEngine.getInstance().getSigLibVersion() + getSuffix(pair.first);
            pcPath = totalZipPath  + versions[1] + getSuffix(pair.second);
        } else if (pair.first == UpdateTypeEnum.NONE && pair.second == UpdateTypeEnum.INCREMENTAL) {
            // 0_1
            androidPath = "";
            pcPath = totalZipPath + AVLEnginePC.getInstance().getDbInfo() + getSuffix(pair.second);
        } else if (pair.first == UpdateTypeEnum.NONE && pair.second == UpdateTypeEnum.FULL) {
            // 0_2
            androidPath = "";
            pcPath = totalZipPath + versions[1] + getSuffix(pair.second);
        }

        AVLEngine.Logger.error("zip path : " + androidPath + "," + pcPath);
        return new String[]{androidPath,pcPath};
    }

    private String getSuffix(UpdateTypeEnum type) {
        if (type == UpdateTypeEnum.FULL) {
            return SdkConst.ZIP_SUFFIX;
        } else if (type == UpdateTypeEnum.INCREMENTAL) {
            return SdkConst.TAR_SUFFIX;
        }
        return "";

    }

    /**
     * 解析服务端返回的isTotal和version
     * @param isTotal 格式为0_0 0_1 0_2 1_0 1_1 1_2 2_0 2_1 2_2
     * @param version 格式:20241009.bl_2024101016161200
     * @return
     */
    private Pair<UpdateTypeEnum, UpdateTypeEnum> parseUpdateTypes(String isTotal,String version) {
        String[] totalArray = isTotal.split("_");
        UpdateTypeEnum android = UpdateTypeEnum.fromCode(Integer.parseInt(totalArray[0]));
        UpdateTypeEnum pc = UpdateTypeEnum.fromCode(Integer.parseInt(totalArray[1]));
        AVLEngine.Logger.error("is_total: + " + android + "," + pc);
        Pair<UpdateTypeEnum,UpdateTypeEnum> pair = new Pair<>(android,pc);
        return pair;
    }

    private boolean updatePlatform(PlatformType platform, UpdateTypeEnum type, String packagePath,Pair<UpdateTypeEnum,UpdateTypeEnum> pair) {
        strategy = UpdateStrategyFactory.createStrategy(type, platform);
        return strategy.performUpdate(packagePath,pair);
    }
}
