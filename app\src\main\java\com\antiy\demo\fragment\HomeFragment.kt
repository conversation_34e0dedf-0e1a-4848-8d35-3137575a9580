package com.antiy.demo.fragment

import android.content.Intent
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.Toast
import com.antiy.avlsdk.AVLEngine
import com.antiy.avlsdk.storage.DBConstants.TABLE_CACHE
import com.antiy.avlsdk.storage.DataManager
import com.antiy.demo.R
import com.antiy.demo.activity.MainActivity
import com.antiy.demo.base.BaseFragment
import com.antiy.demo.databinding.FragmentHomeBinding
import com.google.android.material.bottomnavigation.BottomNavigationView

class HomeFragment : BaseFragment<FragmentHomeBinding>() {

    override fun getViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): FragmentHomeBinding {
        return FragmentHomeBinding.inflate(inflater, container, false)
    }


    override fun initView() {
        super.initView()
        // 这里初始化各个视图组件
        // 例如设置安全评分、快捷功能区域、最近活动等
        if (AVLEngine.getInstance()?.initResult?.isSuccess == true) {
            binding.tvSdkStatus.text = "成功"
        }else {
            binding.tvSdkStatus.text = "失败"
        }

    }

    override fun initListener() {
        super.initListener()
        binding.btnScan.setOnClickListener {
            // 获取BottomNavigationView
            val bottomNav = requireActivity().findViewById<BottomNavigationView>(R.id.bottomNavigation)
            // 切换到扫描页面
            bottomNav.selectedItemId = R.id.nav_scan
        }
        
        // 添加清除缓存按钮的点击事件
        binding.cardMore.setOnClickListener {
            DataManager.getInstance().clearAllData(TABLE_CACHE)
            // 显示清除成功的提示
            Toast.makeText(requireContext(), "缓存清除成功", Toast.LENGTH_SHORT).show()
        }

        binding.cardRealTimeProtection.setOnClickListener {
            // 跳转到实时旧版本
            startActivity(Intent(requireActivity(),MainActivity::class.java))
        }
    }

    fun updateEngineStatus(status: Boolean){
        binding.tvSdkStatus.text = if (status) "成功" else "失败"
    }
}