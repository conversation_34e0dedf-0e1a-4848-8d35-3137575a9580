package com.antiy.avlsdk.update;

import static com.antiy.avlsdk.utils.SdkConst.AVL_PC_BACKUP_SUFFIX;
import static com.antiy.avlsdk.utils.SdkConst.PC_VIRUS_UPDATE_CHECK_PATH;
import static com.antiy.avlsdk.utils.SdkConst.TARGET_ZIP_PATH;

import android.text.TextUtils;
import android.util.Pair;

import com.antiy.avlsdk.AVLCoreEngine;
import com.antiy.avlsdk.AVLEngine;
import com.antiy.avlsdk.pc.AVLEnginePC;
import com.antiy.avlsdk.utils.FileUtil;
import com.antiy.avlsdk.utils.SdkConst;

import java.io.File;

/**
 * Author: wangbiao
 * Date: 2024/10/11 17:07
 * Description:
 */
public abstract class UpdateStrategy {

    private static final String INSTALL_FULL = "total";
    private static final String INSTALL_INCREMENT = "tar";

    /**
     *
     * @param packagePath 安装包路径
     * @param pair  isTotal转换来的
     * @return
     */
    public boolean performUpdate(String packagePath, Pair<UpdateTypeEnum,UpdateTypeEnum> pair) {
        //如果是空，表示不需要更新 默认更新成功
        if (TextUtils.isEmpty(packagePath)) return true;
        int result = -1;

        switch (pair.first) {
            case FULL:
            case INCREMENTAL:
                File androidFile = new File(packagePath);
                if(!androidFile.exists()){
                    AVLEngine.Logger.error("android update file is not found");
                    return false;
                }
                String packageName = androidFile.getName();
                String version = AVLCoreEngine.getInstance().getSigLibVersion();
                AVLEngine.Logger.error("Android virus library version before update:" + version);
                AVLEngine.Logger.error("Updating Android virus library path:" + packagePath + ",name:" + packageName);

                result = AVLCoreEngine.getInstance().installPackage(packagePath, 1,
                        packageName.substring(0, packageName.lastIndexOf(".")),
                        (packageName.endsWith(SdkConst.ZIP_SUFFIX)) ? INSTALL_FULL : INSTALL_INCREMENT);
                AVLEngine.Logger.error("Update Android virus database results:" + result + "," + ((result == 0) ? "Success" : "Fail"));
                version = AVLCoreEngine.getInstance().getSigLibVersion();
                AVLEngine.Logger.error("Updated Android virus library version:" + version);
                break;

        }
        return result == 0;
    }


    /**
     * 删除备份文件和更新目录
     */
    public void cleanupTempFiles() {
        AVLEngine.Logger.info("delete backup file and update file");
        FileUtil.deleteDirectory(new File(AVLEngine.getInstance().getContext().getFilesDir() + TARGET_ZIP_PATH));
        FileUtil.deleteDirectory(new File(AVLEnginePC.getInstance().getAVLPCPath() + AVL_PC_BACKUP_SUFFIX));
    }

    /**
     * 回滚PC引擎
     */
    public void resetPcEngine() {
        AVLEngine.Logger.error("pc virus update fail,resetPcEngine");
        // 删除目前存在的avl_pc
        File pcBakDir = new File(AVLEnginePC.getInstance().getAVLPCPath() + AVL_PC_BACKUP_SUFFIX);
        File pcDir = new File(AVLEnginePC.getInstance().getAVLPCPath());
        // 这里需要判断备份的pc引擎是否存在 只有在存在的时候再去删除 否则会引起问题，当返回的数据不存在时，在AVLEngine.checkUpdate()中会调用此方法
        if (pcBakDir.exists()) {
            AVLEngine.Logger.error("pc virus resetPcEngine,delete path:" + pcDir);
            FileUtil.deleteDirectory(pcDir);
            pcBakDir.renameTo(pcDir);
        }
    }

    /**
     * 通过二进制文件检查pc的更新包
     * @return
     */
    public boolean pcVirusUpdateCheck() {
        File pcDir = new File(AVLEnginePC.getInstance().getAVLPCPath());
        String exePath = pcDir.getParent() + PC_VIRUS_UPDATE_CHECK_PATH;
        try {
            // 给文件加上可执行权限
            File exeFile = new File(exePath);
            if (!exeFile.canExecute()) {
                boolean result = exeFile.setExecutable(true);
                AVLEngine.Logger.info("Set executable permission result: " + result);
            }

            Process process = Runtime.getRuntime().exec(exePath,null,pcDir);
            process.waitFor();
            int value = process.exitValue();
            AVLEngine.Logger.info("pc virus update check result:" + value);
            return value == 0;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
}
