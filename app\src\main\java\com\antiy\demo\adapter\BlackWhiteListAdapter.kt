package com.antiy.demo.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.antiy.demo.databinding.ItemBlackWhiteListBinding
import com.antiy.demo.entity.BlackWhiteListItem

/**
 * 黑白名单RecyclerView适配器
 */
class BlackWhiteListAdapter(
    private val items: MutableList<BlackWhiteListItem>,
    private val onDeleteClick: (BlackWhiteListItem) -> Unit,
    private val onItemClick: (BlackWhiteListItem) -> Unit
) : RecyclerView.Adapter<BlackWhiteListAdapter.ViewHolder>() {

    inner class ViewHolder(private val binding: ItemBlackWhiteListBinding) : 
        RecyclerView.ViewHolder(binding.root) {
        
        fun bind(item: BlackWhiteListItem) {
            binding.apply {
                // 设置Hash值（显示简短版本）
                tvHash.text = item.getShortHash()
                
                // 设置完整Hash值作为副标题，添加点击提示
                tvFullHash.text = "${item.hash}\n点击查看详情并复制"
                
                // 设置类型标签
                tvType.text = item.getTypeText()
                tvType.setTextColor(ContextCompat.getColor(root.context, item.getTypeColorRes()))
                
                // 设置类型背景
                val backgroundRes = if (!item.isWhite) {
                    android.R.color.holo_green_light
                } else {
                    android.R.color.holo_red_light
                }
                chipType.setChipBackgroundColorResource(backgroundRes)
                chipType.text = item.getTypeText()
                
                // 设置点击事件
                root.setOnClickListener {
                    onItemClick(item)
                }
                
                // 设置删除按钮点击事件
                btnDelete.setOnClickListener {
                    onDeleteClick(item)
                }
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding = ItemBlackWhiteListBinding.inflate(
            LayoutInflater.from(parent.context), 
            parent, 
            false
        )
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(items[position])
    }

    override fun getItemCount(): Int = items.size

    /**
     * 更新数据
     */
    fun updateItems(newItems: List<BlackWhiteListItem>) {
        items.clear()
        items.addAll(newItems)
        notifyDataSetChanged()
    }

    /**
     * 添加项目
     */
    fun addItem(item: BlackWhiteListItem) {
        items.add(item)
        notifyItemInserted(items.size - 1)
    }

    /**
     * 删除项目
     */
    fun removeItem(position: Int) {
        if (position in 0 until items.size) {
            items.removeAt(position)
            notifyItemRemoved(position)
        }
    }

    /**
     * 根据hash删除项目
     */
    fun removeItemByHash(hash: String) {
        val position = items.indexOfFirst { it.hash == hash }
        if (position != -1) {
            removeItem(position)
        }
    }
}
