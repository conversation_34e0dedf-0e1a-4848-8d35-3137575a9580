package com.antiy.demo

import android.content.Context
import android.content.SharedPreferences
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.platform.app.InstrumentationRegistry
import com.antiy.avlsdk.AVLEngine
import com.antiy.avlsdk.auth.LicenseManager
import com.antiy.avlsdk.callback.RequestCallback
import com.antiy.avlsdk.entity.RequestMethod
import com.antiy.avlsdk.utils.SdkConst
import org.json.JSONObject
import org.junit.Assert.assertEquals
import org.junit.Assert.assertFalse
import org.junit.Assert.assertNotNull
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import java.util.UUID
import java.util.concurrent.CountDownLatch

/**
 * 授权模块的单元测试
 */
@RunWith(AndroidJUnit4::class)
class AuthTest {
    private lateinit var context: Context
    private lateinit var licenseManager: LicenseManager

    @Before
    fun setUp() {
        context = InstrumentationRegistry.getInstrumentation().targetContext
        licenseManager = LicenseManager(context)
        
        // 初始化 AVLEngine，因为 DeviceUuidManager 依赖它
        val uuid = UUID.randomUUID().toString()
        AVLEngine.init(context, uuid, ALog(context), NetworkManager())
    }

    @Test
    fun testDeviceUuidManagement() {
        // 1. 测试保存UUID
        val testUuid = UUID.randomUUID().toString()

        // 2. 测试获取UUID


    }

    @Test
    fun testLicenseManagerInitialization() {
        assertNotNull("LicenseManager 应该正确初始化", licenseManager)
    }

    @Test
    fun testAuthTokenRequest() {
        val latch = CountDownLatch(1)
        var authTokenReceived = false
        
        // 1. 清除现有token

        // 2. 请求新token
        licenseManager.requestAuthToken(UUID.randomUUID().toString())
        
        // 3. 验证token是否已保存
    }

    @Test
    fun testAuthRequestParameters() {
        // 测试认证请求参数的生成
        val testUuid = UUID.randomUUID().toString()
//        DeviceUuidManager.saveUUID(testUuid)
        
        val networkManager = object : NetworkManager() {
            override fun request(
                uri: String?,
                method: RequestMethod?,
                param: Any?,
                callback: RequestCallback?

            ) {
                // 验证请求参数
                assertTrue("应该使用AUTH_URL", uri == SdkConst.AUTH_URL)
                assertTrue("应该使用POST方法", method == RequestMethod.POST)
                
                val jsonParam = param as JSONObject
                assertNotNull("flag 参数不应为空", jsonParam.getString("flag"))
                assertNotNull("uuid 参数不应为空", jsonParam.getString("uuid"))
                assertNotNull("requestTime 参数不应为空", jsonParam.getString("requestTime"))
                assertNotNull("content 参数不应为空", jsonParam.getString("content"))
                
                // 验证flag的范围（0-31）
                val flag = jsonParam.getString("flag").toInt()
                assertTrue("flag应该在0-31范围内", flag in 0..31)
                
                // 模拟成功响应
                val response = JSONObject()
                response.put("code", 0)
                val data = JSONObject()
                data.put("content", "test_content")
                data.put("responseTime", "2024-01-01 00:00:00")
                data.put("flag", "1")
                response.put("data", data)
                
                callback?.onFinish(0, response.toString())
            }
        }
        
        // 使用测试用的NetworkManager初始化
        AVLEngine.init(context, testUuid, ALog(context), networkManager)
        
        // 请求认证令牌
        licenseManager.requestAuthToken(UUID.randomUUID().toString())
        
        // 验证认证令牌是否已保存
    }

    @Test
    fun testAuthFailureHandling() {
        // 测试网络错误情况
        val networkManager = object : NetworkManager() {
            override fun request(
                uri: String?,
                method: RequestMethod?,
                param: Any?,
                callback: RequestCallback?
            ) {
                callback?.onError("Network error")
            }
        }
        
        AVLEngine.init(context, UUID.randomUUID().toString(), ALog(context), networkManager)
        
        // 清除现有token

        // 请求认证令牌
        licenseManager.requestAuthToken(UUID.randomUUID().toString())
        
        // 验证失败情况下token应该为空
    }

    @Test
    fun testClientIdGeneration() {
        // 测试客户端ID的生成和保存
        licenseManager.requestAuthToken(UUID.randomUUID().toString())
    }
}