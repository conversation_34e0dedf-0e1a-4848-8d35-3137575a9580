package com.antiy.avlsdk.utils;

/**
 * 2 * Copyright (C), 2020-2024
 * 3 * FileName: JsonUtils
 * 4 * Author: wangbiao
 * 5 * Date: 2024/8/30 11:21
 * 6 * Description: json转换
 * 10
 */
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.lang.reflect.Type;
import java.util.List;

public class JsonUtils {

    // 私有构造函数，防止实例化
    private JsonUtils() {}

    /**
     * 将JSON字符串转换为Java对象
     *
     * @param jsonStr JSON字符串
     * @param clazz   目标Java类的Class对象
     * @return 转换后的Java对象
     */
    public static <T> T toObject(String jsonStr, Class<T> clazz) {
        Gson gson = new Gson();
        return gson.fromJson(jsonStr, clazz);
    }

    /**
     * 将Java对象转换为JSON字符串
     *
     * @param obj 要转换的Java对象
     * @return 转换后的JSON字符串
     */
    public static String toJson(Object obj) {
        Gson gson = new Gson();
        return gson.toJson(obj);
    }

    /*
     * @param jsonStr JSON字符串
     * @param type    List集合中元素的类型，使用TypeToken来指定泛型类型
     * @return 转换后的List集合
     */
    public static <T> List<T> toList(String jsonStr, Type type) {
        Gson gson = new Gson();
        return gson.fromJson(jsonStr, type);
    }

    // 将 JSON 字符串转换为 List 集合
    public static <T> List<T> jsonToList(String json, Class<T> clazz) {
        Gson gson = new Gson();
        Type type = TypeToken.getParameterized(List.class, clazz).getType();
        return gson.fromJson(json, type);
    }

    // 将 List 集合转换为 JSON 字符串
    public static <T> String listToJson(List<T> list) {
        Gson gson = new Gson();
        return gson.toJson(list);
    }
}
