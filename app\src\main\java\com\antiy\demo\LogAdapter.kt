package com.antiy.demo

import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView

/**
2 * Copyright (C), 2020-2024
3 * FileName: LogAdapter
4 * Author: wangbiao
5 * Date: 2024/9/2 16:59
6 * Description:
10 */
class LogAdapter(val dataSet: MutableList<String>) :
    RecyclerView.Adapter<LogAdapter.LogViewHolder>() {
    class LogViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val textView = view.findViewById<TextView>(R.id.textView)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): LogViewHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.item_list, parent, false)
        return LogViewHolder(view)
    }

    override fun getItemCount(): Int {
        return dataSet.size
    }

    override fun onBindViewHolder(holder: LogViewHolder, position: Int) {
        holder.textView.text = dataSet[position]
    }

     fun addData(str: String) {
         dataSet.add(str)
         notifyDataSetChanged()
     }
}