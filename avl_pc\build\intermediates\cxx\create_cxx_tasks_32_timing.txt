# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 53ms]
    create-module-model 11ms
    create-module-model
      create-cmake-model 10ms
    create-module-model completed in 16ms
    [gap of 93ms]
  create-initial-cxx-model completed in 192ms
create_cxx_tasks completed in 196ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 84ms
create_cxx_tasks completed in 85ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 108ms
create_cxx_tasks completed in 109ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 180ms
create_cxx_tasks completed in 183ms

