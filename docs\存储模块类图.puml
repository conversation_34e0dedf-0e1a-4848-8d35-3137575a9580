@startuml
title 杀毒引擎存储模块类图

class DataManager {
  -static DataManager instance
  -DatabaseHelper dbHelper
  -SecureStorage secureStorage
  -int historySize
  -long historyTimeout
  -int cloudCheckThreshold
  -long cloudSizeThreshold
  -int silentPerformanceThreshold
  --
  +static DataManager getInstance()
  +ResultScan getCachedResult(String path)
  +void saveCache(String path, ResultScan result)
  +ResultScan getScanHistory(String path)
  +void saveScanHistory(String path, ResultScan result)
  +void maintainDatabase()
  +void clearAllData(String tableName)
  +void setHistorySize(int size)
  +int getHistorySize()
  +void setHistoryTimeout(long timeout)
  +long getHistoryTimeout()
  +void setCloudCheckThreshold(int threshold)
  +int getCloudCheckThreshold()
  +void setCloudSizeThreshold(long threshold)
  +long getCloudSizeThreshold()
  +void setSilentPerformanceThreshold(int threshold)
  +int getSilentPerformanceThreshold()
  -void loadSettings()
}

class DatabaseHelper {
  -static final String DATABASE_NAME
  -static final int DATABASE_VERSION
  -SQLiteDatabase db
  --
  +DatabaseHelper(Context context)
  +void onCreate(SQLiteDatabase db)
  +void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion)
  +long saveCache(String path, String hash, String virus, boolean isMalicious)
  +ResultScan getCachedResult(String path)
  +long saveScanHistory(String path, String hash, String virus, boolean isMalicious)
  +ResultScan getScanHistory(String path)
  +void clearTable(String tableName)
  +void deleteExpiredHistory(long timeout)
  +int getHistoryCount()
  +void deleteOldestHistory(int count)
  +Cursor executeQuery(String sql, String[] args)
  +int executeUpdate(String sql, String[] args)
}

class SecureStorage {
  -static final String PREFERENCES_NAME
  -static final String KEY_AES_KEY
  -static final String KEY_AES_IV
  -Context context
  -SharedPreferences preferences
  -SecretKey aesKey
  -IvParameterSpec ivSpec
  --
  +SecureStorage(Context context)
  +void init()
  +void putString(String key, String value)
  +String getString(String key, String defValue)
  +void putBoolean(String key, boolean value)
  +boolean getBoolean(String key, boolean defValue)
  +void putInt(String key, int value)
  +int getInt(String key, int defValue)
  +void putLong(String key, long value)
  +long getLong(String key, long defValue)
  +void remove(String key)
  +void clear()
  -SecretKey generateAESKey()
  -IvParameterSpec generateIV()
  -SecretKey getAESKey()
  -IvParameterSpec getIV()
  -void saveKey(SecretKey key)
  -void saveIV(IvParameterSpec iv)
  -SecretKey loadKey()
  -IvParameterSpec loadIV()
}

class EncryptorHelper {
  +{static} String calcPathSHA256(String path)
  +{static} String generateUUID()
  +{static} String encrypt(String data, SecretKey key, IvParameterSpec iv)
  +{static} String decrypt(String encryptedData, SecretKey key, IvParameterSpec iv)
  +{static} String encodeBase64(byte[] data)
  +{static} byte[] decodeBase64(String data)
  +{static} SecretKey generateAESKey(byte[] seed)
  +{static} IvParameterSpec generateIV(byte[] seed)
  +{static} byte[] deriveKeyFromPassword(String password, byte[] salt)
  +{static} String md5(String input)
  +{static} String sha256(String input)
}

class DBConstants {
  +{static} final String TABLE_CACHE
  +{static} final String TABLE_HISTORY
  +{static} final String COLUMN_ID
  +{static} final String COLUMN_PATH
  +{static} final String COLUMN_HASH
  +{static} final String COLUMN_VIRUS
  +{static} final String COLUMN_MALICIOUS
  +{static} final String COLUMN_TIMESTAMP
}

class ResultScan {
  -boolean isMalicious
  -String virusName
  -String hash
  -String path
  --
  +boolean isMalicious()
  +void setMalicious(boolean isMalicious)
  +String getVirusName()
  +void setVirusName(String virusName)
  +String getHash()
  +void setHash(String hash)
  +String getPath()
  +void setPath(String path)
}

DataManager *-- DatabaseHelper : 包含
DataManager *-- SecureStorage : 包含
DatabaseHelper ..> DBConstants : 使用
SecureStorage ..> EncryptorHelper : 使用
DataManager ..> ResultScan : 使用
DatabaseHelper ..> ResultScan : 使用

@enduml