package com.antiy.avlsdk.utils;

import com.antiy.avlsdk.entity.ActionSuggestion;
import com.antiy.avlsdk.entity.BehaviorCode;
import com.antiy.avlsdk.entity.RiskLevel;
import com.antiy.avlsdk.entity.VirusCategory;
import com.antiy.avlsdk.entity.VirusInfo;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 病毒风险描述生成器
 * 用于解析病毒名称并生成标准化的病毒信息对象。该工具可以从病毒名中提取类别和行为特征，
 * 并返回包含病毒类别、行为特征列表、风险等级和执行建议的结果。
 */
public class VirusInfoUtil {
    /**
     * 解析病毒名称并返回病毒信息
     *
     * @param virusName 病毒名称，格式：类别/详细信息[行为特征代码1,行为特征代码2,...]
     * @return 病毒信息对象，永不返回null
     */
    public static VirusInfo parseVirusName(String virusName) {
        if (virusName == null || virusName.isEmpty()) {
            return new VirusInfo(null, new ArrayList<>(), RiskLevel.LOW, ActionSuggestion.USE_CAUTION);
        }

        // 提取病毒类别
        String categoryStr = extractCategory(virusName);
        VirusCategory category = VirusCategory.fromString(categoryStr);

        // 确定风险等级和执行建议
        RiskLevel riskLevel = getRiskLevelForCategory(category);
        // 执行建议
        ActionSuggestion suggestion = getSuggestionForCategory(category);

        // 提取行为特征
        List<BehaviorCode> behaviors = new ArrayList<>();
        Pattern pattern = Pattern.compile("\\[(.*?)\\]");
        Matcher matcher = pattern.matcher(virusName);

        if (matcher.find()) {
            String[] behaviorCodes = matcher.group(1).split(",");
            for (String code : behaviorCodes) {
                BehaviorCode behaviorCode = BehaviorCode.fromString(code.trim());
                if (behaviorCode != null) {
                    behaviors.add(behaviorCode);
                }
            }
        }

        return new VirusInfo(category, behaviors, riskLevel, suggestion);
    }

    /**
     * 从病毒名中提取类别
     */
    private static String extractCategory(String virusName) {
        int slashIndex = virusName.indexOf('/');
        return slashIndex > 0 ? virusName.substring(0, slashIndex) : "";
    }

    /**
     * 根据病毒类别确定风险等级
     */
    private static RiskLevel getRiskLevelForCategory(VirusCategory category) {
        if (category == null) {
            return RiskLevel.LOW;
        }

        switch (category) {
            case TROJAN:
            case WORM:
                return RiskLevel.HIGH;
            case G_WARE:
            case TOOL:
            case PORN_WARE:
                return RiskLevel.MEDIUM;
            default:
                return RiskLevel.LOW;
        }
    }

    /**
     * 根据风险等级确定执行建议
     */
    private static ActionSuggestion getSuggestionForCategory(VirusCategory category) {
        switch (category) {
            case TROJAN:
            case G_WARE:
            case PORN_WARE:
            case WORM:
                return ActionSuggestion.CLEAN_NOW;
            case TOOL:
            case RISK_WARE:
            case AD_WARE:
            case PAY_WARE:
            case WARN:
                return ActionSuggestion.USE_CAUTION;
        }
        return null;
    }
}


