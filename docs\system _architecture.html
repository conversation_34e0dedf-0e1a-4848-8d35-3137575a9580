<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>安天AVL SDK系统架构图</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .architecture {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .layer {
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            height: 100%;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
        }
        .layer-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
            color: #444;
        }
        .modules {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            padding: 5px;
        }
        .module {
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 10px;
            min-width: 150px;
            flex: 1;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .module-title {
            font-weight: bold;
            color: #333;
            margin: 0;
            padding: 0;
            border-bottom: none;
            text-align: center;
            width: 100%;
        }
        .components {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        .component {
            background-color: #fff;
            border: 1px solid #eee;
            border-radius: 4px;
            padding: 5px 8px;
            font-size: 14px;
        }
        .legend {
            margin-top: 30px;
            display: flex;
            justify-content: center;
            gap: 20px;
        }
        .legend-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 4px;
        }
        .api-layer {
            background-color: #e3f2fd;
        }
        .core-layer {
            background-color: #e8f5e9;
        }
        .engine-layer {
            background-color: #fff3e0;
        }
        .storage-layer {
            background-color: #f3e5f5;
        }
        .communication-layer {
            background-color: #e0f7fa;
        }
        .arrows {
            display: flex;
            justify-content: center;
            margin: 5px 0;
        }
        .arrow {
            width: 0; 
            height: 0; 
            border-left: 10px solid transparent;
            border-right: 10px solid transparent;
            border-top: 10px solid #aaa;
            margin: 0 auto;
        }
        .horizontal-layers {
            display: flex;
            gap: 15px;
            min-height: 200px;
        }
        .horizontal-layer {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        .horizontal-arrows {
            display: flex;
            justify-content: space-around;
            margin: 5px 0;
        }
        .horizontal-layers .modules {
            flex-direction: column;
            gap: 8px;
        }
        .horizontal-layers .module {
            width: calc(100% - 20px);
            min-width: unset;
            margin: 0;
            flex: 0 0 auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>安天AVL SDK系统架构图</h1>
        
        <div class="architecture">
            <!-- API层 -->
            <div class="layer api-layer">
                <div class="layer-title">API层 - 对外接口</div>
                <div class="modules">
                    <div class="module">
                        <div class="module-title">引擎接口</div>
                    </div>
                    <div class="module">
                        <div class="module-title">回调接口</div>
                    </div>
                </div>
            </div>
            
            <div class="arrows">
                <div class="arrow"></div>
            </div>
            
            <!-- 核心层 -->
            <div class="layer core-layer">
                <div class="layer-title">核心层 - 业务逻辑</div>
                <div class="modules">
                    <div class="module">
                        <div class="module-title">认证模块</div>
                    </div>
                    <div class="module">
                        <div class="module-title">扫描模块</div>
                    </div>
                    <div class="module">
                        <div class="module-title">更新模块</div>
                    </div>
                    <div class="module">
                        <div class="module-title">配置模块</div>
                    </div>
                    <div class="module">
                        <div class="module-title">性能监控模块</div>
                    </div>
                </div>
            </div>
            
            <div class="horizontal-arrows">
                <div class="arrow"></div>
                <div class="arrow"></div>
                <div class="arrow"></div>
            </div>
            
            <!-- 水平排列的三个层 -->
            <div class="horizontal-layers">
                <!-- 引擎层 -->
                <div class="horizontal-layer">
                    <div class="layer engine-layer">
                        <div class="layer-title">引擎层 - 杀毒引擎</div>
                        <div class="modules">
                            <div class="module">
                                <div class="module-title">移动端引擎</div>
                            </div>
                            <div class="module">
                                <div class="module-title">PC端引擎</div>
                            </div>
                            <div class="module">
                                <div class="module-title">引擎抽象</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 存储层 -->
                <div class="horizontal-layer">
                    <div class="layer storage-layer">
                        <div class="layer-title">存储层 - 数据持久化</div>
                        <div class="modules">
                            <div class="module">
                                <div class="module-title">数据管理</div>
                            </div>
                            <div class="module">
                                <div class="module-title">数据库</div>
                            </div>
                            <div class="module">
                                <div class="module-title">安全</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 通信层 -->
                <div class="horizontal-layer">
                    <div class="layer communication-layer">
                        <div class="layer-title">通信层 - 网络与系统交互</div>
                        <div class="modules">
                            <div class="module">
                                <div class="module-title">网络通信</div>
                            </div>
                            <div class="module">
                                <div class="module-title">进程通信</div>
                            </div>
                            <div class="module">
                                <div class="module-title">系统接口</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="legend">
            <div class="legend-item">
                <div class="legend-color" style="background-color: #e3f2fd;"></div>
                <div>API层</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #e8f5e9;"></div>
                <div>核心层</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #fff3e0;"></div>
                <div>引擎层</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #f3e5f5;"></div>
                <div>存储层</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #e0f7fa;"></div>
                <div>通信层</div>
            </div>
        </div>
    </div>
</body>
</html>
