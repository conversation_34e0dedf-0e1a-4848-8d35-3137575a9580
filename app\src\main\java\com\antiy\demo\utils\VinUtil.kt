package com.antiy.demo.utils

import java.nio.charset.StandardCharsets
import java.security.MessageDigest
import java.util.UUID

object VinUtil {
    fun vinToUuidHash(vin: String): String {
        require(vin.length == 17) { "VIN must be 17 characters." }

        val hashBytes = MessageDigest.getInstance("SHA-256").digest(vin.toByteArray(StandardCharsets.UTF_8))
        val uuidBytes = hashBytes.copyOf(16) // 取前 16 字节
        return UUID.nameUUIDFromBytes(uuidBytes).toString()
    }

    fun vinToUuid(vin: String?): String {
        require(!(vin == null || vin.length != 17)) { "VIN must be 17 characters." }
        
        // 使用前8位和后9位，中间补0
        val part1 = vin?.substring(0, 8)
        val part2 = "0000"
        val part3 = "4000"  // 设置版本位为4，表示这是自定义UUID
        val part4 = "8000"  // 设置变体位为10，表示这是标准UUID
        val part5 = vin.substring(8, 17) + "000"
        
        return "$part1-$part2-$part3-$part4-$part5"
    }
}