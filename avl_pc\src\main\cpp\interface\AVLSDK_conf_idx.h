#ifndef	__AVLSDK_CONF_IDX_H__
#define	__AVLSDK_CONF_IDX_H__

#define CFG_ITEM_MAX_LIMIT                                  (128)
#define CFG_ITEMH_MAX_LIMIT                                 (128)

#define CFG_STR_MODULE_PATH                                 (0)
#define CFG_STR_DATA_PATH                                   (1)
#define	CFG_STR_TMP_PATH                                    (2)
#define CFG_STR_LOG_PATH                                    (3)

#define	CFG_FLAG_SHELL_RECG_LOAD                            (4)
#define CFG_FLAG_SFX_DETECT_LOAD                            (5)
#define CFG_FLAG_INFECT_DETECT_LOAD                         (6)
#define CFG_FLAG_COMM_DETECT_LOAD                           (7)
#define	CFG_FLAG_SEC_DETECT_LOAD                            (8)
#define CFG_FLAG_PEP_DETECT_LOAD                            (9)
#define CFG_FLAG_HD_DETECT_LOAD                             (10)
#define CFG_FLAG_STREAM_DETECT_LOAD                         (11)
#define CFG_FLAG_DC_COLLECT_LOAD                            (12)
#define CFG_FLAG_EXPLOIT_DETECT_LOAD                        (13)
#define CFG_FLAG_SCRIPT_DETECT_LOAD                         (14)
#define CFG_FLAG_VCS2S_DETECT_LOAD                          (15)
#define	CFG_FLAG_PATH_FILTER_LOAD                           (16)
#define CFG_FLAG_UNITARINESS_LOAD                           (17)
#define CFG_FLAG_SPLIT_SCRIPT_LOAD                          (18)
#define	CFG_FLAG_SPLIT_PE_LOAD                              (19)
#define CFG_FLAG_APACK_LOAD                                 (20)
#define CFG_FLAG_STATIC_UPK_LOAD                            (21)
#define CFG_FLAG_AEML_LOAD                                  (22)

#define CFG_FLAG_LOG_ENABLE                                 (23)
#define	CFG_FLAG_ALL_MOD_ANALYSE                            (24)
#define CFG_INT_MAX_SCAN_LEVEL_LIMIT                        (25)
#define CFG_INT_MAX_FILE_SIZE_LIMIT                         (26)

#define	CFG_FLAG_SHELL_RECG_ENABLE                          (27)

#define CFG_FLAG_SFX_DETECT_ENABLE                          (28)

#define CFG_FLAG_INFECT_DETECT_ENABLE                       (29)

#define CFG_FLAG_COMM_DETECT_ENABLE                         (30)

#define	CFG_FLAG_SEC_DETECT_ENABLE                          (31)

#define CFG_FLAG_PEP_DETECT_ENABLE                          (32)

#define CFG_FLAG_HD_DETECT_ENABLE                           (33)

#define CFG_FLAG_STREAM_DETECT_ENABLE                       (34)

#define CFG_FLAG_DC_COLLECT_ENABLE                          (35)

#define CFG_FLAG_EXPLOIT_DETECT_ENABLE                      (36)

#define CFG_FLAG_SCRIPT_DETECT_ENABLE                       (37)
#define CFG_INT_SCAN_SCRIPT_MAX_SIZE                        (38)

#define	CFG_FLAG_PATH_FILTER_ENABLE                         (39)

#define CFG_FLAG_UNITARINESS_ENABLE                         (40)
#define CFG_INT_UNITARINESS_EOP_MAX_DASM_COUNT              (41)
#define CFG_INT_UNITARINESS_EOP_RECURE_LAYER                (42)

#define CFG_FLAG_SPLIT_SCRIPT_ENABLE                        (43)

#define	CFG_FLAG_SPLIT_PE_ENABLE                            (44)
#define	CFG_FLAG_SPLIT_PE_OVERLAY_ENABLE                    (45)
#define	CFG_FLAG_SPLIT_PE_RES_ENABLE                        (46)
#define	CFG_INT_SPLIT_PE_RES_MIN_ITEM_SIZE                  (47)
#define	CFG_INT_SPLIT_PE_OVERLAY_MAX_SEARCH                 (48)
#define	CFG_INT_SPLIT_PE_OVERLAY_MIN_SIZE                   (49)

#define	CFG_FLAG_APACK_ENABLE                               (50)
#define CFG_INT_APACK_MAX_ARCHIVE_SIZE                      (51)
#define CFG_INT_APACK_MAX_FILE_NUM                          (52)
#define CFG_INT_APACK_MAX_FILE_SIZE                         (53)
#define CFG_INT_APACK_RECURE_LAYER                          (54)
#define CFG_INT_APACK_MAX_COMPR_SCALE                       (55)
#define CFG_INT_APACK_TIMEOUT                               (56)
#define CFG_FLAG_APACK_RPTBOMB                              (57)
#define CFG_INT_APACK_USE_MAX_MEMORY_SIZE                   (58)

#define CFG_FLAG_STATIC_UPK_ENABLE                          (59)

#define	CFG_FLAG_AEML_ENABLE                                (60)

#define	CFG_FLAG_VCS2S_ENABLE                               (61)
#define CFG_INT_VCS2S_LEVEL                                 (62)
#define	CFG_INT_VCS2S_CHECKMODE                             (63)

#define	CFG_FLAG_FBLOOM_DETECT_LOAD                         (64)
#define	CFG_FLAG_FBLOOM_DETECT_ENABLE                       (65)

#define	CFG_FLAG_KEXPLOIT_DETECT_LOAD                       (66)
#define	CFG_FLAG_KEXPLOIT_DETECT_ENABLE                     (67)

#define	CFG_FLAG_SUF_LOAD                                   (68)
#define	CFG_FLAG_SUF_ENABLE                                 (69)

#define CFG_FLAG_SUFB_LOAD                                  (70)
#define CFG_FLAG_SUFB_ENABLE                                (71)

#define	CFG_FLAG_VM_DETECT_ENABLE                           (72)

#define CFG_FLAG_CLOUD_DETECT_LOAD                          (73)
#define	CFG_FLAG_CLOUD_DETECT_ENABLE                        (74)
#define CFG_STR_CLOUD_HOST                                  (75)
#define CFG_INT_CLOUD_CONNECT_TIMEOUT                       (76)
#define CFG_INT_CLOUD_DETECT_TIMEOUT                        (77)

#define CFG_FLAG_BOL_DETECT_LOAD                            (78)
#define CFG_FLAG_BOL_DETECT_ENABLE                          (79)

#define CFG_FLAG_TXT_INFECT_DETECT_LOAD                     (80)
#define CFG_FLAG_TXT_INFECT_DETECT_ENABLE                   (81)

#define CFG_FLAG_ELF_DETECT_LOAD                            (82)
#define CFG_FLAG_ELF_DETECT_ENABLE                          (83)

#define CFG_FLAG_CLOUD_WAIT_BLOCK                           (84)
#define CFG_INT_CLOUD_MAX_QUEUE_SIZE                        (85)
#define CFG_FLAG_CLOUD_DETECT_DERIVE_DATA                   (86)

#define CFG_STR_LICENSE_PATH                                (87)

#define CFG_FLAG_YARA_DETECT_LOAD                           (88)
#define CFG_FLAG_YARA_DETECT_ENABLE                         (89)

#define CFG_FLAG_MACRO_DETECT_LOAD                          (90)
#define CFG_FLAG_MACRO_DETECT_ENABLE                        (91)

#define CFG_FLAG_ANDROID_DETECT_LOAD                        (92)
#define CFG_FLAG_ANDROID_DETECT_ENABLE                      (93)

#define CFG_FLAG_GEN_HASH_DETECT_LOAD                       (94)
#define CFG_FLAG_GEN_HASH_DETECT_ENABLE                     (95)

#define CFG_INT_SPLIT_SCRIPT_MAX_ITEMS_COUNT                (96)
#define CFG_INT_SPLIT_SCRIPT_MIN_SCRIPT_SIZE                (97)

#define CFG_FLAG_VCS3S_DETECT_LOAD                          (98)
#define	CFG_FLAG_VCS3S_ENABLE                               (99)

#define CFG_FLAG_NSIS_DETECT_LOAD                           (100)
#define CFG_FLAG_NSIS_DETECT_ENABLE                         (101)

#define CFG_FLAG_SWF_DETECT_LOAD                            (102)
#define CFG_FLAG_SWF_DETECT_ENABLE                          (103)

#define CFG_FLAG_SPLIT_SWF_LOAD                             (104)
#define CFG_FLAG_SPLIT_SWF_ENABLE                           (105)

#define CFG_FLAG_HEML_DETECT_LOAD                           (106)
#define CFG_FLAG_HEML_DETECT_ENABLE                         (107)

#define CFG_FLAG_REG_DETECT_LOAD                            (108)
#define CFG_FLAG_REG_DETECT_ENABLE                          (109)

#define CFG_FLAG_GSCPT_DETECT_LOAD                          (110)
#define CFG_FLAG_GSCPT_DETECT_ENABLE                        (111)

#define CFG_FLAG_ELF_SFX_DETECT_LOAD                        (112)
#define CFG_FLAG_ELF_SFX_DETECT_ENABLE                      (113)

#define CFG_FLAG_DOH_DETECT_LOAD                            (114)
#define CFG_FLAG_DOH_DETECT_ENABLE                          (115)

#define CFG_FLAG_MACRO_DETECT_HEUR_ENABLE                   (116)

#define CFG_FLAG_HEUR_FILE_FMT_LOAD                         (117)
#define CFG_FLAG_HEUR_FILE_FMT_ENABLE                       (118)
#define CFG_INT_HEUR_FILE_FMT_TXT_MAX_SIZE                  (119)

#define CFG_FLAG_VH_DETECT_LOAD                             (120)
#define CFG_FLAG_VH_DETECT_ENABLE                           (121)

#define CFG_FLAG_VC_DETECT_LOAD                             (122)
#define CFG_FLAG_VC_DETECT_ENABLE                           (123)

#define	CFG_FLAG_SPLIT_PE_DPER_DLER_ENABLE                  (124)
#define	CFG_FLAG_SPLIT_PE_SCRIPT2EXE_ENABLE                 (125)

#define CFG_FLAG_TXT_UNITARINESS_LOAD                       (126)
#define CFG_FLAG_TXT_UNITARINESS_ENABLE                     (127)
#define CFG_FLAG_TXT_UNITARINESS_UTF16_UTF8_ENABLE          (256)
#define CFG_FLAG_TXT_UNITARINESS_SCRIPT_SPLIT_ENABLE        (257)
#define CFG_FLAG_TXT_UNITARINESS_JS_UNPACK_ENABLE           (258)
#define CFG_FLAG_TXT_UNITARINESS_MAX_SIZE_LIMIT             (259)

#define CFG_INT_TXT_INFECT_DETECT_MAX_SIZE_LIMIT            (260)
#define CFG_FLAG_TXT_INFECT_DETECT_FULL_SCAN                (261)

#define CFG_FLAG_ANDROID_EX_DETECT_LOAD                     (262)
#define CFG_FLAG_ANDROID_EX_DETECT_ENABLE                   (263)

#define CFG_INT_AEML_MAX_FILE_NUM                           (264)
#define CFG_INT_AEML_MAX_FILE_SIZE                          (265)
#define CFG_INT_AEML_MIN_FILE_SIZE                          (266)

#define CFG_INT_SPLIT_PE_RECURE_LAYER                       (267)

#define CFG_FLAG_IGNORE_DUPLICATE_OBJ                       (268)

#define CFG_FLAG_IMP_DETECT_LOAD                            (269)
#define CFG_FLAG_IMP_DETECT_ENABLE                          (270)

#define CFG_FLAG_HMACRO_DETECT_LOAD                         (271)
#define CFG_FLAG_HMACRO_DETECT_ENABLE                       (272)

#define CFG_STR_DEVICE_LICENSE_PATH                         (273)					

#define CFG_FLAG_DC_DETECT_LOAD                             (274)
#define CFG_FLAG_DC_DETECT_ENABLE                           (275)

#define CFG_FLAG_FINFO_DETECT_LOAD                          (276)
#define CFG_FLAG_FINFO_DETECT_ENABLE                        (277)




#endif // __AVLSDK_CONF_IDX_H__


