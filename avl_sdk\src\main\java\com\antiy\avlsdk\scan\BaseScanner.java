package com.antiy.avlsdk.scan;

import com.antiy.avlsdk.callback.IScanner;
import com.antiy.avlsdk.callback.ScanListener;

import java.io.File;
import java.util.List;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

public abstract class BaseScanner implements IScanner {
    protected static final int BATCH_SIZE = 10;
    protected static final int CORE_POOL_SIZE = 4;
    protected static final int MAX_POOL_SIZE = 8;
    protected static final long KEEP_ALIVE_TIME = 60L;

    protected final List<File> files;
    protected final ScanListener callback;
    protected final BlockingQueue<ScanTask> taskQueue;
    protected final AtomicBoolean isPaused;
    protected final AtomicBoolean isStopped;
    protected final AtomicInteger currentIndex;

    protected BaseScanner(List<File> files, ScanListener callback) {
        this.files = files;
        this.callback = callback;
        this.taskQueue = new LinkedBlockingQueue<>();
        this.isPaused = new AtomicBoolean(false);
        this.isStopped = new AtomicBoolean(false);
        this.currentIndex = new AtomicInteger(0);
    }

    @Override
    public void pauseScan() {
        isPaused.set(true);
    }

    @Override
    public void resumeScan() {
        isPaused.set(false);
    }

    @Override
    public void stopScan() {
        isStopped.set(true);
    }

    @Override
    public boolean isPaused() {
        return isPaused.get();
    }

    @Override
    public boolean isStopped() {
        return isStopped.get();
    }

    protected void handlePause() {
        if (isPaused.get()) {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }

    protected static class ScanTask {
        public final int index;
        public final File file;

        public ScanTask(int index, File file) {
            this.index = index;
            this.file = file;
        }

        @Override
        public String toString() {
            return "ScanTask{" +
                    "index=" + index +
                    ", file=" + file +
                    '}';
        }
    }
}
