package com.antiy.avlsdk.utils;

import java.util.concurrent.atomic.AtomicInteger;

/**
 * Author: wang<PERSON>o
 * Date: 2024/9/14 16:47
 * Description: 文件计数器
 */
public class FileCountHelper {
    private static FileCountHelper instance;
    private AtomicInteger currentIndex;
    private int totalRequest;
    private FileCountHelper() {
        currentIndex = new AtomicInteger(0);
    }

    public static FileCountHelper getInstance(){
        if(instance == null){
            synchronized (FileCountHelper.class){
                if (instance == null){
                    instance = new FileCountHelper();
                }
            }
        }
        return instance;
    }

    public void setTotalRequest(int count){
        this.totalRequest = count;
    }

    public int getTotalRequest() {
        return totalRequest;
    }

    public void increment(){
        currentIndex.incrementAndGet();
    }

    public int getCurrentIndex(){
        return currentIndex.get();
    }

    public void reset(){
        this.totalRequest = 0;
        this.currentIndex.set(0);
    }

    // 新增方法，用于检查是否所有文件都已处理
    public boolean isAllFilesProcessed() {
        return getCurrentIndex() >= getTotalRequest();
    }
}
