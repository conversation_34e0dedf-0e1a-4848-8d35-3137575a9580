@startuml
title 杀毒引擎存储模块时序图

actor Client
participant "DataManager" as DM
participant "SecureStorage" as SS
participant "DatabaseHelper" as DB
participant "EncryptorHelper" as EH
database "SQLite数据库" as SQLite
participant "SharedPreferences" as SP

== 初始化流程 ==

Client -> DM: getInstance()
activate DM

DM -> DM: 检查实例是否存在
DM -> SS: 初始化SecureStorage
activate SS
SS -> SS: 准备密钥
deactivate SS

DM -> DB: 创建DatabaseHelper实例
activate DB
DB -> SQLite: 打开或创建数据库
SQLite --> DB: 数据库连接

alt 首次创建数据库
    DB -> SQLite: 创建缓存表(T_CACHE)
    DB -> SQLite: 创建历史表(T_HISTORY)
end

DB --> DM: 数据库初始化完成
deactivate DB

DM --> Client: 返回DataManager实例
deactivate DM

== 数据存储流程 ==

Client -> DM: 存储数据(key, value)
activate DM

alt 存储配置或授权信息
    DM -> SS: putString(key, value)
    activate SS
    SS -> EH: encrypt(value)
    activate EH
    EH --> SS: 加密后的数据
    deactivate EH
    SS -> SP: 存储加密数据
    SS --> DM: 存储成功
    deactivate SS

else 存储扫描历史
    DM -> DM: prepareScanHistory(path, result)
    DM -> DB: saveScanHistory(path, hash, result)
    activate DB
    DB -> SQLite: INSERT INTO T_HISTORY
    SQLite --> DB: 插入结果
    DB --> DM: 存储成功
    deactivate DB

else 存储缓存结果
    DM -> DM: prepareCacheData(path, result)
    DM -> DB: saveCache(path, hash, result)
    activate DB
    DB -> SQLite: INSERT INTO T_CACHE
    SQLite --> DB: 插入结果
    DB --> DM: 存储成功
    deactivate DB
end

DM --> Client: 返回存储结果
deactivate DM

== 数据读取流程 ==

Client -> DM: 读取数据(key/path)
activate DM

alt 读取配置或授权信息
    DM -> SS: getString(key)
    activate SS
    SS -> SP: 读取加密数据
    SP --> SS: 返回加密数据
    SS -> EH: decrypt(encryptedData)
    activate EH
    EH --> SS: 解密后的数据
    deactivate EH
    SS --> DM: 返回解密数据
    deactivate SS

else 查询缓存结果
    DM -> DB: getCachedResult(path)
    activate DB
    DB -> SQLite: SELECT FROM T_CACHE WHERE PATH=?
    SQLite --> DB: 查询结果
    DB --> DM: 返回缓存数据
    deactivate DB

else 查询历史记录
    DM -> DB: getScanHistory(path)
    activate DB
    DB -> SQLite: SELECT FROM T_HISTORY WHERE PATH=?
    SQLite --> DB: 查询结果
    DB --> DM: 返回历史数据
    deactivate DB
end

DM --> Client: 返回读取结果
deactivate DM

== 数据维护流程 ==

Client -> DM: maintainDatabase()
activate DM

DM -> DM: 获取历史记录大小限制
DM -> DM: 获取历史记录超时时间

DM -> DB: 清理过期记录
activate DB
DB -> SQLite: DELETE FROM T_HISTORY WHERE TIMESTAMP<?
SQLite --> DB: 删除结果
DB --> DM: 清理完成
deactivate DB

DM -> DB: 获取历史记录数量
activate DB
DB -> SQLite: SELECT COUNT(*) FROM T_HISTORY
SQLite --> DB: 记录数量
DB --> DM: 返回记录数量
deactivate DB

alt 历史记录超出限制
    DM -> DB: 删除最旧的记录
    activate DB
    DB -> SQLite: DELETE FROM T_HISTORY ORDER BY TIMESTAMP LIMIT ?
    SQLite --> DB: 删除结果
    DB --> DM: 清理完成
    deactivate DB
end

DM --> Client: 维护完成
deactivate DM

@enduml